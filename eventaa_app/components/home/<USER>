import React from 'react';
import { StyleSheet, TextInput, TouchableOpacity, View } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { ThemedText } from '@/components/ThemedText';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Fonts } from '@/constants/Fonts';

type SearchBarProps = {
  placeholder?: string;
  value: string;
  onChangeText: (text: string) => void;
  onFilterPress?: () => void;
};

export function SearchBar({
  placeholder = 'Search...',
  value,
  onChangeText,
  onFilterPress
}: SearchBarProps) {
  const colorScheme = useColorScheme() ?? 'light';
  const headerTextColor = Colors[colorScheme].headerText;

  return (
    <View style={styles.container}>
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={24} color={headerTextColor} style={styles.searchIcon} />
        <TextInput
          style={[styles.input, { color: headerTextColor, fontFamily: Fonts.regular.fontFamily }]}
          placeholder={placeholder}
          placeholderTextColor="rgba(255, 255, 255, 0.7)"
          value={value}
          onChangeText={onChangeText}
        />
      </View>

      <TouchableOpacity
        style={styles.filterButton}
        onPress={onFilterPress}
        activeOpacity={0.7}
      >
        <ThemedText style={[styles.filterText, { color: headerTextColor }]}>
          Filters
        </ThemedText>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 12,
    alignItems: 'center',
    gap: 12,
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 0,
  },
  searchIcon: {
    marginRight: 0,
  },
  input: {
    flex: 1,
    height: 40,
    fontSize: 18,
    color: '#fff',
    fontWeight: '300',
    marginLeft: 8,
  },
  filterButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 50,
    paddingHorizontal: 16,
    paddingVertical: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  filterText: {
    fontSize: 16,
  },
});
