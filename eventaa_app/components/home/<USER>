import React from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';

import { ThemedText } from '@/components/ThemedText';

type InviteFriendsCardProps = {
  onInvitePress?: () => void;
};

export function InviteFriendsCard({ onInvitePress }: InviteFriendsCardProps) {
  return (
    <View style={styles.container}>
      <View style={styles.contentContainer}>
        <ThemedText style={styles.title}>Invite your friends</ThemedText>
        <ThemedText style={styles.subtitle}>Get $20 for ticket</ThemedText>
        
        <TouchableOpacity
          style={styles.inviteButton}
          onPress={onInvitePress}
          activeOpacity={0.8}
        >
          <ThemedText style={styles.inviteButtonText}>INVITE</ThemedText>
        </TouchableOpacity>
      </View>
      
      <View style={styles.decorationContainer}>
        <View style={styles.giftBox} />
        <View style={styles.confetti1} />
        <View style={styles.confetti2} />
        <View style={styles.confetti3} />
        <View style={styles.hand1} />
        <View style={styles.hand2} />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#e0f7fa',
    borderRadius: 8,
    padding: 16,
    marginHorizontal: 16,
    marginVertical: 16,
    flexDirection: 'row',
    position: 'relative',
    overflow: 'hidden',
  },
  contentContainer: {
    flex: 1,
    zIndex: 1,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#0d47a1',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: '#0d47a1',
    marginBottom: 16,
  },
  inviteButton: {
    backgroundColor: '#00e5ff',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 4,
    alignSelf: 'flex-start',
  },
  inviteButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 14,
  },
  decorationContainer: {
    position: 'absolute',
    right: 0,
    top: 0,
    bottom: 0,
    width: 120,
  },
  giftBox: {
    position: 'absolute',
    width: 60,
    height: 60,
    backgroundColor: '#ffeb3b',
    right: 20,
    top: 30,
    borderRadius: 4,
  },
  confetti1: {
    position: 'absolute',
    width: 10,
    height: 10,
    backgroundColor: '#f44336',
    right: 40,
    top: 20,
    borderRadius: 2,
    transform: [{ rotate: '45deg' }],
  },
  confetti2: {
    position: 'absolute',
    width: 8,
    height: 8,
    backgroundColor: '#4caf50',
    right: 60,
    top: 15,
    borderRadius: 2,
    transform: [{ rotate: '30deg' }],
  },
  confetti3: {
    position: 'absolute',
    width: 12,
    height: 12,
    backgroundColor: '#2196f3',
    right: 20,
    top: 10,
    borderRadius: 2,
    transform: [{ rotate: '15deg' }],
  },
  hand1: {
    position: 'absolute',
    width: 30,
    height: 20,
    backgroundColor: '#ffccbc',
    right: 10,
    bottom: 20,
    borderRadius: 10,
  },
  hand2: {
    position: 'absolute',
    width: 30,
    height: 20,
    backgroundColor: '#ffccbc',
    right: 50,
    bottom: 30,
    borderRadius: 10,
    transform: [{ rotate: '30deg' }],
  },
});
