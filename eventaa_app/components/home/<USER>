import React from 'react';
import { Image, StyleSheet, TouchableOpacity, View } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';

import { ThemedText } from '@/components/ThemedText';

type EventCardProps = {
  id: string;
  title: string;
  date: string;
  month: string;
  location: string;
  image: any;
  isFavorite?: boolean;
  onPress?: () => void;
  onFavoritePress?: () => void;
};

export function EventCard({
  title,
  date,
  month,
  location,
  image,
  isFavorite = false,
  onPress,
  onFavoritePress,
}: EventCardProps) {
  return (
    <TouchableOpacity
      style={styles.container}
      onPress={onPress}
      activeOpacity={0.9}
    >
      <View style={styles.imageContainer}>
        <Image source={image} style={styles.image} />
        
        <View style={styles.dateContainer}>
          <View style={styles.dateDay}>
            <ThemedText style={styles.dateDayText}>{date}</ThemedText>
          </View>
          <View style={styles.dateMonth}>
            <ThemedText style={styles.dateMonthText}>{month}</ThemedText>
          </View>
        </View>
        
        <TouchableOpacity
          style={styles.favoriteButton}
          onPress={onFavoritePress}
          activeOpacity={0.8}
        >
          <MaterialIcons
            name={isFavorite ? 'bookmark' : 'bookmark-outline'}
            size={24}
            color="#ef4444"
          />
        </TouchableOpacity>
      </View>
      
      <View style={styles.contentContainer}>
        <ThemedText style={styles.title} numberOfLines={2}>{title}</ThemedText>
        <View style={styles.locationContainer}>
          <MaterialIcons name="location-on" size={16} color="#6b7280" />
          <ThemedText style={styles.locationText}>{location}</ThemedText>
        </View>
        <View style={styles.attendeesContainer}>
          <ThemedText style={styles.attendeesText}>+20 Going</ThemedText>
        </View>
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 8,
    overflow: 'hidden',
    width: 280,
    marginRight: 16,
  },
  imageContainer: {
    position: 'relative',
    height: 150,
  },
  image: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  dateContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
  },
  dateDay: {
    backgroundColor: '#ef4444',
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  dateDayText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 18,
  },
  dateMonth: {
    backgroundColor: '#b91c1c',
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  dateMonthText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 14,
    textTransform: 'uppercase',
  },
  favoriteButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: '#fff',
    borderRadius: 50,
    padding: 4,
  },
  contentContainer: {
    padding: 12,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  locationText: {
    fontSize: 14,
    color: '#6b7280',
    marginLeft: 4,
  },
  attendeesContainer: {
    marginTop: 4,
  },
  attendeesText: {
    fontSize: 14,
    color: '#6b7280',
  },
});
