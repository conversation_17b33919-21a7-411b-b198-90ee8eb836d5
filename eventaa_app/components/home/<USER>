import React from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { ThemedText } from '@/components/ThemedText';
import { Colors } from '@/constants/Colors';

type CategoryButtonProps = {
  title: string;
  icon: 'sports' | 'music' | 'food' | string;
  color: string;
  onPress?: () => void;
};

export function CategoryButton({ title, icon, color, onPress }: CategoryButtonProps) {
  const renderIcon = () => {
    const iconColor = "#fff";

    switch (icon) {
      case 'sports':
        return <Ionicons name="basketball" size={20} color={iconColor} />;
      case 'music':
        return <Ionicons name="musical-notes" size={20} color={iconColor} />;
      case 'food':
        return <Ionicons name="restaurant" size={20} color={iconColor} />;
      case 'more':
        return <Ionicons name="grid" size={20} color={iconColor} />;
      default:
        return <Ionicons name="apps" size={20} color={iconColor} />;
    }
  };

  return (
    <TouchableOpacity
      style={[styles.container, { backgroundColor: color }]}
      onPress={onPress}
      activeOpacity={0.8}
    >
      {renderIcon()}
      <ThemedText style={[styles.title, { color: '#fff' }]}>{title}</ThemedText>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 50,
    gap: 10,
  },
  title: {
    fontSize: 16,
  },
});
