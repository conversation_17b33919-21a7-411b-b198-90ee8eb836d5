import React from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { ThemedText } from '@/components/ThemedText';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

type LocationHeaderProps = {
  location: string;
  onLocationPress?: () => void;
  onNotificationPress?: () => void;
};

export function LocationHeader({
  location,
  onLocationPress,
  onNotificationPress
}: LocationHeaderProps) {
  const colorScheme = useColorScheme() ?? 'light';
  const headerTextColor = Colors[colorScheme].headerText;

  return (
    <View style={styles.container}>
      <TouchableOpacity style={styles.menuButton} activeOpacity={0.7}>
        <View style={styles.menuLine} />
        <View style={styles.menuLine} />
        <View style={styles.menuLine} />
      </TouchableOpacity>

      <View style={styles.locationContainer}>
        <TouchableOpacity
          onPress={onLocationPress}
          activeOpacity={0.7}
          style={styles.locationWrapper}
        >
          <ThemedText
            style={[styles.locationLabel, { color: headerTextColor }]}
            weight="light"
          >
            Current Location
          </ThemedText>
          <View style={styles.locationRow}>
            <ThemedText
              style={[styles.locationText, { color: headerTextColor }]}
              weight="bold"
            >
              {location}
            </ThemedText>
            <Ionicons name="chevron-down" size={20} color={headerTextColor} />
          </View>
        </TouchableOpacity>
      </View>

      <TouchableOpacity
        style={styles.notificationButton}
        onPress={onNotificationPress}
        activeOpacity={0.7}
      >
        <Ionicons name="notifications-outline" size={24} color={headerTextColor} />
        <View style={styles.notificationDot} />
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  menuButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'flex-start',
    marginRight: 8,
  },
  menuLine: {
    width: 24,
    height: 2,
    backgroundColor: Colors.light.headerText,
    marginVertical: 3,
    borderRadius: 1,
  },
  locationContainer: {
    flex: 1,
    alignItems: 'center',
  },
  locationWrapper: {
    alignItems: 'center',
  },
  locationLabel: {
    fontSize: 14,
    opacity: 0.8,
    textAlign: 'center',
  },
  locationRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  locationText: {
    fontSize: 18,
    textAlign: 'center',
  },
  notificationButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  notificationDot: {
    position: 'absolute',
    top: 10,
    right: 10,
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.light.info,
    borderWidth: 1,
    borderColor: Colors.light.headerText,
  },
});
