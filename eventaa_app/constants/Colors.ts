/**
 * Colors used in the app, defined for both light and dark modes.
 * These colors are extracted from the web app to maintain consistency.
 */

// Primary colors
const primaryColor = '#ef4444';
const primaryHoverColor = '#dc2626';
const primaryLightColor = '#fecaca';
const primaryDarkLightColor = '#7f1d1d';

export const Colors = {
  light: {
    // Base colors
    text: '#111827',
    textSecondary: '#4b5563',
    textMuted: '#6b7280',
    textLight: '#9ca3af',
    background: '#f9fafb',
    card: '#ffffff',
    border: '#e5e7eb',
    borderLight: '#f3f4f6',

    // UI colors
    primary: primaryColor,
    primaryHover: primaryHoverColor,
    primaryLight: primaryLightColor,
    secondary: '#6366f1',
    success: '#10b981',
    warning: '#f97316',
    danger: '#ef4444',
    info: '#0ea5e9',

    // Tab bar colors
    tabIconDefault: '#9ca3af',
    tabIconSelected: primaryColor,
    tabBackground: '#ffffff',

    // Header colors
    headerBackground: '#4338ca',
    headerText: '#ffffff',
  },
  dark: {
    // Base colors
    text: '#f4f4f5',
    textSecondary: '#d4d4d8',
    textMuted: '#a1a1aa',
    textLight: '#71717a',
    background: '#18181b', // zinc-900
    card: '#27272a', // zinc-800
    border: '#3f3f46', // zinc-700
    borderLight: '#52525b', // zinc-600

    // UI colors
    primary: primaryColor,
    primaryHover: primaryHoverColor,
    primaryLight: primaryDarkLightColor,
    secondary: '#818cf8',
    success: '#34d399',
    warning: '#fb923c',
    danger: '#f87171',
    info: '#38bdf8',

    // Tab bar colors
    tabIconDefault: '#71717a',
    tabIconSelected: primaryColor,
    tabBackground: '#27272a',

    // Header colors
    headerBackground: '#4338ca',
    headerText: '#ffffff',
  },
};
