import React, { useState } from 'react';
import { FlatList, SafeAreaView, ScrollView, StyleSheet, View } from 'react-native';
import { StatusBar } from 'expo-status-bar';

import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { LocationHeader } from '@/components/home/<USER>';
import { SearchBar } from '@/components/home/<USER>';
import { CategoryButton } from '@/components/home/<USER>';
import { EventCard } from '@/components/home/<USER>';
import { SectionHeader } from '@/components/home/<USER>';
import { InviteFriendsCard } from '@/components/home/<USER>';

// Mock data for events
const EVENTS = [
  {
    id: '1',
    title: 'International Band Music Festival',
    date: '10',
    month: 'JUNE',
    location: '36 Guild Street London, UK',
    image: require('@/assets/images/icon.png'),
    isFavorite: false,
  },
  {
    id: '2',
    title: '<PERSON> <PERSON> Londons Mothers Day',
    date: '10',
    month: 'JUNE',
    location: 'Radius Gallery, Santa Cruz, CA',
    image: require('@/assets/images/icon.png'),
    isFavorite: true,
  },
  {
    id: '3',
    title: 'International Art Festival',
    date: '15',
    month: 'JUNE',
    location: 'Art Gallery, New York, USA',
    image: require('@/assets/images/icon.png'),
    isFavorite: false,
  },
];

export default function HomeScreen() {
  const [searchQuery, setSearchQuery] = useState('');
  const [location] = useState('New Yourk, USA');

  const handleLocationPress = () => {
    console.log('Location pressed');
  };

  const handleNotificationPress = () => {
    console.log('Notification pressed');
  };

  const handleFilterPress = () => {
    console.log('Filter pressed');
  };

  const handleCategoryPress = (category: string) => {
    console.log(`Category pressed: ${category}`);
  };

  const handleEventPress = (eventId: string) => {
    console.log(`Event pressed: ${eventId}`);
  };

  const handleFavoritePress = (eventId: string) => {
    console.log(`Favorite pressed: ${eventId}`);
  };

  const handleSeeAllPress = (section: string) => {
    console.log(`See all pressed for: ${section}`);
  };

  const handleInvitePress = () => {
    console.log('Invite pressed');
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="light" />

      {/* Header with blue background */}
      <View style={styles.header}>
        <LocationHeader
          location={location}
          onLocationPress={handleLocationPress}
          onNotificationPress={handleNotificationPress}
        />
        <SearchBar
          placeholder="Search..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          onFilterPress={handleFilterPress}
        />

        {/* Category buttons */}
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.categoriesContainer}
        >
          <CategoryButton
            title="Sports"
            icon="sports"
            color="#ef4444"
            onPress={() => handleCategoryPress('Sports')}
          />
          <CategoryButton
            title="Music"
            icon="music"
            color="#f97316"
            onPress={() => handleCategoryPress('Music')}
          />
          <CategoryButton
            title="Food"
            icon="food"
            color="#10b981"
            onPress={() => handleCategoryPress('Food')}
          />
          <CategoryButton
            title="More"
            icon="more"
            color="#6366f1"
            onPress={() => handleCategoryPress('More')}
          />
        </ScrollView>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Upcoming Events Section */}
        <SectionHeader
          title="Upcoming Events"
          onSeeAllPress={() => handleSeeAllPress('Upcoming Events')}
        />

        <FlatList
          horizontal
          data={EVENTS}
          keyExtractor={(item) => item.id}
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.eventsContainer}
          renderItem={({ item }) => (
            <EventCard
              {...item}
              onPress={() => handleEventPress(item.id)}
              onFavoritePress={() => handleFavoritePress(item.id)}
            />
          )}
        />

        {/* Invite Friends Card */}
        <InviteFriendsCard onInvitePress={handleInvitePress} />

        {/* Nearby You Section */}
        <SectionHeader
          title="Nearby You"
          onSeeAllPress={() => handleSeeAllPress('Nearby You')}
        />

        {/* Bottom navigation is handled by the tab layout */}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  header: {
    backgroundColor: Colors.light.headerBackground,
    paddingTop: 10,
    paddingBottom: 16,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  categoriesContainer: {
    paddingHorizontal: 16,
    paddingTop: 8,
    paddingBottom: 16,
    gap: 12,
  },
  content: {
    flex: 1,
  },
  eventsContainer: {
    paddingLeft: 16,
    paddingRight: 8,
  },
});
