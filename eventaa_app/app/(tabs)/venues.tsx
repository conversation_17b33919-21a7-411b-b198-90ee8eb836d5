import React, { useState, useEffect } from 'react';
import { FlatList, SafeAreaView, ScrollView, StyleSheet, View, TouchableOpacity, ActivityIndicator, Image } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { Ionicons } from '@expo/vector-icons';

import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { LocationHeader } from '@/components/home/<USER>';
import { SearchBar } from '@/components/home/<USER>';

// Mock data for venues - would be replaced with API data
const VENUES = [
  {
    id: '1',
    name: 'Grand Ballroom',
    description: 'Elegant venue for weddings and corporate events',
    address: 'Lilongwe, Malawi',
    rating: 4.8,
    capacity: 250,
    price: 'MWK 150,000',
    image: require('@/assets/images/icon.png'),
    tags: ['Wedding', 'Corporate', 'Conference'],
  },
  {
    id: '2',
    name: 'Lakeside Resort',
    description: 'Beautiful waterfront venue with stunning views',
    address: 'Mangochi, Malawi',
    rating: 4.5,
    capacity: 180,
    price: 'MWK 120,000',
    image: require('@/assets/images/icon.png'),
    tags: ['Wedding', 'Party', 'Outdoor'],
  },
  {
    id: '3',
    name: 'City Conference Center',
    description: 'Modern conference facilities in the heart of the city',
    address: 'Blantyre, Malawi',
    rating: 4.2,
    capacity: 300,
    price: 'MWK 200,000',
    image: require('@/assets/images/icon.png'),
    tags: ['Conference', 'Corporate', 'Meeting'],
  },
];

// Category data
const CATEGORIES = [
  { id: '1', name: 'All', icon: 'grid-outline' },
  { id: '2', name: 'Wedding', icon: 'heart-outline' },
  { id: '3', name: 'Conference', icon: 'business-outline' },
  { id: '4', name: 'Party', icon: 'wine-outline' },
  { id: '5', name: 'Outdoor', icon: 'leaf-outline' },
];

export default function VenuesScreen() {
  const colorScheme = useColorScheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [location] = useState('Lilongwe, Malawi');
  const [selectedCategory, setSelectedCategory] = useState('1');
  const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'map'
  const [isLoading, setIsLoading] = useState(false);
  const [venues] = useState(VENUES);

  // This would be replaced with actual API call
  useEffect(() => {
    // Simulate loading
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  }, []);

  const handleLocationPress = () => {
    console.log('Location pressed');
  };

  const handleNotificationPress = () => {
    console.log('Notification pressed');
  };

  const handleFilterPress = () => {
    console.log('Filter pressed');
  };

  const handleCategoryPress = (categoryId: string) => {
    setSelectedCategory(categoryId);
    // Would filter venues based on category
  };

  const handleVenuePress = (venueId: string) => {
    console.log(`Venue pressed: ${venueId}`);
  };

  const toggleViewMode = () => {
    setViewMode(viewMode === 'grid' ? 'map' : 'grid');
  };

  const renderCategoryButton = ({ item }: { item: typeof CATEGORIES[0] }) => (
    <TouchableOpacity
      style={[
        styles.categoryButton,
        selectedCategory === item.id && styles.selectedCategoryButton,
      ]}
      onPress={() => handleCategoryPress(item.id)}
      activeOpacity={0.7}
    >
      <Ionicons
        name={item.icon as any}
        size={20}
        color={selectedCategory === item.id ? '#ffffff' : Colors[colorScheme ?? 'light'].primary}
      />
      <ThemedText
        style={[
          styles.categoryText,
          selectedCategory === item.id && styles.selectedCategoryText,
        ]}
      >
        {item.name}
      </ThemedText>
    </TouchableOpacity>
  );

  const renderVenueCard = ({ item }: { item: typeof VENUES[0] }) => (
    <TouchableOpacity
      style={styles.venueCard}
      onPress={() => handleVenuePress(item.id)}
      activeOpacity={0.9}
    >
      <View style={styles.imageContainer}>
        <Image source={item.image} style={styles.venueImage} />
        <View style={styles.ratingContainer}>
          <Ionicons name="star" size={14} color="#FFC107" />
          <ThemedText style={styles.ratingText}>{item.rating}</ThemedText>
        </View>
      </View>

      <View style={styles.venueDetails}>
        <ThemedText style={styles.venueName} numberOfLines={1}>{item.name}</ThemedText>
        <View style={styles.locationRow}>
          <Ionicons name="location-outline" size={14} color={Colors[colorScheme ?? 'light'].textSecondary} />
          <ThemedText style={styles.locationText} numberOfLines={1}>{item.address}</ThemedText>
        </View>

        <View style={styles.tagsContainer}>
          {item.tags.slice(0, 2).map((tag, index) => (
            <View key={index} style={styles.tagChip}>
              <ThemedText style={styles.tagText}>{tag}</ThemedText>
            </View>
          ))}
          {item.tags.length > 2 && (
            <ThemedText style={styles.moreTagsText}>+{item.tags.length - 2}</ThemedText>
          )}
        </View>

        <View style={styles.priceRow}>
          <ThemedText style={styles.priceLabel}>From</ThemedText>
          <ThemedText style={styles.priceValue}>{item.price}</ThemedText>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />

      {/* Header with blue background */}
      <ThemedView style={styles.header} variant="header">
        <LocationHeader
          location={location}
          onLocationPress={handleLocationPress}
          onNotificationPress={handleNotificationPress}
        />
        <SearchBar
          placeholder="Search venues..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          onFilterPress={handleFilterPress}
        />
      </ThemedView>

      <ThemedView style={styles.content}>
        {/* View mode toggle and categories */}
        <View style={styles.controlsContainer}>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoriesContainer}
          >
            {CATEGORIES.map((category) => renderCategoryButton({ item: category }))}
          </ScrollView>

          <TouchableOpacity
            style={styles.viewModeButton}
            onPress={toggleViewMode}
            activeOpacity={0.7}
          >
            <Ionicons
              name={viewMode === 'grid' ? 'map-outline' : 'grid-outline'}
              size={24}
              color={Colors[colorScheme ?? 'light'].primary}
            />
          </TouchableOpacity>
        </View>

        {/* Venues list */}
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={Colors[colorScheme ?? 'light'].primary} />
            <ThemedText style={styles.loadingText}>Loading venues...</ThemedText>
          </View>
        ) : viewMode === 'grid' ? (
          <FlatList
            data={venues}
            keyExtractor={(item) => item.id}
            renderItem={renderVenueCard}
            contentContainerStyle={styles.venuesContainer}
            showsVerticalScrollIndicator={false}
            numColumns={2}
            columnWrapperStyle={styles.venueRow}
          />
        ) : (
          <ThemedView style={styles.mapContainer}>
            <ThemedText style={styles.mapPlaceholder}>Map view coming soon</ThemedText>
            <Ionicons name="map" size={48} color={Colors[colorScheme ?? 'light'].textSecondary} />
          </ThemedView>
        )}
      </ThemedView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    backgroundColor: Colors.light.headerBackground, // Will be themed by ThemedView
    paddingTop: 10,
    paddingBottom: 16,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  content: {
    flex: 1,
    paddingTop: 16,
  },
  controlsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  categoriesContainer: {
    flexGrow: 1,
    gap: 8,
  },
  categoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: Colors.light.card,
    marginRight: 8,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  selectedCategoryButton: {
    backgroundColor: Colors.light.primary,
    borderColor: Colors.light.primary,
  },
  categoryText: {
    marginLeft: 6,
    fontSize: 14,
  },
  selectedCategoryText: {
    color: '#ffffff',
  },
  viewModeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.light.card,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  venuesContainer: {
    paddingHorizontal: 8,
    paddingBottom: 20,
  },
  venueRow: {
    justifyContent: 'space-between',
    paddingHorizontal: 8,
  },
  venueCard: {
    width: '48%',
    backgroundColor: Colors.light.card,
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  imageContainer: {
    position: 'relative',
    height: 120,
  },
  venueImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  ratingContainer: {
    position: 'absolute',
    top: 8,
    right: 8,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 12,
  },
  ratingText: {
    color: '#ffffff',
    fontSize: 12,
    marginLeft: 2,
    fontWeight: 'bold',
  },
  venueDetails: {
    padding: 12,
  },
  venueName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  locationRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  locationText: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    marginLeft: 4,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 8,
  },
  tagChip: {
    backgroundColor: Colors.light.borderLight,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
    marginRight: 4,
    marginBottom: 4,
  },
  tagText: {
    fontSize: 10,
    color: Colors.light.textSecondary,
  },
  moreTagsText: {
    fontSize: 10,
    color: Colors.light.textSecondary,
    alignSelf: 'center',
    marginLeft: 2,
  },
  priceRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  priceLabel: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    marginRight: 4,
  },
  priceValue: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.light.primary,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    color: Colors.light.textSecondary,
  },
  mapContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  mapPlaceholder: {
    marginBottom: 16,
    fontSize: 16,
    color: Colors.light.textSecondary,
  },
});
