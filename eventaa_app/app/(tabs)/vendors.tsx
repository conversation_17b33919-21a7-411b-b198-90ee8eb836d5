import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  FlatList,
  Animated,
  ActivityIndicator,
  Dimensions,
  TextInput
} from 'react-native';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { StatusBar } from 'expo-status-bar';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

// Mock data for vendors
const VENDORS = [
  {
    id: '1',
    name: 'Elegant Events Planning',
    description: 'Full-service event planning and coordination',
    location: 'Lilongwe, Malawi',
    rating: 4.9,
    reviewCount: 128,
    services: ['Planning', 'Coordination', 'Decoration'],
    pricing: 'Premium',
    image: require('@/assets/images/icon.png'),
    featured: true,
    verified: true,
  },
  {
    id: '2',
    name: 'Sound & Lights Pro',
    description: 'Professional audio and lighting solutions',
    location: 'Blantyre, Malawi',
    rating: 4.7,
    reviewCount: 89,
    services: ['Audio', 'Lighting', 'DJ Services'],
    pricing: 'Standard',
    image: require('@/assets/images/icon.png'),
    featured: false,
    verified: true,
  },
  {
    id: '3',
    name: 'Gourmet Catering',
    description: 'Exquisite catering for all types of events',
    location: 'Lilongwe, Malawi',
    rating: 4.8,
    reviewCount: 156,
    services: ['Catering', 'Beverage Service', 'Staff'],
    pricing: 'Premium',
    image: require('@/assets/images/icon.png'),
    featured: true,
    verified: true,
  },
  {
    id: '4',
    name: 'Perfect Photography',
    description: 'Capturing your special moments with creativity',
    location: 'Mzuzu, Malawi',
    rating: 4.5,
    reviewCount: 72,
    services: ['Photography', 'Videography', 'Editing'],
    pricing: 'Standard',
    image: require('@/assets/images/icon.png'),
    featured: false,
    verified: false,
  },
  {
    id: '5',
    name: 'Floral Artistry',
    description: 'Beautiful floral arrangements for any occasion',
    location: 'Zomba, Malawi',
    rating: 4.6,
    reviewCount: 94,
    services: ['Floral Design', 'Decoration', 'Setup'],
    pricing: 'Standard',
    image: require('@/assets/images/icon.png'),
    featured: false,
    verified: true,
  },
];

// Categories for filter
const CATEGORIES = [
  { id: 'featured', name: 'Featured', icon: 'star-outline' },
  { id: 'planning', name: 'Planning', icon: 'calendar-outline' },
  { id: 'catering', name: 'Catering', icon: 'restaurant-outline' },
  { id: 'music', name: 'Music', icon: 'musical-notes-outline' },
  { id: 'photo', name: 'Photography', icon: 'camera-outline' },
  { id: 'decor', name: 'Decoration', icon: 'color-palette-outline' },
];

// Screen width for layout calculations
const { width } = Dimensions.get('window');

export default function VendorsScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const scrollY = useRef(new Animated.Value(0)).current;
  const [activeCategory, setActiveCategory] = useState('featured');
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [vendors, setVendors] = useState(VENDORS);
  const [showFilters, setShowFilters] = useState(false);

  // Animation values
  const headerHeight = scrollY.interpolate({
    inputRange: [0, 100],
    outputRange: [180, 80],
    extrapolate: 'clamp',
  });

  const headerTitleOpacity = scrollY.interpolate({
    inputRange: [50, 100],
    outputRange: [0, 1],
    extrapolate: 'clamp',
  });

  const headerSearchScale = scrollY.interpolate({
    inputRange: [0, 100],
    outputRange: [1, 0.8],
    extrapolate: 'clamp',
  });

  const headerSearchOpacity = scrollY.interpolate({
    inputRange: [0, 60, 100],
    outputRange: [1, 0.3, 0],
    extrapolate: 'clamp',
  });

  // Load vendors with simulated API call
  useEffect(() => {
    const fetchVendors = async () => {
      setIsLoading(true);
      // Simulate API call
      setTimeout(() => {
        setVendors(VENDORS);
        setIsLoading(false);
      }, 1000);
    };

    fetchVendors();
  }, []);

  // Filter vendors by category
  const filterVendorsByCategory = (categoryId: string) => {
    setActiveCategory(categoryId);
    setIsLoading(true);

    setTimeout(() => {
      if (categoryId === 'featured') {
        setVendors(VENDORS.filter(vendor => vendor.featured));
      } else {
        // In a real app, you would filter by actual category from an API
        setVendors(VENDORS);
      }
      setIsLoading(false);
    }, 500);
  };

  // Render category pill
  const renderCategory = ({ item }: { item: typeof CATEGORIES[0] }) => (
    <TouchableOpacity
      style={[
        styles.categoryPill,
        activeCategory === item.id && {
          backgroundColor: colors.primary,
        }
      ]}
      onPress={() => filterVendorsByCategory(item.id)}
      activeOpacity={0.7}
    >
      <Ionicons
        name={item.icon as any}
        size={18}
        color={activeCategory === item.id ? '#fff' : colors.textSecondary}
        style={styles.categoryIcon}
      />
      <ThemedText
        style={[
          styles.categoryText,
          activeCategory === item.id && { color: '#fff' },
        ]}
      >
        {item.name}
      </ThemedText>
    </TouchableOpacity>
  );

  // Render vendor card
  const renderVendorCard = ({ item }: { item: typeof VENDORS[0] }) => (
    <TouchableOpacity
      style={styles.vendorCard}
      activeOpacity={0.9}
      onPress={() => console.log(`Vendor pressed: ${item.id}`)}
    >
      <View style={styles.cardImageContainer}>
        <Image source={item.image} style={styles.cardImage} />
        {item.featured && (
          <View style={styles.featuredTag}>
            <ThemedText style={styles.featuredText}>Featured</ThemedText>
          </View>
        )}
        <LinearGradient
          colors={['transparent', 'rgba(0,0,0,0.8)']}
          style={styles.cardGradient}
        />
      </View>

      <View style={styles.cardContent}>
        <View style={styles.cardHeader}>
          <View style={styles.nameContainer}>
            <ThemedText style={styles.vendorName}>{item.name}</ThemedText>
            {item.verified && (
              <MaterialIcons name="verified" size={16} color={colors.primary} style={styles.verifiedIcon} />
            )}
          </View>
          <View style={styles.ratingContainer}>
            <MaterialIcons name="star" size={16} color="#FFC107" />
            <ThemedText style={styles.ratingText}>{item.rating}</ThemedText>
            <ThemedText style={styles.reviewCount}>({item.reviewCount})</ThemedText>
          </View>
        </View>

        <ThemedText style={styles.vendorDescription} numberOfLines={2}>
          {item.description}
        </ThemedText>

        <View style={styles.locationRow}>
          <Ionicons name="location-outline" size={14} color={colors.textSecondary} />
          <ThemedText style={styles.locationText} numberOfLines={1}>
            {item.location}
          </ThemedText>
        </View>

        <View style={styles.tagsRow}>
          {item.services.map((service: string, index: number) => (
            <View key={index} style={styles.tagChip}>
              <ThemedText style={styles.tagText}>{service}</ThemedText>
            </View>
          ))}
        </View>

        <View style={styles.cardFooter}>
          <View style={[
            styles.pricingBadge,
            { backgroundColor: item.pricing === 'Premium' ? colors.primary + '20' : colors.secondary + '20' }
          ]}>
            <Ionicons
              name={item.pricing === 'Premium' ? 'diamond-outline' : 'cash-outline'}
              size={14}
              color={item.pricing === 'Premium' ? colors.primary : colors.secondary}
            />
            <ThemedText style={[
              styles.pricingText,
              { color: item.pricing === 'Premium' ? colors.primary : colors.secondary }
            ]}>
              {item.pricing}
            </ThemedText>
          </View>

          <TouchableOpacity
            style={styles.contactButton}
            onPress={() => console.log(`Contact vendor: ${item.id}`)}
          >
            <ThemedText style={styles.contactButtonText}>Contact</ThemedText>
          </TouchableOpacity>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <StatusBar style="light" />

      {/* Animated Header */}
      <Animated.View style={[styles.header, { height: headerHeight }]}>
        <LinearGradient
          colors={['#4338ca', '#6366f1']}
          style={StyleSheet.absoluteFill}
        />

        {/* Header content */}
        <View style={styles.headerContent}>
          <Animated.View style={[styles.headerTitle, { opacity: headerTitleOpacity }]}>
            <ThemedText style={styles.headerTitleText}>Vendors</ThemedText>
          </Animated.View>

          <Animated.View
            style={[
              styles.searchContainer,
              {
                transform: [{ scale: headerSearchScale }],
                opacity: headerSearchOpacity,
              }
            ]}
          >
            <ThemedText style={styles.headerText}>Find the perfect vendor</ThemedText>
            <ThemedText style={styles.headerSubtext}>
              Browse through our curated list of top event vendors
            </ThemedText>
          </Animated.View>
        </View>

        {/* Search bar */}
        <Animated.View
          style={[
            styles.searchBarContainer,
            {
              transform: [{
                translateY: scrollY.interpolate({
                  inputRange: [0, 100],
                  outputRange: [0, -30],
                  extrapolate: 'clamp',
                })
              }],
            }
          ]}
        >
          <View style={styles.searchBar}>
            <Ionicons name="search" size={20} color={colors.textSecondary} style={styles.searchIcon} />
            <TextInput
              style={styles.searchInput}
              placeholder="Search vendors..."
              placeholderTextColor={colors.textSecondary}
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
            <TouchableOpacity
              style={styles.filterButton}
              onPress={() => setShowFilters(!showFilters)}
            >
              <Ionicons name="options-outline" size={20} color={colors.primary} />
            </TouchableOpacity>
          </View>
        </Animated.View>
      </Animated.View>

      {/* Main content */}
      <Animated.ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollViewContent}
        onScroll={Animated.event(
          [{ nativeEvent: { contentOffset: { y: scrollY } } }],
          { useNativeDriver: false }
        )}
        scrollEventThrottle={16}
      >
        {/* Categories */}
        <View style={styles.categoriesContainer}>
          <FlatList
            horizontal
            data={CATEGORIES}
            renderItem={renderCategory}
            keyExtractor={(item) => item.id}
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoriesList}
          />
        </View>

        {/* Filters row - conditionally shown */}
        {showFilters && (
          <View style={styles.filtersRow}>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <TouchableOpacity style={styles.filterChip}>
                <ThemedText style={styles.filterChipText}>Price: Any</ThemedText>
                <Ionicons name="chevron-down" size={16} color={colors.textSecondary} />
              </TouchableOpacity>
              <TouchableOpacity style={styles.filterChip}>
                <ThemedText style={styles.filterChipText}>Rating: 4+</ThemedText>
                <Ionicons name="chevron-down" size={16} color={colors.textSecondary} />
              </TouchableOpacity>
              <TouchableOpacity style={styles.filterChip}>
                <ThemedText style={styles.filterChipText}>Verified Only</ThemedText>
              </TouchableOpacity>
              <TouchableOpacity style={styles.filterChip}>
                <ThemedText style={styles.filterChipText}>Available Now</ThemedText>
              </TouchableOpacity>
            </ScrollView>
          </View>
        )}

        {/* Results count */}
        <View style={styles.resultsHeader}>
          <ThemedText style={styles.resultsText}>
            {isLoading ? 'Searching...' : `${vendors.length} vendors found`}
          </ThemedText>
          <TouchableOpacity style={styles.sortButton}>
            <ThemedText style={styles.sortText}>Sort by: Rating</ThemedText>
            <Ionicons name="chevron-down" size={16} color={colors.textSecondary} />
          </TouchableOpacity>
        </View>

        {/* Vendors list */}
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
            <ThemedText style={styles.loadingText}>Finding the best vendors...</ThemedText>
          </View>
        ) : (
          <View style={styles.vendorsContainer}>
            {vendors.map(vendor => (
              <View key={vendor.id} style={styles.vendorCardWrapper}>
                {renderVendorCard({ item: vendor })}
              </View>
            ))}
          </View>
        )}
      </Animated.ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 10,
    overflow: 'hidden',
  },
  headerContent: {
    flex: 1,
    paddingTop: 50,
    paddingHorizontal: 20,
    justifyContent: 'space-between',
  },
  headerTitle: {
    position: 'absolute',
    top: 50,
    left: 20,
    right: 20,
  },
  headerTitleText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  searchContainer: {
    marginTop: 20,
  },
  headerText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  headerSubtext: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  searchBarContainer: {
    position: 'absolute',
    bottom: -25,
    left: 0,
    right: 0,
    paddingHorizontal: 20,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 12,
    paddingVertical: 10,
    paddingHorizontal: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: Colors.light.text,
  },
  filterButton: {
    padding: 5,
  },
  content: {
    flex: 1,
    marginTop: 180, // Same as initial header height
  },
  scrollViewContent: {
    paddingTop: 20,
    paddingBottom: 30,
  },
  categoriesContainer: {
    marginBottom: 15,
  },
  categoriesList: {
    paddingHorizontal: 20,
  },
  categoryPill: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 20,
    backgroundColor: '#fff',
    marginRight: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  categoryIcon: {
    marginRight: 6,
  },
  categoryText: {
    fontSize: 14,
    fontWeight: '500',
  },
  filtersRow: {
    paddingHorizontal: 20,
    marginBottom: 15,
  },
  filterChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    backgroundColor: '#fff',
    marginRight: 10,
    borderWidth: 1,
    borderColor: Colors.light.borderLight,
  },
  filterChipText: {
    fontSize: 13,
    marginRight: 5,
  },
  resultsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 15,
  },
  resultsText: {
    fontSize: 14,
    fontWeight: '500',
  },
  sortButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sortText: {
    fontSize: 14,
    marginRight: 5,
    color: Colors.light.textSecondary,
  },
  loadingContainer: {
    paddingVertical: 50,
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 14,
    color: Colors.light.textSecondary,
  },
  vendorsContainer: {
    paddingHorizontal: 20,
  },
  vendorCardWrapper: {
    marginBottom: 20,
  },
  vendorCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  cardImageContainer: {
    height: 180,
    position: 'relative',
  },
  cardImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  cardGradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 60,
  },
  featuredTag: {
    position: 'absolute',
    top: 15,
    left: 0,
    backgroundColor: Colors.light.primary,
    paddingVertical: 5,
    paddingHorizontal: 15,
    borderTopRightRadius: 20,
    borderBottomRightRadius: 20,
  },
  featuredText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  cardContent: {
    padding: 16,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  nameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  vendorName: {
    fontSize: 18,
    fontWeight: 'bold',
    marginRight: 5,
  },
  verifiedIcon: {
    marginLeft: 4,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    fontSize: 14,
    fontWeight: 'bold',
    marginLeft: 4,
  },
  reviewCount: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    marginLeft: 2,
  },
  vendorDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 10,
    color: Colors.light.textSecondary,
  },
  locationRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  locationText: {
    fontSize: 13,
    marginLeft: 5,
    color: Colors.light.textSecondary,
  },
  tagsRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 12,
  },
  tagChip: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    backgroundColor: Colors.light.borderLight,
    marginRight: 6,
    marginBottom: 6,
  },
  tagText: {
    fontSize: 12,
    color: Colors.light.textSecondary,
  },
  cardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 4,
  },
  pricingBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 12,
  },
  pricingText: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
  },
  contactButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: Colors.light.primary,
    borderRadius: 8,
  },
  contactButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#fff',
  },
});
