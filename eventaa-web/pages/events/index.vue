<template>
  <div>
    <LandingHomeHero @onQuery="onQuery" @tags="onSearchTags" />
    <div v-if="activeFilters.length > 0" class="w-full px-5 mt-3">
      <div class="flex items-center justify-between bg-gray-50 p-3">
        <div class="flex flex-wrap items-center gap-2">
          <span class="text-base font-medim mr-2">Active Filters:</span>
          <div v-for="filter in activeFilters" :key="filter.key"
            class="flex items-center bg-gray-100 border border-gray-200 px-1 py-0.5 rounded-full text-xs">
            <span class="font-thin">{{ filter.label }}</span>
            <button
              @click="removeFilter(filter.key as 'location' | 'search' | 'categories' | 'dateRange' | 'trending' | 'recommended')"
              class="ml-2 border bg-gray-300 rounded-full p-1">
              <Icon icon="mdi:close" class="w-4 h-4" />
            </button>
          </div>
          <button @click="clearAllFilters"
            class="ml-2 text-medium border rounded-full bg-white px-2 py-1 text-red-600 hover:text-red-100 hover:bg-red-600 transition duration-150">
            Clear All
          </button>
        </div>
      </div>
    </div>
    <div class="w-full mt-5 px-5" v-if="loading">
      <EventsSkeletonLoader v-if="loading" :is-grid="toggleGrid" :items-to-show="itemsPerPage" />
    </div>

    <div class="w-full mt-5 px-5" v-else>
      <div class="w-full flex justify-between">
        <h3 class="text-xl font-semibold tracking-wider">
          Showing {{ events.length }} events
        </h3>
        <div class="flex items-center space-x-2">
          <EventsFilters @apply-filters="onFiltersApply" />
          <div class="relative flex">
            <div class="relative">
              <button @click="changeView('grid')" :class="[
                toggleGrid
                  ? 'bg-red-600 text-white'
                  : 'bg-gray-200 text-gray-500',
                'p-1.5 shiny',
              ]">
                <Icon icon="ep:grid" class="w-6 h-6" />
              </button>
              <span v-if="toggleGrid"
                class="absolute left-1/2 -bottom-1 transform -translate-x-1/2 w-0 h-0 border-l-[6px] border-l-transparent border-r-[6px] border-r-transparent border-t-[6px] border-t-red-600"></span>
            </div>

            <div class="relative">
              <button @click="changeView('list')" :class="[
                toggleGrid
                  ? 'bg-gray-200 text-gray-500'
                  : 'bg-red-600 text-white',
                'p-1.5 shiny',
              ]">
                <Icon icon="ph:list-bold" class="w-6 h-6" />
              </button>
              <span v-if="!toggleGrid"
                class="absolute left-1/2 -bottom-1 transform -translate-x-1/2 w-0 h-0 border-l-[6px] border-l-transparent border-r-[6px] border-r-transparent border-t-[6px] border-t-red-600"></span>
            </div>
          </div>
        </div>
      </div>

      <div>
        <div v-if="toggleGrid" class="w-full sm:grid sm:grid-cols-3 gap-4 mt-1 space-y-4 sm:space-y-0">
          <EventsCard v-for="event in events" :key="event.id" :event="event" />
        </div>

        <div class="mt-1 w-full flex flex-col space-y-2.5" v-else>
          <EventsListview v-for="event in events" :key="event.id" :event="event" />
        </div>
      </div>

      <div class="flex flex-col items-center mt-4" v-if="totalEvents > 0">
        <span class="">
          Showing
          <span class="font-semibold">{{ startIndex + 1 }}</span>
          to
          <span class="font-semibold">{{ endIndex }}</span>
          of
          <span class="font-semibold">{{ totalEvents }}</span>
          Entries
        </span>
        <div class="inline-flex mt-2 xs:mt-0">
          <button @click="prevPage" :disabled="currentPage === 1"
            class="flex items-center justify-center px-4 h-10 text-base font-medium bg-gray-200 hover:bg-gray-300 transition duration-150 disabled:opacity-50 disabled:cursor-not-allowed">
            <Icon icon="grommet-icons:link-previous" class="w-5 h-5 mr-2" />
            Prev
          </button>
          <button @click="nextPage" :disabled="currentPage === totalPages"
            class="flex items-center justify-center px-4 h-10 text-base font-medium transition duration-150 text-white bg-red-600 border-0 hover:bg-red-700 disabled:cursor-not-allowed">
            Next
            <Icon icon="streamline:next" class="w-5 h-5 ml-2" />
          </button>
        </div>
      </div>

      <div v-if="totalEvents == 0" class="w-full flex flex-col space-y-2.5 items-center justify-center py-20">
        <img src="/public/assets/svgs/tester.svg" class="w-auto h-72" alt="no-events-story-illustration" />
        <h3 class="font-semibold text-lg">Could not load events, please try again later...</h3>
        <button @click="fetchEvents"
          class="inline-flex items-center text-sky-500 transition-all hover:text-sky-400 duration-150">
          <Icon icon="system-uicons:refresh" class="w-5 h-5 mr-2" /> Refresh
        </button>
      </div>
    </div>

    <div class="mt-10">
      <div
        class="flex items-center max-md:flex-col gap-6 bg-gradient-to-tr from-red-700 to-pink-500 text-white px-6 py-3.5">
        <p class="text-base flex-1 max-md:text-center">
          <span class="text-xl font-semibold flex items-center">
            <Icon icon="mage:megaphone-a" class="h-6 w-6 mr-2" /> Did you know?
          </span>
          You can host your own virtual and venue events here on Eventa? Sign up
          today and start hosting your own events.
        </p>

        <div>
          <button type="button" class="bg-white text-black py-2.5 px-5">
            Become Host
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Category, EventItem } from '@/types';
import type { EventsResponse } from '@/types/api';
import dayjs from 'dayjs';
import type { FilterComponents } from '~/types/filters';

definePageMeta({
  layout: "default",
});

useHead({
  title: "Explore events - Eventa Malawi",
  meta: [
    {
      name: "description",
      content: "Events page",
    },
  ],
});

const httpClient = useHttpClient();
const toggleGrid = ref<boolean>(true);
const currentPage = ref<number>(1);
const itemsPerPage = ref<number>(6);
const totalEvents = ref<number>(0);
const events = ref<EventItem[]>([]);
const loading = ref<boolean>(false);
const dateRange = ref<string>('');
const searchQuery = ref<string>('');
const categories = ref<number[]>([]);
const lat = ref<number>(0);
const lng = ref<number>(0);
const radius = ref<number>(10990);
const address = ref<string>('');
const routerQuery = useRouterQuery();
const { $toast }: any = useNuxtApp();

const totalPages = computed(() =>
  Math.ceil(totalEvents.value / itemsPerPage.value)
);

const startIndex = computed(() =>
  (currentPage.value - 1) * itemsPerPage.value
);

const endIndex = computed(() =>
  Math.min(startIndex.value + itemsPerPage.value, totalEvents.value)
);

const prevPage = (): void => {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
};

const nextPage = (): void => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
};

const changeView = (type: "grid" | "list"): void => {
  toggleGrid.value = type === "grid";
  routerQuery.replaceOneQuery("view", { view: type });
}

interface ActiveFilter {
  key: string;
  label: string;
}

const activeFilters = ref<ActiveFilter[]>([]);

const filterResetActions = {
  search: () => {
    searchQuery.value = '';
  },
  categories: () => {
    categories.value = [];
  },
  dateRange: () => {
    dateRange.value = '';
  },
  location: () => {
    lat.value = 0;
    lng.value = 0;
    radius.value = 100;
    address.value = '';
  },
  trending: () => {
    activeFilters.value.map((filter) => {
      if (filter.key == "trending") {
        activeFilters.value.splice(activeFilters.value.indexOf(filter), 1);
        routerQuery.removeAllQueries()
      }
    });
  },
  recommended: () => {
    activeFilters.value.map((filter) => {
      if (filter.key == "recommended") {
        activeFilters.value.splice(activeFilters.value.indexOf(filter), 1);
      }
    });
  },
  nearby: () => {
    activeFilters.value.map((filter) => {
      if (filter.key == "nearby") {
        activeFilters.value.splice(activeFilters.value.indexOf(filter), 1);
      }
    });
  }
};

const removeFilter = (key: keyof typeof filterResetActions) => {
  const resetAction = filterResetActions[key];
  if (resetAction) {
    resetAction();
  }
};

const clearAllFilters = () => {
  searchQuery.value = '';
  categories.value = [];
  dateRange.value = '';
  lat.value = 0;
  lng.value = 0;
  radius.value = 100;
  address.value = '';
};

const updateActiveFilters = () => {
  activeFilters.value = [];

  if (searchQuery.value) {
    activeFilters.value.push({
      key: 'search',
      label: `Search: ${searchQuery.value}`
    });
  }

  if (categories.value.length > 0) {
    activeFilters.value.push({
      key: 'categories',
      label: `Categories: ${categories.value.length} selected`
    });
  }

  if (dateRange.value) {
    const [start, end] = dateRange.value.split(',');
    activeFilters.value.push({
      key: 'dateRange',
      label: `Date: ${dayjs(start).format('MMM D')} - ${dayjs(end).format('MMM D')}`
    });
  }

  if (address.value) {
    activeFilters.value.push({
      key: 'nearby',
      label: `Nearby: ${address.value}`
    });
  }
};

watch(
  [() => searchQuery.value, () => categories.value, () => dateRange.value],
  () => {
    updateActiveFilters();
    if (searchQuery.value || categories.value.length > 0 || dateRange.value.length > 1) {
      searchEvents(`title=${searchQuery.value}&categories=${JSON.stringify(categories.value)}&dateRange=${dateRange.value || ''}`);
    } else {
      fetchEvents();
    }
  },
  { deep: true }
);

const onFiltersApply = (components: FilterComponents): void => {
  if (components) {
    const queryParams: Record<string, string> = {};
    if (components.dateRange && components.dateRange.length === 2) {
      dateRange.value = `${dayjs(components.dateRange[0]).format(DATE_FORMAT.FULL)},${dayjs(components.dateRange[1]).format(DATE_FORMAT.FULL)}`;
    }

    if (components.categories && components.categories.length > 0) {
      categories.value = components.categories.map((category: Category) => {
        return category.id;
      });
    }

    if (components.location) {
      if (components.location.center?.lat) {
        lat.value = Number(components.location.center.lat);
      }
      if (components.location.center?.lng) {
        lng.value = Number(components.location.center.lng);
      }
      if (components.location.radius) {
        radius.value = Number(components.location.radius);
      }
      if (components.location.address) {
        address.value = components.location.address;
      }
    }

    updateActiveFilters();

    if (Object.keys(queryParams).length > 0) {
      routerQuery.removeAllQueries();
      routerQuery.replaceQuery(queryParams);
    }
  }
};

const onQuery = async(items: { search: string; category: string | number; location: any; }): Promise<void> => {
  searchQuery.value = items.search;
  categories.value = [];
  if (items.category && typeof items.category === 'number') {
    categories.value.push(items.category);
  }
  if(items.location){
    address.value = items.location.city;
    lat.value = items.location.latitude;
    lng.value = items.location.longitude;
  }
};

const onSearchTags = (newTag: "Trending" | "Recommended" | "Nearby Me"): void => {
  const isTagAlreadyActive = activeFilters.value.some(
    filter => filter.key === newTag.toString().toLowerCase()
  );

  if (newTag?.toString().toLowerCase() === "trending") {
    fetchTrendingEvents();
  }

  if (newTag?.toString().toLowerCase() === "recommended") {
    fetchRecommendedEvents();
  }

  if (newTag?.toString().toLowerCase() === "nearby") {
    fetchNearbyEvents();
  }

  if (!isTagAlreadyActive) {
    activeFilters.value.push({
      key: newTag.toString().toLowerCase(),
      label: newTag.charAt(0).toUpperCase() + newTag.slice(1),
    });
  }
}

const fetchEvents = async (): Promise<void> => {
  try {
    loading.value = true;
    const response = await httpClient.get<EventsResponse>(
      `${ENDPOINTS.EVENTS.GET}?per_page=${itemsPerPage.value}&page=${currentPage.value}`
    );
    if (response) {
      events.value = response.events.data;
      totalEvents.value = response.events.total;
    }
  } catch (error) {
    $toast.error('An error occurred while fetching events, please try again later.');
    console.error('Error fetching events:', error);
  } finally {
    loading.value = false;
  }
};

const searchEvents = async (query: string): Promise<void> => {
  try {
    loading.value = true;
    const response = await httpClient.get<EventsResponse>(
      `${ENDPOINTS.EVENTS.SEARCH}?per_page=${itemsPerPage.value}&page=${currentPage.value}&${query}`
    );
    if (response) {
      events.value = response.events.data;
      totalEvents.value = response.events.total;
      response.events.data.length == 0 && $toast.warn('No events found with the specified filters, please try again.');
    }
  } catch (error) {
    $toast.error('An error occurred while fetching events, please try again later.');
    console.error('Error fetching events:', error);
  } finally {
    loading.value = false;
  }
};

const fetchTrendingEvents = async (): Promise<void> => {
  try {
    loading.value = true;
    const response = await httpClient.get<EventsResponse>(
      `${ENDPOINTS.EVENTS.TRENDING}?per_page=${itemsPerPage.value}&page=${currentPage.value}`
    );
    if (response) {
      events.value = response.events.data;
      totalEvents.value = response.events.total;
    }
  } catch (error) {
    $toast.error('An error occurred while fetching events, please try again later.');
    console.error('Error fetching events:', error);
  } finally {
    loading.value = false;
  }
}

const fetchRecommendedEvents = async (): Promise<void> => {
  try {
    loading.value = true;
    const response = await httpClient.get<EventsResponse>(
      `${ENDPOINTS.EVENTS.RECOMMENDED}?per_page=${itemsPerPage.value}&page=${currentPage.value}`
    );
    if (response) {
      events.value = response.events.data;
      totalEvents.value = response.events.total;
    }
  } catch (error) {
    $toast.error('An error occurred while fetching events, please try again later.');
    console.error('Error fetching events:', error);
  } finally {
    loading.value = false;
  }
}

const fetchNearbyEvents = async (): Promise<void> => {
  try {
    $toast.success("Searching events 100KM around you...");
    loading.value = true;
    const response = await httpClient.post<EventsResponse>(
      `${ENDPOINTS.EVENTS.NEARBY}?per_page=${itemsPerPage.value}&page=${currentPage.value}&latitude=${lat.value}&longitude=${lng.value}&radius=${radius.value}&city=${address.value}`
    );
    if (response) {
      events.value = response.events.data;
      totalEvents.value = response.events.total;
    }
  } catch (error) {
    $toast.error('An error occurred while fetching events, please try again later.');
    console.error('Error fetching events:', error);
  } finally {
    loading.value = false;
  }
}

watch(currentPage, (newPage: number, oldPage: number) => {
  if (newPage !== oldPage) {
    fetchEvents();
  }
});

watch(
  [() => searchQuery.value, () => categories.value, () => dateRange.value, address.value],
  ([newQuery, newCategories, newDateRange, newAddress]) => {
    if (newQuery || newCategories.length > 0 || newDateRange.length > 1) {
      searchEvents(`title=${newQuery}&categories=${JSON.stringify(newCategories)}&dateRange=${newDateRange || ''}`);
    } else if(newAddress !== null && lat.value !== 0 && lng.value !== 0) {
      fetchNearbyEvents();
    } else {
      fetchEvents();
    }
  },
  { deep: true }
);

onMounted(() => {
  fetchEvents();
});
</script>

<style lang="scss" scoped>
.moving-background {
  position: relative;
  background-image: url("/assets/images/hero.png");
  background-size: cover;
  background-position: center;
}

.shiny {
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-1px);
  }
}
</style>