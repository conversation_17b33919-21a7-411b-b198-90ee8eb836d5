<template>
    <NuxtLoadingIndicator />
    <SkeletonEventSlug v-if="loading"/>
    <div v-else class="w-full bg-gray-50 dark:bg-zinc-800">
        <div class="px-5 mx-auto py-5">
            <div class="grid grid-cols-1 lg:grid-cols-5 gap-6">
                <div class="lg:col-span-3 space-y-6">
                    <div class="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                        <div class="w-16 shadow bg-white dark:bg-zinc-900 flex-shrink-0">
                            <div class="rounded-none flex flex-col items-center">
                                <div
                                    class="bg-red-600 uppercase px-2 py-2 text-white text-xl font-semibold w-full text-center">
                                    {{ dayjs(event?.start).format(DATE_FORMAT.SINGLE_MONTH) }}
                                </div>
                                <div class="text-2xl font-bold text-center py-2 dark:text-zinc-50">
                                    {{ dayjs(event?.start).format(DATE_FORMAT.SINGLE_DAY) }}
                                </div>
                            </div>
                        </div>
                        <div class="space-y-2">
                            <h3 class="text-xl sm:text-2xl font-bold dark:text-zinc-50">
                                {{ event?.title || "" }}
                            </h3>
                            <div class="flex items-center gap-2 text-gray-600 dark:text-zinc-100 pb-0.5">
                                <Icon icon="fa6-solid:map-location" class="w-5 h-5" />
                                <p class="text-sm sm:text-base">{{ event?.location }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="relative group">
                        <img :src="`${runtimeConfig.public.baseUrl}storage/events/${event?.cover_art}`"
                            :alt="`${event?.slug}-event-banner`"
                            class="w-full h-48 sm:h-72 object-cover shadow shadow-red-50 transition-transform duration-300" />
                        <div class="sm:hidden flex justify-end absolute bottom-0 left-0 divide-x-2 divide-gray-200 dark:divide-zinc-700">
                            <button class="bg-black bg-opacity-75 px-4 py-2 text-white flex items-center gap-2">
                                <Icon icon="fluent:share-28-filled" class="w-5 h-5" />
                                Share
                            </button>
                            <button @click="addLike"
                                class="bg-black bg-opacity-75 px-4 py-2 text-white flex items-center gap-2">
                                <Icon icon="fluent:heart-28-filled" class="w-5 h-5"
                                    :class="event?.is_liked ? 'text-red-600' : 'text-white'" />
                                Like
                            </button>
                            <button @click="addAttendance"
                                class="bg-black bg-opacity-75 px-4 py-2 text-white flex items-center gap-2">
                                <Icon icon="entypo:add-user" class="w-5 h-5"
                                    :class="event?.is_attendee ? 'text-red-600' : 'text-white'" />
                                Going
                            </button>
                        </div>
                        <div class="hidden sm:block absolute bottom-0 left-0">
                            <div class="flex flex-col transition-opacity duration-300">
                                <CoreSocialShare v-if="event" :event="event"/>
                                <button @click="addLike"
                                    class="bg-black bg-opacity-25 shiny flex flex-col items-center space-y-2 border-t px-2 text-white">
                                    <Icon icon="fluent:heart-28-filled" class="w-6 h-6 mt-2"
                                        :class="event?.is_liked ? 'text-red-600' : 'text-white'" />
                                    Like
                                </button>
                                <button @click="addAttendance"
                                    class="bg-black bg-opacity-25 shiny flex flex-col items-center space-y-2 border-t px-2 text-white">
                                    <Icon icon="entypo:add-user" class="w-6 h-6 mt-2"
                                        :class="event?.is_attendee ? 'text-red-600' : 'text-white'" />
                                    Going
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="border-l-4 border-gray-100 dark:border-zinc-700 bg-white dark:bg-zinc-900 px-4 py-2">
                        <div class="text-gray-500 dark:text-zinc-100" v-html="event?.description"></div>
                        <div v-if="event?.meeting_link" class="flex items-center space-x-2 py-2">
                            <Icon :icon="getProperRemoteIcon(String(event?.meeting_link?.link))" class="w-6 h-6" />
                            <p @click="openLink(event?.meeting_link?.link)" class="text-sky-500 hover:underline transition duration-150 cursor-pointer">{{ event?.meeting_link?.link }}</p>
                        </div>
                    </div>

                    <div v-if="event?.sponsors && event?.sponsors?.length > 0" class="mt-2">
                        <h3 class="text-2xl font-semibold dark:text-zinc-50">Sponsors</h3>
                        <CoreSponsors :sponsors="event?.sponsors!"/>
                        <div class="mt-8 bg-gradient-to-r from-red-50 to-red-100 rounded-none p-6 space-y-4">
                            <h4 class="text-xl font-semibold dark:text-zinc-100">Interested in Sponsoring?</h4>
                            <p class="text-gray-600 dark:text-zinc-200">
                                Join our growing list of sponsors and get your brand in front of thousands of attendees.
                            </p>
                            <div class="flex flex-col sm:flex-row gap-4 sm:items-center">
                                <button
                                    class="bg-red-600 text-white px-6 py-2 rounded-none hover:bg-red-700 transition-colors duration-200">
                                    View Sponsorship Packages
                                </button>
                                <button class="text-red-600 hover:text-red-700 font-medium">
                                    Contact Our Team →
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="lg:col-span-2">
                    <div v-if="hasDatePassed" class="text-center space-y-3 py-5 bg-white dark:bg-zinc-900 shadow-md">
                        <div class="inline-flex items-center justify-center w-16 h-16">
                            <IconsGameIconsFlag />
                        </div>
                        <div class="space-y-1 flex flex-col items-center">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-zinc-100">Event Completed</h3>
                            <p class="text-gray-500 dark:text-zinc-100">This event has already taken place</p>
                            <EventsReview :eventId="Number(event?.id)" @update:rating="fetchEventDetails" />
                        </div>
                        <EventsRatingHighlights :event="event!"/>
                    </div>
                    <div v-else class="bg-white dark:bg-zinc-900 shadow-md shadow-red-50">
                        <div class="border-b border-gray-100 dark:border-zinc-700 py-2 px-4">
                            <h3 class="text-xl font-semibold dark:text-zinc-50">Event details</h3>
                        </div>

                        <div class="py-2">
                            <CoreCountdown :start-date="new Date(String(event?.end))" :end-date="event?.end" />
                        </div>

                        <div v-if="event !== undefined">
                            <EventsUserCard :user="event.user" />
                        </div>

                        <div class="h-64 sm:h-72">
                            <GoogleMap :api-key="runtimeConfig.public.googleMapsApiKey" mapId="nearby-events-map"
                                class="w-full h-full" :zoom="15" :center="center" mapTypeId="terrain">
                                <CustomMarker :options="{
                                    position: center,
                                    anchorPoint: 'BOTTOM_CENTER',
                                }">
                                    <div style="text-align: center">
                                        <div class="relative shadow-2xl shadow-black animate-bounce">
                                            <button
                                                @click="toDirections" 
                                                class="relative inline-flex items-center justify-center p-2 rounded-none cursor-pointer bg-red-600 text-white">
                                                <Icon icon="fa6-solid:map-location-dot" class="w-6 h-6" />
                                            </button>
                                            <span
                                                class="absolute left-1/2 -bottom-1 transform -translate-x-1/2 w-0 h-0 border-l-[6px] border-l-transparent border-r-[6px] border-r-transparent border-t-[6px] border-t-red-600"></span>
                                        </div>
                                    </div>
                                </CustomMarker>
                                <Circle :center="center" :radius="circleRadius" :options="circleOptions" />
                            </GoogleMap>
                        </div>

                        <div class="py-2" v-if="event?.tiers && event?.tiers?.length > 0">
                            <div class="space-y-4">
                                <h3 class="text-xl font-bold px-4 mt-2">Buy Tickets</h3>
                                <TransitionGroup name="list" tag="div">
                                    <div v-for="tier in event?.tiers" :key="tier.id"
                                        class="transform transition-all duration-300 hover:scale-[1.02]">
                                        <div class="bg-white dark:bg-zinc-900 px-4 py-2 border border-gray-100">
                                            <div class="flex items-center justify-between">
                                                <div class="space-y-2">
                                                    <div class="flex items-center gap-3">
                                                        <h3 class="text-xl font-semibold">MK{{
                                                            tier.price.toLocaleString() }}</h3>
                                                        <span :class="[
                                                            'px-2 py-1 text-xs font-medium rounded-full',
                                                            tier.is_refundable
                                                                ? 'bg-green-100 text-green-500 border border-green-300'
                                                                : 'bg-red-100 text-red-500 border border-red-300',
                                                        ]">
                                                            {{ tier.is_refundable ? 'Refundable' : 'Non-refundable' }}
                                                        </span>
                                                    </div>
                                                    <p class="text-lg font-medium text-gray-500">{{ tier.name }}</p>
                                                    <p class="text-sm text-gray-600">{{ tier.description }}</p>
                                                </div>

                                                <div class="flex items-center space-x-3">
                                                    <button @click="modifyTicketCart(event!!, tier, 'sub')"
                                                        class="w-8 h-8 border-2 hover:bg-red-600 hover:border-red-600 hover:text-white border-gray-200 rounded-full flex items-center justify-center transition duration-150">
                                                        <Icon icon="fa6-solid:minus" class="w-5 h-5" />
                                                    </button>

                                                    <button @click="modifyTicketCart(event!!, tier, 'add')"
                                                        class="w-8 h-8 border-2 border-gray-200 rounded-full flex items-center justify-center hover:bg-red-600 hover:border-red-600 hover:text-white transition duration-150">
                                                        <Icon icon="fa6-solid:plus" class="w-5 h-5" />
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </TransitionGroup>

                                <div class="py-2 px-4">
                                    <h3 class="text-lg font-semibold uppercase">Total</h3>
                                    <div class="bg-gray-50 my-2">
                                        <table class="w-full">
                                            <tbody>
                                                <tr v-for="item in cart" class="border border-gray-100 border-dotted">
                                                    <td class="border-r border-gray-100 border-dotted px-2">{{
                                                        item.tier.name }}
                                                    </td>
                                                    <td class="border-r border-gray-100 border-dotted px-2">{{
                                                        item.quantity }} tickets</td>
                                                    <td class="px-2">MK{{ (item.quantity *
                                                        Number(item.tier.price)).toLocaleString() }}</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <CoreCart :items="cart" @update="fetchEventDetails"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-6 flex flex-col items-start space-y-2.5">
                <h3 class="text-xl font-semibold">Related events</h3>
                <div class="py-1 bg-gray-50">
                    <div class="bg-gray-50 border-2 border-gray-100 rounded-full px-4 py-1 flex items-center gap-2">
                        <img :src="`${runtimeConfig.public.baseUrl}storage/categories/${event?.category?.icon}`"
                            :alt="`${event?.category?.name}-icon`" class="w-5 h-5" />
                        <p>{{ event?.category?.name }}</p>
                    </div>
                </div>
                <div class="w-full sm:grid sm:grid-cols-3 gap-4">
                    <EventsCard v-for="event in relatedEvents" :event="event" />
                </div>
                <NuxtLink :to="`/events?category=${event?.category?.name}`"
                    class="w-full flex items-center justify-center">
                    View More &rarr;
                </NuxtLink>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import type { EventItem, GeoCoordinate, TicketCartItem } from '@/types';
import dayjs from 'dayjs';
import type { Package } from '@/types/ticket';
import { useAuthStore } from '@/store/auth';

definePageMeta({
    layout: "default",
});

const center = ref<GeoCoordinate>({
    lat: -13.991627856237,
    lng: 33.768879386812806,
});
const { $toast }: any = useNuxtApp();
const { start, finish } = useLoadingIndicator();
const authStore = useAuthStore();
const loading = ref<boolean>(false);
const title = ref<string>("");
const httpClient = useHttpClient();
const runtimeConfig = useRuntimeConfig();
const circleRadius = ref<number>(100000);
const event = ref<EventItem>();
const isAttending = ref<boolean>(event?.value?.is_attending || false);
const relatedEvents = ref<EventItem[]>([]);
const route = useRoute();

useHead({
    title: computed(() => `${title.value} - EventaHub Malawi`)
});

const circleOptions = ref({
    fillColor: "red",
    fillOpacity: 0.35,
    strokeColor: "#FF0000",
    strokeOpacity: 0.8,
    strokeWeight: 2,
});
const cart = ref<TicketCartItem[]>([]);

const parseCoordinates = (lat: any, lng: any): GeoCoordinate => {
    const parsedLat = Number(lat);
    const parsedLng = Number(lng);

    if (isNaN(parsedLat) || isNaN(parsedLng)) {
        return center.value;
    }

    return {
        lat: parsedLat,
        lng: parsedLng
    };
};

const hasDatePassed = computed((): boolean => {
    const startDate = new Date(String(event.value?.end));
    return !startDate || startDate.getTime() < Date.now();
});

function modifyTicketCart(event: EventItem, tier: Package, method: 'add' | 'sub'): void {
    const cartItem = cart.value.find(
        (item) => item.event.id === event.id && item.tier.id === tier.id
    );
    if (cartItem !== undefined) {
        if (method === 'add') {
            cartItem.quantity += 1;
        } else {
            cartItem.quantity -= 1;
            if (cartItem.quantity <= 0) {
                const itemIndex = cart.value.findIndex(
                    (item) => item.event.id === event.id && item.tier.id === tier.id
                );
                if (itemIndex !== -1) {
                    cart.value.splice(itemIndex, 1);
                    return;
                }
            }
        }

        cartItem.total = cartItem.quantity * Number(cartItem.tier.price);
    } else {
        if (method === 'add') {
            cart.value.push({
                sn: cart.value.length + 1,
                quantity: 1,
                tier: tier,
                event: event,
                total: Number(tier.price)
            });
        }
    }
}

const fetchEventDetails = async (): Promise<void> => {
    loading.value = true;
    start();
    try {
        const response: any = await httpClient.get(`${ENDPOINTS.EVENTS.SLUG}/${route.params.slug}?_def=${authStore?.user?.id || 0}`);
        if (response) {
            event.value = response.event;
            center.value = parseCoordinates(
                response.event.latitude,
                response.event.longitude
            );
            relatedEvents.value = response.related_events;
            title.value = response.event.title;
            isAttending.value = response.event.is_attending;
            loading.value = false;
        }
    } catch (e: any) {
        console.error(e);
        $toast.error("Failed to fetch event details");
    } finally {
        loading.value = false;
        finish();
    }
}

const addAttendance = async (): Promise<void> => {
    start();
    try {
        const response: { message: string; } = await httpClient.post(ENDPOINTS.EVENTS.ATTENDANCE, {
            event_id: event.value?.id
        });
        if (response) {
            $toast.success(response.message);
            isAttending.value = true;
            fetchEventDetails();
        }
    } catch (e: any) {
        $toast.warn(e.message.error);
        isAttending.value = false;
    } finally {
        finish();
    }
}

const addLike = async (): Promise<void> => {
    try {
        const response: { message: string; } = await httpClient.post(ENDPOINTS.EVENTS.LIKE, {
            event_id: event.value?.id
        });
        if (response) {
            $toast.success(response.message);
            fetchEventDetails();
        }
    } catch (e: any) {
        $toast.warn(e.message.error);
    }
}

const toDirections = (): void => {
    navigateTo(`/events/directions?location=${event?.value?.location}&lat=${center.value.lat}&lng=${center.value.lng}`)
}

const getProperRemoteIcon = (link: string): string => {
  const iconMap = {
    'zoom.us': 'logos:zoom',
    'teams.microsoft.com': 'logos:microsoft-teams',
    'meet.google.com': 'logos:google-meet',
  };
  let icon = "hugeicons:globe";
  for (const [keyword, mappedIcon] of Object.entries(iconMap)) {
    console.log(keyword, link)
    if (link.includes(keyword)) {
      return mappedIcon;
    }
  }
  return icon;
}

const openLink = (link: string): void => {
    const document = window.document;
    const url = new URL(link);
    const newWindow = document.createElement('a');
    newWindow.target = '_blank';
    newWindow.href = url.href;
    document.body.appendChild(newWindow);
    newWindow.click();
    document.body.removeChild(newWindow);
}

onMounted(() => {
    fetchEventDetails();
});
</script>

<style lang="scss" scoped></style>