<template>
  <div class="dashboard-bg-main min-h-screen">
    <div class="p-4 sm:p-6">
      <div class="mb-6">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 class="text-2xl font-bold dashboard-text-primary">Profile</h1>
            <p class="mt-1 text-sm dashboard-text-muted">Manage your vendor profile information</p>
          </div>
          <div class="mt-4 md:mt-0">
            <button @click="saveProfile" :disabled="isLoading || isSaving"
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium text-white dashboard-bg-primary hover:bg-red-700 dashboard-transition dashboard-shadow disabled:opacity-50 disabled:cursor-not-allowed">
              <CoreLoader v-if="isSaving" :height="100" :width="100"/>
              {{ isSaving ? 'Saving...' : 'Save Changes' }}
            </button>
          </div>
        </div>
      </div>

      <div v-if="isLoading" class="dashboard-bg-card dashboard-shadow overflow-hidden flex flex-col items-center justify-center mb-6 p-8">
        <CoreLoader :height="50" :width="50"/>
        <p class="text-center mt-4 text-gray-600">Loading profile...</p>
      </div>

      <template v-else>
        <div class="dashboard-bg-card dashboard-shadow overflow-hidden mb-6">
          <div class="px-6 py-4 dashboard-border border-b">
            <h3 class="text-lg font-medium dashboard-text-primary">Basic Information</h3>
          </div>
          <div class="p-6">
            <div class="flex flex-col md:flex-row">
              <div class="md:w-1/3 mb-6 md:mb-0">
                <div class="flex flex-col items-center">
                  <div class="relative">
                    <img :src="profileImageUrl" alt="Profile picture" class="w-40 h-40 object-cover dashboard-shadow rounded-full" />
                    <label for="profile-image-upload"
                      class="absolute rounded-full bottom-0 right-0 dashboard-bg-primary text-white p-2 cursor-pointer dashboard-shadow">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M4 5a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V7a2 2 0 00-2-2h-1.586a1 1 0 01-.707-.293l-1.121-1.121A2 2 0 0011.172 3H8.828a2 2 0 00-1.414.586L6.293 4.707A1 1 0 015.586 5H4zm6 9a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd" />
                      </svg>
                      <input type="file" id="profile-image-upload" class="hidden" accept="image/*" @change="handleImageUpload" />
                    </label>
                  </div>
                  <p class="mt-4 text-sm dashboard-text-muted">Click to upload a new photo</p>
                </div>
              </div>
              <div class="md:w-2/3">
                <FormKit :actions="false" #default="{ }" type="form" id="profileForm" v-model="formData" @submit="saveProfile">
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormKit type="text" name="name" label="Business Name" validation="required" />
                    <FormKit type="email" name="business_email" label="Business Email" validation="required|email" />
                    <FormKit type="tel" name="phone" label="Phone Number" validation="required" />
                    <FormKit type="text" name="languages" label="Languages" validation="required" help="Separate languages with commas" />
                    <FormKit type="select" name="is_available" label="Availability Status" :options="[
                      { label: 'Available for Bookings', value: true },
                      { label: 'Not Available', value: false }
                    ]" validation="required" />
                    <div class="md:col-span-2">
                      <FormKit type="textarea" name="bio" label="Bio" validation="required" rows="4" />
                    </div>
                  </div>
                </FormKit>
              </div>
            </div>
          </div>
        </div>

        <div class="dashboard-bg-card dashboard-shadow overflow-hidden mb-6">
          <div class="px-6 py-4 dashboard-border border-b">
            <h3 class="text-lg font-medium dashboard-text-primary">Location</h3>
          </div>
          <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormKit type="text" name="location" label="Location" validation="required" help="City, Country or full address" v-model="formData.location" />
            </div>
          </div>
        </div>

        <div class="dashboard-bg-card dashboard-shadow overflow-hidden">
          <div class="px-6 py-4 dashboard-border border-b">
            <h3 class="text-lg font-medium dashboard-text-primary">Social Media & Website</h3>
          </div>
          <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormKit type="url" name="website" label="Website" v-model="formData.website" />
              <FormKit type="url" name="facebook" label="Facebook" v-model="formData.facebook" />
              <FormKit type="url" name="instagram" label="Instagram" v-model="formData.instagram" />
              <FormKit type="url" name="twitter" label="Twitter" v-model="formData.twitter" />
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { useVendorStore } from '@/store/vendor';

definePageMeta({
  layout: 'vendor-dashboard'
});

const vendorStore = useVendorStore();
const httpClient = useHttpClient();
const runtimeConfig = useRuntimeConfig();
const { $toast }: any = useNuxtApp();

const isLoading = ref(true);
const isSaving = ref(false);
const profileImage = ref<File | null>(null);
const profileImagePreview = ref<string | null>(null);

const formData = reactive({
  name: '',
  bio: '',
  business_email: '',
  phone: '',
  languages: '',
  is_available: true,
  location: '',
  website: '',
  facebook: '',
  instagram: '',
  twitter: ''
});

const profileImageUrl = computed(() => {
  if (profileImagePreview.value) {
    return profileImagePreview.value;
  }

  if (vendorStore.details?.logo) {
    return `${runtimeConfig.public.baseUrl}storage/${vendorStore.details.logo}`;
  }

  if (vendorStore.details?.user?.avatar) {
    return `${runtimeConfig.public.baseUrl}storage/avatars/${vendorStore.details.user.avatar}`;
  }

  return `https://ui-avatars.com/api/?name=${encodeURIComponent(formData.name || 'Vendor')}&size=200&background=random`;
});

const handleImageUpload = (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files[0]) {
    const file = input.files[0];
    profileImage.value = file;

    const reader = new FileReader();
    reader.onload = (e) => {
      profileImagePreview.value = e.target?.result as string;
    };
    reader.readAsDataURL(file);
  }
};

const loadVendorProfile = async () => {
  isLoading.value = true;
  try {
    await vendorStore.fetchVendorDetails();

    if (vendorStore.details) {
      formData.name = vendorStore.details.name || '';
      formData.business_email = vendorStore.details.business_email || '';
      formData.phone = vendorStore.details.phone || '';
      formData.languages = vendorStore.details.languages || '';
      formData.bio = vendorStore.details.bio || '';
      formData.is_available = vendorStore.details.is_available === 1;
      formData.location = vendorStore.details.location || '';
      formData.website = vendorStore.details.website || '';
      formData.facebook = vendorStore.details.facebook || '';
      formData.instagram = vendorStore.details.instagram || '';
      formData.twitter = vendorStore.details.twitter || '';
    }
  } catch (error) {
    $toast.error('Failed to load vendor profile');
    console.error('Error loading vendor profile:', error);
  } finally {
    isLoading.value = false;
  }
};

const saveProfile = async () => {
  isSaving.value = true;
  try {
    if (!vendorStore.details?.id) {
      throw new Error('Vendor ID not found');
    }

    const formDataObj = new FormData();

    formDataObj.append('name', formData.name);
    formDataObj.append('bio', formData.bio);
    formDataObj.append('location', formData.location);
    formDataObj.append('languages', formData.languages);
    formDataObj.append('phone', formData.phone);
    formDataObj.append('business_email', formData.business_email);
    formDataObj.append('is_available', formData.is_available.toString());

    if (formData.website) formDataObj.append('website', formData.website);
    if (formData.facebook) formDataObj.append('facebook', formData.facebook);
    if (formData.instagram) formDataObj.append('instagram', formData.instagram);
    if (formData.twitter) formDataObj.append('twitter', formData.twitter);

    if (profileImage.value) {
      formDataObj.append('logo', profileImage.value);
    }

    const response = await httpClient.post(`/vendors/update/${vendorStore.details.id}`, formDataObj, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });

    if (response) {
      $toast.success('Profile updated successfully');
      await vendorStore.fetchVendorDetails();
    }
  } catch (error: any) {
    $toast.error(error.message || 'Failed to update profile');
    console.error('Error updating profile:', error);
  } finally {
    isSaving.value = false;
  }
};

onMounted(() => {
  loadVendorProfile();
});
</script>
