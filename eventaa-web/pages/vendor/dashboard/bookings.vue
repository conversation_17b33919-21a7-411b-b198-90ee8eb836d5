<template>
  <div class="dashboard-bg-main min-h-screen">
    <div class="p-4 sm:p-6">
      <div class="mb-6">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 class="text-2xl font-bold dashboard-text-primary">Bookings</h1>
            <p class="mt-1 text-sm dashboard-text-muted">Manage all your service bookings in one place</p>
          </div>
          <div class="mt-4 md:mt-0 flex space-x-3">
            <div class="relative">
              <select v-model="filterStatus" :disabled="isLoading"
                class="block w-full pl-3 pr-10 py-2 dashboard-input dashboard-border dashboard-text-primary focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm">
                <option value="All">All Bookings</option>
                <option value="pending">Pending</option>
                <option value="approved">Confirmed</option>
                <option value="completed">Completed</option>
                <option value="cancelled">Cancelled</option>
                <option value="rejected">Rejected</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      <div class="mb-6">
        <div class="dashboard-border border-b">
          <nav class="-mb-px flex space-x-8 overflow-x-auto">
            <button v-for="tab in tabs" :key="tab.name" @click="setActiveTab(tab.name)" :disabled="isLoading" :class="[
              activeTab === tab.name
                ? 'border-red-500 text-red-600 dark:text-red-400'
                : 'border-transparent dashboard-text-muted hover:dashboard-text-secondary hover:border-gray-300 dark:hover:border-gray-600',
              isLoading ? 'opacity-50 cursor-not-allowed' : '',
              'whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm dashboard-transition'
            ]">
              {{ tab.name }}
              <span :class="[
                activeTab === tab.name
                  ? 'bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-400'
                  : 'dashboard-bg-hover dashboard-text-primary',
                'ml-2 py-0.5 px-2.5 text-xs font-medium'
              ]">
                <span v-if="isLoading" class="inline-block w-3 h-3 animate-pulse bg-current rounded-full"></span>
                <span v-else>{{ getTabCount(tab.name) }}</span>
              </span>
            </button>
          </nav>
        </div>
      </div>

      <div class="dashboard-bg-card dashboard-shadow overflow-hidden mb-6">
        <div class="px-6 py-4 dashboard-border border-b flex justify-between items-center">
          <h3 class="text-lg font-medium dashboard-text-primary">{{ currentMonthYear }}</h3>
          <div class="flex space-x-2">
            <button @click="previousMonth"
              class="inline-flex items-center px-3 py-1.5 dashboard-border dashboard-bg-card dashboard-text-secondary hover:dashboard-bg-hover dashboard-transition text-sm font-medium">
              <Icon icon="heroicons:arrow-left" class="h-4 w-4 mr-1" />
              Previous
            </button>
            <button @click="goToToday"
              class="inline-flex items-center px-3 py-1.5 dashboard-border dashboard-bg-card dashboard-text-secondary hover:dashboard-bg-hover dashboard-transition text-sm font-medium">
              Today
            </button>
            <button @click="nextMonth"
              class="inline-flex items-center px-3 py-1.5 dashboard-border dashboard-bg-card dashboard-text-secondary hover:dashboard-bg-hover dashboard-transition text-sm font-medium">
              Next
              <Icon icon="heroicons:arrow-right" class="h-4 w-4 ml-1" />
            </button>
          </div>
        </div>
        <div class="p-6">
          <div v-if="isCalendarLoading" class="flex justify-center items-center py-20">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 dashboard-border-primary"></div>
          </div>

          <div v-else class="dashboard-border">
            <div class="grid grid-cols-7 gap-px dashboard-border border-b">
              <div v-for="day in ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']" :key="day"
                class="dashboard-bg-hover py-2 text-center text-sm font-semibold dashboard-text-secondary">
                {{ day }}
              </div>
            </div>
            <div class="grid grid-cols-7 gap-px">
              <div v-for="(date, index) in calendarDays" :key="index" :class="[
                'dashboard-bg-card p-3 h-32 overflow-y-auto',
                date.isCurrentMonth ? '' : 'dashboard-bg-hover',
                date.isToday ? 'bg-red-50 dark:bg-red-900/20' : ''
              ]" @click="date.bookings.length > 0 ? showBookingDetails(date.bookings[0]) : null">
                <div class="flex justify-between items-center">
                  <span :class="[
                    'text-sm',
                    date.isCurrentMonth ? 'dashboard-text-primary' : 'dashboard-text-light',
                    date.isToday ? 'font-bold text-red-600 dark:text-red-400' : ''
                  ]">
                    {{ date.day }}
                  </span>
                  <span v-if="date.bookings.length > 0"
                    class="flex h-5 w-5 items-center justify-center dashboard-bg-primary text-white text-xs">
                    {{ date.bookings.length }}
                  </span>
                </div>

                <div v-for="booking in date.bookings.slice(0, 2)" :key="booking.id"
                  class="mt-2 p-1 text-xs cursor-pointer dashboard-transition" :class="getStatusClass(booking.status)"
                  @click.stop="showBookingDetails(booking)">
                  <div class="font-medium">{{ booking.vendorService?.service?.name || 'Service' }}</div>
                  <div>{{ dayjs(booking.booking_from).format('h:mm A') }}</div>
                </div>

                <div v-if="date.bookings.length > 2" class="mt-1 text-xs text-center dashboard-text-muted">
                  + {{ date.bookings.length - 2 }} more
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="dashboard-bg-card dashboard-shadow overflow-hidden">
        <div class="px-6 py-4 dashboard-border border-b flex justify-between items-center">
          <h3 class="text-lg font-medium dashboard-text-primary">{{ activeTab }} Bookings</h3>
          <div v-if="!isLoading && filteredBookings.length > 0" class="flex space-x-2">
            <button @click="exportToPDF" class="inline-flex items-center px-3 py-1.5 dashboard-border dashboard-bg-card dashboard-text-secondary hover:dashboard-bg-hover dashboard-transition text-sm font-medium">
              <Icon icon="heroicons:document-arrow-down" class="h-4 w-4 mr-1" />
              Export PDF
            </button>
            <json-excel
              class="inline-flex items-center px-3 py-1.5 dashboard-border dashboard-bg-card dashboard-text-secondary hover:dashboard-bg-hover dashboard-transition text-sm font-medium"
              :data="exportData"
              :fields="exportFields"
              :name="`${activeTab}-bookings-${dayjs().format('YYYY-MM-DD')}.xlsx`"
              :before-generate="beforeExport"
              :before-finish="onExportDone">
              <Icon icon="heroicons:table-cells" class="h-4 w-4 mr-1" />
              Export Excel
            </json-excel>
          </div>
        </div>

        <div v-if="isLoading" class="flex justify-center items-center py-20">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 dashboard-border-primary"></div>
        </div>

        <div v-else-if="filteredBookings.length === 0" class="py-20 text-center">
          <div class="mx-auto h-12 w-12 text-gray-400">
            <Icon icon="heroicons:calendar" class="h-12 w-12" />
          </div>
          <h3 class="mt-2 text-sm font-medium dashboard-text-primary">No bookings</h3>
          <p class="mt-1 text-sm dashboard-text-muted">No bookings found for the selected filter.</p>
        </div>

        <div v-else class="overflow-x-auto">
          <table class="min-w-full divide-y dashboard-border-light">
            <thead class="dashboard-bg-hover">
              <tr>
                <th scope="col"
                  class="px-6 py-3 text-left text-xs font-medium dashboard-text-muted uppercase tracking-wider">
                  Client
                </th>
                <th scope="col"
                  class="px-6 py-3 text-left text-xs font-medium dashboard-text-muted uppercase tracking-wider">
                  Service
                </th>
                <th scope="col"
                  class="px-6 py-3 text-left text-xs font-medium dashboard-text-muted uppercase tracking-wider">
                  Date & Time
                </th>
                <th scope="col"
                  class="px-6 py-3 text-left text-xs font-medium dashboard-text-muted uppercase tracking-wider">
                  Status
                </th>
                <th scope="col"
                  class="px-6 py-3 text-left text-xs font-medium dashboard-text-muted uppercase tracking-wider">
                  Amount
                </th>
                <th scope="col"
                  class="px-6 py-3 text-right text-xs font-medium dashboard-text-muted uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody class="dashboard-bg-card divide-y dashboard-border-light">
              <tr v-for="booking in filteredBookings" :key="booking.id"
                class="hover:dashboard-bg-hover dashboard-transition">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10">
                      <img class="h-10 w-10 rounded-full" :src="`${runtimeConfig.public.baseUrl}storage/avatars/${booking.user?.avatar}`" alt="user-avatar" />
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium dashboard-text-primary">{{ booking.user?.name }}</div>
                      <div class="text-sm dashboard-text-muted">{{ booking.user?.email }}</div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm dashboard-text-primary">{{ booking.vendor_service?.service?.name }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm dashboard-text-primary">{{ dayjs(booking.booking_from).format('MMM D, YYYY') }}</div>
                  <div class="text-sm dashboard-text-muted">{{ dayjs(booking.booking_from).format('h:mm A') }} - {{ dayjs(booking.booking_to).format('h:mm A') }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                    :class="getStatusClass(booking.status)">
                    {{ booking.status }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm dashboard-text-muted">
                  ${{ booking.total_price }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div class="flex justify-end space-x-2">
                    <button @click="showBookingDetails(booking)"
                      class="dashboard-text-brand hover:text-red-800 dashboard-transition">View</button>
                    <button @click="showDeleteDialog(booking.id)"
                      class="dashboard-text-brand hover:text-red-800 dashboard-transition">Delete</button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <div v-if="!isLoading && filteredBookings.length > 0" class="px-6 py-3 dashboard-bg-hover flex items-center justify-between">
          <div class="flex-1 flex justify-between sm:hidden">
            <button @click="prevPage" :disabled="currentPage === 1"
              class="relative inline-flex items-center px-4 py-2 dashboard-border dashboard-bg-card dashboard-text-secondary hover:dashboard-bg-hover dashboard-transition text-sm font-medium"
              :class="{ 'opacity-50 cursor-not-allowed': currentPage === 1 }">
              Previous
            </button>
            <button @click="nextPage" :disabled="currentPage >= totalPages"
              class="ml-3 relative inline-flex items-center px-4 py-2 dashboard-border dashboard-bg-card dashboard-text-secondary hover:dashboard-bg-hover dashboard-transition text-sm font-medium"
              :class="{ 'opacity-50 cursor-not-allowed': currentPage >= totalPages }">
              Next
            </button>
          </div>
          <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p class="text-sm dashboard-text-secondary">
                Showing <span class="font-medium">{{ paginationStart }}</span> to <span class="font-medium">{{
                  paginationEnd }}</span> of <span class="font-medium">{{ totalFilteredBookings }}</span> results
              </p>
            </div>
            <div v-if="totalPages > 1">
              <nav class="relative z-0 inline-flex -space-x-px" aria-label="Pagination">
                <button @click="prevPage" :disabled="currentPage === 1"
                  class="relative inline-flex items-center px-2 py-2 dashboard-border dashboard-bg-card dashboard-text-secondary hover:dashboard-bg-hover dashboard-transition text-sm font-medium"
                  :class="{ 'opacity-50 cursor-not-allowed': currentPage === 1 }">
                  <span class="sr-only">Previous</span>
                  <Icon icon="heroicons:chevron-left" class="h-5 w-5" />
                </button>
                <button v-for="page in paginationPages" :key="page" @click="goToPage(page)" :class="[
                  page === currentPage ? 'bg-red-50 text-red-600 dark:bg-red-900/20 dark:text-red-400 border-red-500' : 'dashboard-bg-card dashboard-text-secondary',
                  'relative inline-flex items-center px-4 py-2 dashboard-border text-sm font-medium dashboard-transition'
                ]">
                  {{ page }}
                </button>
                <button @click="nextPage" :disabled="currentPage >= totalPages"
                  class="relative inline-flex items-center px-2 py-2 dashboard-border dashboard-bg-card dashboard-text-secondary hover:dashboard-bg-hover dashboard-transition text-sm font-medium"
                  :class="{ 'opacity-50 cursor-not-allowed': currentPage >= totalPages }">
                  <span class="sr-only">Next</span>
                  <Icon icon="heroicons:chevron-right" class="h-5 w-5" />
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <VendorDashboardBookingDetailsDialog :booking="selectedBooking" :is-open="isBookingDetailsOpen"
    @close="closeBookingDetails" @update-status="updateBookingStatus" />

  <TransitionRoot appear :show="isDeleteDialogOpen" as="template">
    <Dialog as="div" @close="isDeleteDialogOpen = false" class="relative z-10">
      <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
        leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
        <div class="fixed inset-0 bg-black bg-opacity-25 dark:bg-opacity-40" />
      </TransitionChild>

      <div class="fixed inset-0 overflow-y-auto">
        <div class="flex min-h-full items-center justify-center p-4 text-center">
          <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
            enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95">
            <DialogPanel
              class="w-full max-w-md transform overflow-hidden dashboard-bg-card p-6 text-left align-middle shadow-xl transition-all dashboard-border">
              <DialogTitle as="h3" class="text-lg font-medium leading-6 dashboard-text-primary">
                Confirm Deletion
              </DialogTitle>
              <div class="mt-2">
                <p class="text-sm dashboard-text-secondary">
                  Are you sure you want to delete this booking? This action cannot be undone.
                </p>
              </div>

              <div class="mt-4 flex justify-end space-x-3">
                <button type="button"
                  class="inline-flex justify-center px-4 py-2 text-sm font-medium dashboard-text-secondary dashboard-bg-card dashboard-border dashboard-transition hover:dashboard-bg-hover"
                  @click="isDeleteDialogOpen = false">
                  Cancel
                </button>
                <button type="button"
                  class="inline-flex justify-center px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus-visible:ring-2 focus-visible:ring-red-500 focus-visible:ring-offset-2"
                  @click="confirmDeleteBooking">
                  Delete
                </button>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import dayjs from 'dayjs';
import { useVendorStore } from '@/store/vendor';
import { useVendorBookingsStore } from '@/store/vendorBookings';
import { useVendorServicesStore } from '@/store/vendorServices';
import { handleError } from '@/utils/errorHandler';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import JsonExcel from 'vue3-json-excel';
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue';

definePageMeta({
  layout: 'vendor-dashboard'
});

interface CalendarDay {
  date: Date;
  day: number;
  isCurrentMonth: boolean;
  isToday: boolean;
  bookings: any[];
}

const vendorStore = useVendorStore();
const vendorBookingsStore = useVendorBookingsStore();
const vendorServicesStore = useVendorServicesStore();
const runtimeConfig = useRuntimeConfig();
const { $toast }: any = useNuxtApp();

const isLoading = ref<boolean>(true);
const isCalendarLoading = ref<boolean>(true);

const exportFields = {
  'Client Name': 'clientName',
  'Client Email': 'clientEmail',
  'Service': 'serviceName',
  'Date': 'bookingDate',
  'Time': 'bookingTime',
  'Status': 'status',
  'Amount': 'amount'
};

const exportData = computed(() => {
  return filteredBookings.value.map(booking => ({
    clientName: booking.user?.name || 'N/A',
    clientEmail: booking.user?.email || 'N/A',
    serviceName: booking.vendor_service?.service?.name || 'N/A',
    bookingDate: dayjs(booking.booking_from).format('MMM D, YYYY'),
    bookingTime: `${dayjs(booking.booking_from).format('h:mm A')} - ${dayjs(booking.booking_to).format('h:mm A')}`,
    status: booking.status,
    amount: booking.total_price
  }));
});

const tabs = [
  { name: 'All', href: '#', current: true },
  { name: 'pending', href: '#', current: false },
  { name: 'approved', href: '#', current: false },
  { name: 'completed', href: '#', current: false },
  { name: 'cancelled', href: '#', current: false },
];

const statusClasses = {
  'pending': 'bg-yellow-100 text-yellow-800',
  'approved': 'bg-green-100 text-green-800',
  'completed': 'bg-blue-100 text-blue-800',
  'cancelled': 'bg-red-100 text-red-800',
  'rejected': 'bg-red-100 text-red-800',
};

const darkStatusClasses = {
  'pending': 'dark:bg-yellow-900 dark:text-yellow-300',
  'approved': 'dark:bg-green-900 dark:text-green-300',
  'completed': 'dark:bg-blue-900 dark:text-blue-300',
  'cancelled': 'dark:bg-red-900 dark:text-red-300',
  'rejected': 'dark:bg-red-900 dark:text-red-300',
};

const getStatusClass = (status: string) => {
  return `${statusClasses[status as keyof typeof statusClasses] || ''} ${darkStatusClasses[status as keyof typeof darkStatusClasses] || ''}`;
};

const selectedBooking = ref<any | null>(null);
const isBookingDetailsOpen = ref(false);

const activeTab = ref('All');
const filterStatus = ref('All');

const currentPage = ref(1);
const itemsPerPage = 10;

const currentMonth = ref(new Date());

const filteredBookings = computed(() => {
  if (activeTab.value === 'All') {
    return vendorBookingsStore.bookings;
  }
  return vendorBookingsStore.getBookingsByStatus(activeTab.value);
});

const totalFilteredBookings = computed(() => filteredBookings.value.length);
const totalPages = computed(() => Math.ceil(totalFilteredBookings.value / itemsPerPage));

const paginationStart = computed(() => {
  if (totalFilteredBookings.value === 0) return 0;
  return (currentPage.value - 1) * itemsPerPage + 1;
});

const paginationEnd = computed(() => {
  return Math.min(currentPage.value * itemsPerPage, totalFilteredBookings.value);
});

const paginationPages = computed(() => {
  const pages = [];
  const maxVisiblePages = 3;

  if (totalPages.value <= maxVisiblePages) {
    for (let i = 1; i <= totalPages.value; i++) {
      pages.push(i);
    }
  } else {
    let startPage = Math.max(1, currentPage.value - 1);
    let endPage = Math.min(totalPages.value, startPage + maxVisiblePages - 1);

    if (endPage - startPage < maxVisiblePages - 1) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
  }

  return pages;
});

const currentMonthYear = computed(() => {
  return dayjs(currentMonth.value).format('MMMM YYYY');
});

const calendarDays = computed(() => {
  const days: CalendarDay[] = [];
  const today = new Date();

  const firstDay = new Date(currentMonth.value.getFullYear(), currentMonth.value.getMonth(), 1);
  const lastDay = new Date(currentMonth.value.getFullYear(), currentMonth.value.getMonth() + 1, 0);
  const firstDayOfWeek = firstDay.getDay();
  const prevMonthLastDay = new Date(currentMonth.value.getFullYear(), currentMonth.value.getMonth(), 0).getDate();

  for (let i = firstDayOfWeek - 1; i >= 0; i--) {
    const date = new Date(currentMonth.value.getFullYear(), currentMonth.value.getMonth() - 1, prevMonthLastDay - i);
    days.push({
      date,
      day: date.getDate(),
      isCurrentMonth: false,
      isToday: isSameDay(date, today),
      bookings: getBookingsForDate(date)
    });
  }

  for (let i = 1; i <= lastDay.getDate(); i++) {
    const date = new Date(currentMonth.value.getFullYear(), currentMonth.value.getMonth(), i);
    days.push({
      date,
      day: i,
      isCurrentMonth: true,
      isToday: isSameDay(date, today),
      bookings: getBookingsForDate(date)
    });
  }

  const remainingDays = 42 - days.length;
  for (let i = 1; i <= remainingDays; i++) {
    const date = new Date(currentMonth.value.getFullYear(), currentMonth.value.getMonth() + 1, i);
    days.push({
      date,
      day: i,
      isCurrentMonth: false,
      isToday: isSameDay(date, today),
      bookings: getBookingsForDate(date)
    });
  }

  return days;
});

function isSameDay(date1: Date, date2: Date): boolean {
  return date1.getDate() === date2.getDate() &&
    date1.getMonth() === date2.getMonth() &&
    date1.getFullYear() === date2.getFullYear();
}

function getBookingsForDate(date: Date): any[] {
  return vendorBookingsStore.bookings.filter(booking => {
    const bookingDate = dayjs(booking.booking_from).toDate();
    return isSameDay(bookingDate, date);
  });
}

function getTabCount(tabName: string): number {
  const tabCountMap: Record<string, number> = {
    'All': vendorBookingsStore.totalBookings,
    'pending': vendorBookingsStore.pendingBookings,
    'approved': vendorBookingsStore.confirmedBookings,
    'completed': vendorBookingsStore.completedBookings,
    'cancelled': vendorBookingsStore.cancelledBookings
  };
  return tabCountMap[tabName] ?? 0;
}

function setActiveTab(tabName: string): void {
  activeTab.value = tabName;
  currentPage.value = 1;
}

function prevPage() {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
}

function nextPage() {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
}

function goToPage(page: number) {
  currentPage.value = page;
}

function previousMonth() {
  currentMonth.value = new Date(currentMonth.value.getFullYear(), currentMonth.value.getMonth() - 1, 1);
}

function nextMonth() {
  currentMonth.value = new Date(currentMonth.value.getFullYear(), currentMonth.value.getMonth() + 1, 1);
}

function goToToday() {
  currentMonth.value = new Date();
}

function showBookingDetails(booking: any) {
  selectedBooking.value = booking;
  isBookingDetailsOpen.value = true;
}

function closeBookingDetails() {
  isBookingDetailsOpen.value = false;
  selectedBooking.value = null;
}

const isDeleteDialogOpen = ref(false);
const bookingToDelete = ref<number | null>(null);

function showDeleteDialog(id: number) {
  bookingToDelete.value = id;
  isDeleteDialogOpen.value = true;
}

async function confirmDeleteBooking() {
  try {
    if (!bookingToDelete.value) return;

    const result = await vendorBookingsStore.deleteBooking(bookingToDelete.value);
    if (result) {
      $toast.success('Booking deleted successfully');
    } else if (vendorBookingsStore.error) {
      handleError({ message: vendorBookingsStore.error }, $toast, 'Failed to delete booking');
    } else {
      throw new Error('Failed to delete booking');
    }
    isDeleteDialogOpen.value = false;
    bookingToDelete.value = null;
  } catch (error: any) {
    handleError(error, $toast, 'An error occurred while deleting the booking');
  }
}

function updateBookingStatus(data: { id: number, status: string }) {
  $toast.success(`Booking status updated to ${data.status}`);
  loadBookings();
}

async function loadVendorData() {
  try {
    if (!vendorStore.details) {
      await vendorStore.fetchVendorDetails();
    }
  } catch (error: any) {
    handleError(error, $toast, 'Failed to load vendor details');
  }
}

async function loadServices() {
  try {
    await vendorServicesStore.fetchVendorServices();
  } catch (error: any) {
    handleError(error, $toast, 'Failed to load vendor services');
  }
}

async function loadBookings() {
  try {
    isLoading.value = true;
    const result = await vendorBookingsStore.fetchVendorBookings();
    if (!result && vendorBookingsStore.error) {
      handleError({ message: vendorBookingsStore.error }, $toast, 'Failed to load bookings');
    }
  } catch (error: any) {
    handleError(error, $toast, 'Failed to load bookings');
  } finally {
    isLoading.value = false;
    isCalendarLoading.value = false;
  }
}

function beforeExport(): void {
  $toast.info('Preparing Excel export...');
}

function onExportDone(): void {
  $toast.success('Excel file exported successfully');
}

function exportToPDF(): void {
  try {
    $toast.info('Generating PDF...');

    const doc = new jsPDF();
    const tableColumn = ["Client", "Service", "Date", "Booking From", "Booking To", "Status", "Amount"];
    const tableRows: any[] = [];

    filteredBookings.value.forEach(booking => {
      const bookingData = [
        booking.user?.name || 'N/A',
        booking.vendor_service?.service?.name || 'N/A',
        dayjs(booking.booking_from).format('MMM D, YYYY'),
        dayjs(booking.booking_from).format('h:mm A'),
        dayjs(booking.booking_to).format('h:mm A'),
        booking.status,
        `$${booking.total_price}`
      ];
      tableRows.push(bookingData);
    });

    doc.setFontSize(22);
    doc.setTextColor(220, 53, 69);
    doc.text("EventaHub", 14, 15);

    if (vendorStore.details?.name) {
      doc.setFontSize(14);
      doc.setTextColor(0, 0, 0);
      doc.text(`${vendorStore.details.name}`, 14, 22);
    }

    doc.setFontSize(15);
    doc.setTextColor(0, 0, 0);
    doc.text(`${activeTab.value} Bookings Report`, 14, 30);

    doc.setFontSize(11);
    doc.setTextColor(100, 100, 100);
    doc.text(`Generated on: ${dayjs().format('MMMM D, YYYY')} at ${dayjs().format('h:mm A')}`, 14, 36);
    doc.text(`Total Bookings: ${filteredBookings.value.length}`, 14, 42);
    doc.text(`Filter: ${activeTab.value} bookings`, 14, 48);

    autoTable(doc, {
      head: [tableColumn],
      body: tableRows,
      startY: 55,
      theme: 'grid',
      styles: {
        fontSize: 10,
        cellPadding: 3,
        overflow: 'linebreak',
      },
      headStyles: {
        fillColor: [220, 53, 69],
        textColor: 255,
        fontStyle: 'bold',
      },
      alternateRowStyles: {
        fillColor: [245, 245, 245]
      }
    });

    const pageCount = (doc as any).internal.getNumberOfPages();
    for (let i = 1; i <= pageCount; i++) {
      doc.setPage(i);
      doc.setFontSize(10);
      doc.setTextColor(150, 150, 150);
      const pageSize = (doc as any).internal.pageSize;
      const pageWidth = pageSize.width ? pageSize.width : pageSize.getWidth();
      const pageHeight = pageSize.height ? pageSize.height : pageSize.getHeight();
      doc.text(`Page ${i} of ${pageCount}`, pageWidth / 2, pageHeight - 10, { align: 'center' });
      doc.text('EventaHub Booking System', 14, pageHeight - 10);
      doc.text(`${dayjs().format('YYYY-MM-DD')}`, pageWidth - 20, pageHeight - 10);
    }

    doc.save(`${activeTab.value}-bookings-${dayjs().format('YYYY-MM-DD')}.pdf`);
    $toast.success('PDF exported successfully');
  } catch (error: any) {
    handleError(error, $toast, 'Failed to export PDF');
  }
}

onMounted(async () => {
  await loadVendorData();
  await loadServices();
  await loadBookings();

  const urlParams = new URLSearchParams(window.location.search);
  const tabParam = urlParams.get('tab');
  if (tabParam && tabs.some(tab => tab.name.toLowerCase() === tabParam.toLowerCase())) {
    setActiveTab(tabParam);
  }
});
</script>
