<template>
    <div class="flex flex-col items-center justify-center py-20">
        <h1 class="text-2xl tracking-wide font-semibold">Reset Password</h1>
        <p>Make sure your new password follows the following password criteria: </p>
        <div>
            <ul class="list-disc text-start">
                <li>At least 8 characters long</li>
                <li>Must contain at least a number <b>[0-9]</b></li>
                <li>Contain atleast a special character <b>[!@#$%^&*]</b></li>
                <li>Must contain at least 1 capital letter <b>[A-Z]</b></li>
                <li>Contain at least 1 small letter <b>[a-z]</b></li>
                <li>No spaces</li>
            </ul>
        </div>
        <div class="mt-5">
            <FormKit id="editForm" type="form" submit-label="Update" :actions="false" @submit="onSubmit"
            #default="{ value }">
                <FormKit type="password" required placeholder="Enter new password" prefixIcon="password" label="New Password"/>
                <FormKit type="password" required placeholder="Re-enter your password" prefixIcon="password" label="Confirm New Password"/>
                <label class="flex items-center mt-2">
                    <FormKit type="checkbox"/> <span class="ml-2">
                        I agree to Eventa Malawi <a href="#" class="text-blue-500">Terms and Conditions</a> on password reset
                    </span>
                </label>
                <CorePrimaryButton class="mt-3" text="Send Password Reset Link"/>
            </FormKit>
        </div>
    </div>
</template>

<script setup lang="ts">
useHead({
    title: "Reset Password - Eventa Malawi"
});
const onSubmit = () => {
    console.log("reset")
}
</script>

<style lang="scss">
</style>