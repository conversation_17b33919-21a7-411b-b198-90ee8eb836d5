<template>
    <div class="">
        <div class="w-full grid grid-cols-1 md:grid-cols-2 gap-5">
            <div class="col-span-1 py-10 md:py-20 px-5">
                <div class="max-w-fit mb-2">
                    <div class="flex items-center gap-2 bg-red-50 border border-red-100 rounded-full px-2 py-1">
                        <div class="w-2 h-2 bg-red-600 rounded-full animate-pulse"></div>
                        <span class="font-medium text-red-500 text-sm">Hosting subscription</span>
                        <span class="font-semibold text-sm">Up To 75% OFF</span>
                    </div>
                </div>
                <div class="flex gap-2 mt-3">
                    <h3 class="text-3xl md:text-5xl font-semibold text-gray-900">
                        Best Events Management Platform
                    </h3>
                </div>
                <div class="mt-3">
                    <p class="text-gray-500">
                        Get the best experience hosting your vast events on EventaHub
                        Malawi. Sell tickets online and generate revenue.
                    </p>
                </div>
                <div class="mt-3 flex flex-col sm:flex-row items-center gap-2 sm:space-x-2.5">
                    <button @click="scrollToPlans" class="w-full sm:w-auto bg-red-600 text-white px-2 py-2 shiny hover:bg-red-700 transition-all">Get started</button>
                </div>
            </div>
            <div class="col-span-1 mt-5 md:mt-0">
                <img src="/assets/svgs/wallet.svg" alt="wallter-svg" class="w-full h-full object-cover" />
            </div>
        </div>
        <div id="subscription-plans" class="bg-gray-50 border-t border-b mx-auto px-4 py-8">
            <div class="text-center flex flex-col justify-center items-center mb-12">
                <h2 class="text-2xl md:text-3xl font-bold mb-4">Subscription Plans</h2>
                <p class="text-gray-600 mb-6 w-full sm:w-1/2 text-center">
                    Choose the perfect plan to elevate your events, reach more attendees, and maximize your revenue—all
                    with seamless management tools designed for success.
                </p>

                <div class="flex items-center justify-center gap-4">
                    <span class="text-gray-600">Monthly</span>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" v-model="isYearly" class="sr-only peer" />
                        <div
                            class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-600">
                        </div>
                    </label>
                    <span class="text-gray-600">Yearly</span>
                </div>
            </div>

            <div v-if="loading" class="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
                <div v-for="i in 3" :key="i" class="bg-white shadow-lg p-8 shimmer-container">
                    <div class="shimmer h-6 w-1/2 mx-auto mb-8"></div>
                    <div class="shimmer h-10 w-1/3 mx-auto mb-8"></div>
                    <div v-for="j in 5" :key="j" class="shimmer h-4 w-full mb-4"></div>
                    <div class="shimmer h-12 w-full mt-8"></div>
                </div>
            </div>

            <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-5xl mx-auto">
                <div v-for="(plan, index) in plans" :key="index"
                    class="relative bg-white shadow p-8 hover:shadow-lg transition-shadow">
                    <div v-if="plan.id == 3"
                        class="absolute -right-2 -top-2 bg-orange-500 text-white px-4 py-1 rotate-12">
                        30% Sale
                    </div>

                    <div class="text-center mb-8">
                        <h3 class="text-xl font-bold mb-2">{{ plan.name }}</h3>
                    </div>

                    <div class="text-center mb-8">
                        <p class="text-3xl font-bold">
                            MWK{{ isYearly ? Number(plan.price) * 12 : plan.price }}
                        </p>
                    </div>

                    <ul class="space-y-4 mb-8">
                        <li v-for="(feature, fIndex) in getPlanFeatures(plan.attributes)" :key="fIndex"
                            class="flex items-center">
                            <span v-if="feature.includes('Yes')" class="text-green-500 mr-2">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20"
                                    fill="currentColor">
                                    <path fill-rule="evenodd"
                                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                        clip-rule="evenodd" />
                                </svg>
                            </span>
                            <span v-else-if="feature.includes('No')" class="text-red-500 mr-2">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20"
                                    fill="currentColor">
                                    <path fill-rule="evenodd"
                                        d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                        clip-rule="evenodd" />
                                </svg>
                            </span>
                            {{ feature }}
                        </li>
                    </ul>

                    <button :class="[
                        'w-full py-3 px-6 font-medium transition-colors',
                        false
                            ? 'bg-red-600 hover:bg-red-700 text-white'
                            : 'bg-black hover:bg-gray-800 text-white',
                    ]">
                        Add to Cart
                    </button>
                </div>
            </div>
        </div>

        <section class="mx-auto px-4 py-16">
            <div class="relative mb-20">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
                    <div>
                        <h2 class="text-3xl md:text-4xl font-bold mb-4">
                            What more can EventaHub do for you?
                        </h2>
                        <p class="text-gray-600 mb-8">
                            EventaHub is a powerful event management platform that can help
                            you organize and promote your events.
                        </p>
                    </div>

                    <div class="relative">
                        <div class="bg-white rounded-none shadow-lg shadow-red-100 overflow-hidden">
                            <div class="relative aspect-video">
                                <img src="@/assets/images/explore.jpg" alt="explore-image-eventahub"
                                    class="w-full h-full object-cover" />
                                <button
                                    class="absolute inset-0 m-auto w-12 h-12 md:w-16 md:h-16 bg-red-500 rounded-full flex items-center justify-center hover:bg-pink-600 transition-colors">
                                    <span class="sr-only">Play video</span>
                                    <svg class="w-6 h-6 md:w-8 md:h-8 text-white" fill="currentColor"
                                        viewBox="0 0 24 24">
                                        <path d="M8 5v14l11-7z" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div>
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
                    <div v-for="(feature, index) in features" :key="index"
                        class="border border-dashed rounded-lg p-8 hover:shadow-sm transition-shadow">
                        <div class="mb-6">
                            <div :class="[
                                'w-16 h-16 rounded-full flex items-center justify-center',
                                getIconBackgroundColor(index)
                            ]">
                                <Icon :icon="feature.icon" class="w-8 h-8 text-black" />
                            </div>
                        </div>
                        <h3 class="text-2xl font-bold mb-3">{{ feature.title }}</h3>
                        <p class="text-gray-600">
                            {{ feature.description }}
                        </p>
                    </div>
                </div>
            </div>
        </section>
    </div>
</template>

<script setup lang="ts">
import type { Plan, PlanAttributes } from "@/types";
import { ENDPOINTS } from "../utils/api";

definePageMeta({
    layout: "default"
});

useHead({
    title: "Pricing | EventaHub Malawi",
    meta: [
        {
            name: "description",
            content:
                "EventaHub is a powerful event management platform that can help you organize and promote your events.",
        },
    ],
});

const isYearly = ref(false);
const loading = ref(true);

const httpClient = useHttpClient();
const plans = ref<Plan[]>([]);

const features = [
    {
        title: "Event Creation & Management",
        description:
            "Easily create, organize, and manage events with powerful tools that simplify event setup, attendee tracking, and scheduling—all in one place.",
        icon: "fluent-mdl2:edit-create",
    },
    {
        title: "Tickets Selling",
        description:
            "Sell tickets online effortlessly with secure payment processing and customizable ticket options to meet the needs of any event.",
        icon: "streamline:tickets",
    },
    {
        title: "Invitation",
        description:
            "Send personalized invitations to potential attendees with RSVP tracking to make planning and attendance management simple and effective.",
        icon: "fluent-mdl2:chat-invite-friend",
    },
    {
        title: "Social Media Marketing",
        description:
            "Promote events across social media platforms with targeted marketing tools designed to increase engagement and reach.",
        icon: "carbon:promote",
    },
    {
        title: "News & Trends",
        description:
            "Stay informed with the latest industry news and event trends to keep your strategies relevant and effective.",
        icon: "icon-park-solid:trend-two",
    },
    {
        title: "Affiliate Marketing",
        description:
            "Boost your event's reach through affiliate marketing, allowing partners to earn commissions by promoting your event.",
        icon: "tabler:affiliate",
    },
];

const formatFeatureValue = (key: string, value: any) => {
    if (typeof value === 'boolean') {
        return value ? 'Yes' : 'No';
    }
    if (Array.isArray(value)) {
        return value.join(', ');
    }
    return value;
};

const scrollToPlans = () => {
  const plansSection = document.getElementById('subscription-plans');
  if (plansSection) {
    plansSection.scrollIntoView({ behavior: 'smooth' });
  }
};

const getIconBackgroundColor = (index: number) => {
    const colors = [
        'bg-blue-100',
        'bg-pink-100',
        'bg-green-100',
        'bg-yellow-100',
        'bg-pink-100',
        'bg-gray-100'
    ];

    return colors[index % colors.length];
};

const getPlanFeatures = (attributes: PlanAttributes) => [
    `Support: ${attributes.support}`,
    `Duration: ${attributes.duration}`,
    `Analytics: ${formatFeatureValue('analytics', attributes.analytics)}`,
    `Custom Branding: ${formatFeatureValue('custom_branding', attributes.custom_branding)}`,
    `Featured Events: ${formatFeatureValue('featured_events', attributes.featured_events)}`,
    `Ticket Scanning: ${formatFeatureValue('ticket_scanning', attributes.ticket_scanning)}`,
    `SMS Notifications: ${formatFeatureValue('sms_notifications', attributes.sms_notifications)}`,
    `Multiple Organizers: ${formatFeatureValue('multiple_organizers', attributes.multiple_organizers)}`,
    `Event Page Customization: ${formatFeatureValue('event_page_customization', attributes.event_page_customization)}`,
];

const fetchSubscriptionPlans = async () => {
    loading.value = true;
    try {
        const response: any = await httpClient.get(ENDPOINTS.SUBSCRIPTION.GET_ALL);
        if (response) {
            plans.value = response.map((plan: any) => ({
                ...plan,
                attributes: JSON.parse(plan.attributes),
            }));
        }
    } catch (error) {
        console.error("Error fetching subscription plans:", error);
    } finally {
        setTimeout(() => {
            loading.value = false;
        }, 1000);
    }
};

onMounted(() => {
    fetchSubscriptionPlans();
});
</script>

<style lang="css" scoped>
.shimmer-container {
    overflow: hidden;
}

.shimmer {
    position: relative;
    background: #f6f7f8;
    background-image: linear-gradient(to right,
            #f6f7f8 0%,
            #edeef1 20%,
            #f6f7f8 40%,
            #f6f7f8 100%);
    background-repeat: no-repeat;
    background-size: 800px 104px;
    animation-duration: 1.5s;
    animation-fill-mode: forwards;
    animation-iteration-count: infinite;
    animation-name: shimmer;
    animation-timing-function: linear;
}

@keyframes shimmer {
    0% {
        background-position: -468px 0;
    }

    100% {
        background-position: 468px 0;
    }
}

@media (max-width: 640px) {
    .shimmer {
        background-size: 400px 104px;
    }
}
</style>