<template>
    <div class="relative bg-gray-100">
        <div class="px-10 pt-5">


            <nav class="flex" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
                    <li class="inline-flex items-center">
                        <a href="#" class="inline-flex items-center font-medium">
                            <Icon icon="solar:home-linear" class="h-5 w-5 mr-1" />
                            Home
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="rtl:rotate-180 w-3 h-3  mx-1" aria-hidden="true"
                                xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                    stroke-width="2" d="m1 9 4-4-4-4" />
                            </svg>
                            <a href="#" class="ms-1 font-medium">My Profile</a>
                        </div>
                    </li>
                </ol>
            </nav>

        </div>
        <div class="w-full grid grid-cols-5 gap-3 px-10 py-5">
            <div class="w-full shadow bg-white col-span-2 flex flex-col px-10 py-20">
                <div class="w-full flex flex-col space-y-0.5 items-center justify-center relative pb-3 pt-5">
                    <div class="relative" @mouseover="isHovered = true" @mouseleave="isHovered = false">
                        <img :src="`${runtimeConfig.public.baseUrl}storage/avatars/${profile?.avatar}`" alt="profile-picture"
                            class="w-20 h-20 object-cover rounded-full border border-sky-100" />
                        <div v-if="isHovered"
                            class="absolute inset-0 bg-black bg-opacity-10 transiton duration-150 rounded-full flex items-center justify-center">
                            <button @click="onCameraClicked"
                                class="bg-black bg-opacity-25 flex items-center justify-center p-2 rounded-full z-20 cursor-pointer">
                                <Icon icon="twemoji:camera" class="w-6 h-6 text-gray-500" />
                            </button>
                        </div>
                        <CoreImagePicker :url="ENDPOINTS.PROFILE.UPDATE_PHOTO" field="photo" ref="imageUploadComponent" @onUploaded="fetchUser"/>
                        <Icon icon="material-symbols:verified-rounded"
                            class="absolute w-6 h-6 mr-2 text-sky-500 -bottom-2 -right-0" />
                    </div>

                    <h3 class="text-lg font-semibold">{{ profile?.name }}</h3>
                    <p class="text-base font-light">{{ profile?.email }}</p>
                    <div>
                        <p class="text-sky-500 font-thin">
                            Verify email address
                        </p>
                    </div>
                </div>
                <div class="w-full grid grid-cols-2 gap-2">
                    <div class="flex flex-col items-center space-y-1 border-r">
                        <h3 class="text-xl font-semibold">{{ profile?.followers_count }}</h3>
                        <p class="text-base text-gray-500">Followers</p>
                    </div>
                    <div class="flex flex-col items-center space-y-1">
                        <h3 class="text-xl font-semibold">{{ profile?.following_count }}</h3>
                        <p class="text-base text-gray-500">Following</p>
                    </div>
                </div>
                <div class="w-full mx-auto flex flex-col items-center justify-center mt-3">
                    <p class="text-base text-gray-500" v-html="profile?.profile?.about"></p>
                    <div class="w-full flex items-center justify-center mx-auto">
                        <ProfileEditAbout @update="fetchUser" :bio="String(profile?.profile?.about)"/>
                    </div>
                </div>

                <div class="w-full flex flex-col items-center justify-center py-2.5 space-y-3">
                    <h4 class="text-base font-medium text-gray-700">Dashboards</h4>
                    <div class="flex flex-wrap gap-3 justify-center">
                        <nuxt-link to="/dashboard"
                            class="flex items-center space-x-2 border-2 border-black hover:bg-gray-100 transition duration-150 px-4 py-2">
                            <Icon icon="heroicons:home" class="w-5 h-5 mr-2" />
                            Host Dashboard
                        </nuxt-link>
                        <nuxt-link to="/vendor/dashboard"
                            class="flex items-center space-x-2 border-2 border-red-600 text-red-600 hover:bg-red-50 transition duration-150 px-4 py-2">
                            <Icon icon="heroicons:briefcase" class="w-5 h-5 mr-2" />
                            Vendor Dashboard
                        </nuxt-link>
                    </div>
                </div>

            </div>

            <div class="w-full col-span-3 shadow bg-white rounded-none focus:outline-none focus:ring-none">
                <div>
                    <div class="bg-gray-50 flex items-center border-b border-gray-200">
                        <button v-for="tab in tabs" v-bind:key="tab.name.toString().toLowerCase()"
                            class="px-4 py-2 shiny flex items-center text-center hover:bg-red-600 hover:text-white transition duration-150"
                            :class="currentTab.name == tab.name ? 'bg-red-600 text-white' : 'border-r border-gray-200 text-gray-500'"
                            @click="setCurrent(tab)">
                            <Icon :icon="tab.icon" class="w-5 h-5 mr-2" /> {{ tab.name }}
                        </button>
                    </div>
                    <div>
                        <component v-bind="componentProps" :is="tabs.find((tab: Tab) => tab.name == currentTab.name)?.component" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { LazyProfileAbout, LazyProfileSettings, LazyProfileEvents, LazyProfileTickets } from '#components';
import type { Component } from 'vue';
import type { User } from '@/types/user';
import { useAuthStore } from '@/store/auth';

useHead({
    title: `My Profile - Eventa Malawi`
});

interface Tab {
    name: string;
    icon: string;
    component?: Component
}

const runtimeConfig = useRuntimeConfig();
const router = useRouter();
const route = useRoute();
const httpClient = useHttpClient();
const profile = ref<User>();
const userStore = useAuthStore();
const isHovered = ref<boolean>(false);
const imageUploadComponent = ref<{ openDialog: () => void } | null>(null);
const tabs = ref<Tab[]>([
    {
        name: "Events",
        icon: "fluent-mdl2:schedule-event-action",
        component: markRaw(LazyProfileEvents)
    },
    {
        name: "Tickets",
        icon: "icon-park-outline:ticket",
        component: markRaw(LazyProfileTickets)
    },
    {
        name: "About",
        icon: "oui:user",
        component: markRaw(LazyProfileAbout)
    },
    {
        name: "Settings",
        icon: "simple-line-icons:settings",
        component: markRaw(LazyProfileSettings)
    }
]);

const componentPropsMap = {
    'Events': (_currentTab: any) => ({
        events: profile.value?.events
    }),
    'Tickets': () => ({

    }),
    'About': (_currentTab: any) => ({
        profile: profile.value
    }),
};

const componentProps = computed(() => {
    const propsGenerator = componentPropsMap[currentTab.value.name as keyof typeof componentPropsMap] || (() => ({}));
    return propsGenerator(currentTab.value);
});

const initialTab = computed((): Tab => {
    const foundTab = route.query.tab ? tabs.value.find((tab: Tab) => tab.name === String(route.query.tab)) : undefined;
    return foundTab || tabs.value[0];
})
const currentTab = ref<Tab>(initialTab.value);
const setCurrent = (tab: Tab): void => {
    currentTab.value = tab;
    const currentRoute = router.currentRoute.value;
    const newQuery = {
        ...currentRoute.query,
        tab: tab.name,
    };
    router.replace({ query: newQuery });
}

const onCameraClicked = (): void => {
    imageUploadComponent.value?.openDialog();
}

const fetchUser = async (): Promise<void> => {
    try {
        const response = await httpClient.get<any>(ENDPOINTS.PROFILE.USER);
        if (response) {
            profile.value = response;
            userStore.setUser(response);
        }
    } catch (error) {
        console.log(error)
    }
}

onMounted(() => {
    fetchUser();
})
</script>
<style scoped></style>