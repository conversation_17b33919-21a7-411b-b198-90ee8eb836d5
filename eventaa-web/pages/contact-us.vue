<template>
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12">
    <div
      class="bg-gray-50 p-6 sm:p-12 mb-8 sm:mb-12 text-center relative overflow-hidden rounded-none"
    >
      <div class="absolute inset-0">
        <div class="absolute inset-0">
          <div
            class="absolute left-0 top-0 w-24 sm:w-40 h-24 sm:h-40 rounded-full opacity-20 bg-gray-200 -translate-x-1/2 -translate-y-1/2"
          ></div>
          <div
            class="absolute right-0 top-0 w-24 sm:w-40 h-24 sm:h-40 rounded-full opacity-20 bg-gray-200 translate-x-1/2 -translate-y-1/2"
          ></div>
          <div
            class="absolute right-0 bottom-0 w-24 sm:w-40 h-24 sm:h-40 rounded-full opacity-20 bg-gray-200 translate-x-1/2 translate-y-1/2"
          ></div>
        </div>
      </div>
      <div class="relative">
        <div
          class="inline-block bg-gray-300 text-black px-3 py-1 sm:px-4 sm:py-2 rounded-full text-xs sm:text-sm font-medium mb-2 sm:mb-4"
        >
          WRITE TO US
        </div>
        <h1 class="text-3xl sm:text-4xl md:text-5xl font-bold">Get In Touch</h1>
      </div>
    </div>

    

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12">
      <div>
        <h2 class="text-2xl sm:text-3xl font-bold text-gray-900 mb-4 sm:mb-6">Let's Talk!</h2>
        <p class="text-sm sm:text-base text-gray-600 mb-6 sm:mb-8">
          Get in touch with us using the enquiry form or contact details below.
        </p>

        <FormKit
          type="form"
          @submit="handleSubmit"
          submit-label=""
          :actions="false"
          class="space-y-4 sm:space-y-6"
        >
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <FormKit
              type="text"
              name="firstName"
              label="First Name"
              validation="required"
            />
            <FormKit
              type="text"
              name="lastName"
              label="Last Name"
              validation="required"
            />
          </div>

          <FormKit
            type="email"
            name="email"
            label="Email"
            validation="required|email"
            prefixIcon="email"
          />

          <FormKit
            type="textarea"
            name="message"
            label="Message"
            validation="required"
            placeholder="Type something..."
            :rows="4"
          />

          <Vueform class="mt-2">
            <CheckboxElement
              name="terms_agreement"
              input-class="text-red-600 focus:ring-red-500"
            >
            By sending this message, you agree to Eventa Malawi <a href='#' class='text-red-600 hover:text-red-500'>Privacy Policy</a></CheckboxElement>
          </Vueform>

          <CorePrimaryButton text="Send Message" class="w-full sm:w-auto mt-4" />
        </FormKit>
      </div>

      <div class="bg-gray-50 p-4 sm:p-6 rounded-lg mt-8 lg:mt-0">
        <div class="flex justify-center mb-6 sm:mb-8">
          <img 
            src="@/assets/illustrations/contact-person.png" 
            alt="contact-person-illustration" 
            class="h-40 sm:h-56 w-auto object-cover" 
          />
        </div>

        <div class="space-y-4 sm:space-y-6">
          <h3 class="text-lg sm:text-xl font-bold text-gray-900 mb-3 sm:mb-4">Quick Contact</h3>

          <div class="flex items-start sm:items-center">
            <div class="flex-shrink-0 bg-red-100 p-2 rounded-none">
              <div class="w-5 sm:w-6 h-5 sm:h-6 text-red-600">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                  />
                </svg>
              </div>
            </div>
            <div class="ml-3 sm:ml-4">
              <p class="text-base sm:text-lg font-semibold text-black">Email:</p>
              <a
                href="mailto:<EMAIL>"
                class="text-sm sm:text-base text-gray-600 hover:text-sky-600"
              >
                <EMAIL>
              </a>
            </div>
          </div>

          <div class="flex items-start sm:items-center">
            <div class="flex-shrink-0 bg-red-100 p-2 rounded-none">
              <div class="w-5 sm:w-6 h-5 sm:h-6 text-red-600">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                  />
                </svg>
              </div>
            </div>
            <div class="ml-3 sm:ml-4">
              <p class="text-base sm:text-lg font-semibold text-black">Phone Number</p>
              <p class="text-sm sm:text-base text-gray-600">Malawi +265 88 0295693</p>
            </div>
          </div>

          <div class="flex items-start sm:items-center">
            <div class="flex-shrink-0 bg-red-100 p-2 rounded-none">
              <div class="w-5 sm:w-6 h-5 sm:h-6 text-red-600">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                  />
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                  />
                </svg>
              </div>
            </div>
            <div class="ml-3 sm:ml-4">
              <p class="text-base sm:text-lg font-semibold text-black">Headquater</p>
              <p class="text-sm sm:text-base text-gray-600">
                Area 7, Lilongwe Malawi
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>

definePageMeta({
  layout: "default",
});

useHead({
  title: "Contact Us | Eventa Malawi",
  meta: [
    {
      name: "description",
      content: "Contact us page",
    },
  ],
});

const handleSubmit = (formData: any) => {
  console.log("Form submitted:", formData);
  alert("Thank you for your message! We will get back to you soon.");
};
</script>