<template>
    <div class="flex flex-col items-center justify-center py-20">
        <div class="w-full flex flex-col text-center justify-center">
            <img src="@/assets/illustrations/facepalm.png" alt="face-palm-illustration-eventa-malawi" class="object-contain w-auto h-72"/>
            <h3 class="w-72 justify-center mx-auto text-xl tracking-wide font-bold text-ellipsis">Are you sure you don't remember your Password?</h3>
            <p class="w-1/2 justify-center mx-auto mt-2 text-base text-gray-500 text-wrap">Enter the email address associated with your account below and we will send you a password reset link</p>
        </div>
        <div class="mt-5">
            <FormKit id="editForm" type="form" submit-label="Update" :actions="false" @submit="onSubmit"
            #default="{ value }">
                <FormKit type="email" required placeholder="Enter your email address" prefixIcon="email" label="Email Address"/>
                <CorePrimaryButton class="mt-3" text="Send Password Reset Link"/>
            </FormKit>
        </div>
    </div>
</template>

<script setup lang="ts">
definePageMeta({
    layout: "default"
});
useHead({
    title: "Forgot Password - Eventa Malawi"
});

const onSubmit = (values: any) => {
    navigateTo("/reset-password");
}
</script>

<style lang="scss">
</style>