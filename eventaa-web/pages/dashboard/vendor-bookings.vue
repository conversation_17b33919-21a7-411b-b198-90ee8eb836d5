<template>
  <div class="p-6">
    <div class="mb-6 flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold">My Vendor Bookings</h1>
        <p class="text-gray-500">View and manage your vendor service bookings</p>
      </div>
    </div>

    <div class="bg-white rounded-lg shadow overflow-hidden mb-6">
      <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
        <h3 class="text-lg font-medium text-gray-900">Bookings</h3>
        <div class="flex space-x-2">
          <select v-model="filterStatus" @change="fetchBookings()"
            class="rounded-md border-gray-300 shadow-sm focus:border-red-500 focus:ring focus:ring-red-500 focus:ring-opacity-50">
            <option value="all">All Bookings</option>
            <option value="pending">Pending</option>
            <option value="approved">Approved</option>
            <option value="completed">Completed</option>
            <option value="rejected">Rejected</option>
            <option value="cancelled">Cancelled</option>
          </select>
        </div>
      </div>

      <div v-if="loading" class="flex justify-center items-center py-20">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-red-500"></div>
      </div>

      <div v-else-if="bookings.length === 0" class="py-20 text-center">
        <div class="mx-auto h-12 w-12 text-gray-400">
          <Icon icon="heroicons:calendar" class="h-12 w-12" />
        </div>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No bookings</h3>
        <p class="mt-1 text-sm text-gray-500">No bookings found for the selected filter.</p>
        <div class="mt-6">
          <NuxtLink to="/vendors"
            class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
            Browse Vendors
          </NuxtLink>
        </div>
      </div>

      <div v-else class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col"
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Vendor
              </th>
              <th scope="col"
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Service
              </th>
              <th scope="col"
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Date & Time
              </th>
              <th scope="col"
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th scope="col"
                class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="booking in bookings" :key="booking.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-shrink-0 h-10 w-10">
                    <img class="h-10 w-10 rounded-full" 
                      :src="booking.vendor?.logo || 'https://ui-avatars.com/api/?name=' + encodeURIComponent(booking.vendor?.name || 'Vendor')" 
                      alt="" />
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">{{ booking.vendor?.name }}</div>
                    <div class="text-sm text-gray-500">{{ booking.vendor?.location }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ booking.vendorService?.service?.name }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ formatDate(booking.booking_from) }}</div>
                <div class="text-sm text-gray-500">
                  {{ formatTime(booking.booking_from) }} - {{ formatTime(booking.booking_to) }}
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full" 
                  :class="getStatusClass(booking.status)">
                  {{ booking.status }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <button @click="showBookingDetails(booking)"
                  class="text-red-600 hover:text-red-900 mr-3">View</button>
                <VendorsRatingDialog 
                  v-if="canRateBooking(booking)" 
                  :vendor-id="booking.vendor_id" 
                  :booking-id="booking.id"
                  @rating-submitted="handleRatingSubmitted" />
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div v-if="bookings.length > 0" class="px-6 py-4 border-t border-gray-200">
        <div class="flex items-center justify-between">
          <div class="text-sm text-gray-700">
            Showing <span class="font-medium">{{ paginationStart }}</span> to
            <span class="font-medium">{{ paginationEnd }}</span> of
            <span class="font-medium">{{ totalItems }}</span> results
          </div>
          <nav class="flex items-center space-x-2">
            <button @click="prevPage" :disabled="currentPage <= 1"
              class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
              :class="{ 'opacity-50 cursor-not-allowed': currentPage <= 1 }">
              <span class="sr-only">Previous</span>
              <Icon icon="heroicons:chevron-left" class="h-5 w-5" />
            </button>
            <button @click="nextPage" :disabled="currentPage >= totalPages"
              class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
              :class="{ 'opacity-50 cursor-not-allowed': currentPage >= totalPages }">
              <span class="sr-only">Next</span>
              <Icon icon="heroicons:chevron-right" class="h-5 w-5" />
            </button>
          </nav>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import dayjs from 'dayjs';
import { ENDPOINTS } from '@/utils/api';

const loading = ref(true);
const bookings = ref<any[]>([]);
const filterStatus = ref('all');
const currentPage = ref(1);
const itemsPerPage = 10;
const totalItems = ref(0);
const totalPages = ref(1);
const { $toast }: any = useNuxtApp();
const httpClient = useHttpClient();

const paginationStart = computed(() => {
  if (totalItems.value === 0) return 0;
  return (currentPage.value - 1) * itemsPerPage + 1;
});

const paginationEnd = computed(() => {
  return Math.min(currentPage.value * itemsPerPage, totalItems.value);
});

const fetchBookings = async (): Promise<void> => {
  loading.value = true;
  try {
    const params: any = {
      page: currentPage.value,
      per_page: itemsPerPage
    };
    
    if (filterStatus.value !== 'all') {
      params.status = filterStatus.value;
    }
    
    const response: any = await httpClient.get(`${ENDPOINTS.VENDORS.BASE}/bookings/user`, { params });
    
    if (response) {
      bookings.value = response.data;
      totalItems.value = response.total;
      totalPages.value = response.last_page;
    }
  } catch (error: any) {
    $toast.error(error.message || 'Failed to load bookings');
  } finally {
    loading.value = false;
  }
};

const formatDate = (dateString: string): string => {
  return dayjs(dateString).format('MMM D, YYYY');
};

const formatTime = (dateString: string): string => {
  return dayjs(dateString).format('h:mm A');
};

const getStatusClass = (status: string): string => {
  const statusClasses: Record<string, string> = {
    'pending': 'bg-yellow-100 text-yellow-800',
    'approved': 'bg-green-100 text-green-800',
    'completed': 'bg-blue-100 text-blue-800',
    'rejected': 'bg-red-100 text-red-800',
    'cancelled': 'bg-gray-100 text-gray-800'
  };
  
  return statusClasses[status] || 'bg-gray-100 text-gray-800';
};

const canRateBooking = (booking: any): boolean => {
  // Can rate if booking is completed and the booking date has passed
  return booking.status === 'completed' && dayjs(booking.booking_to).isBefore(dayjs());
};

const showBookingDetails = (booking: any): void => {
  // Implement booking details view
  console.log('Show booking details:', booking);
};

const handleRatingSubmitted = (): void => {
  $toast.success('Thank you for your rating!');
};

const prevPage = (): void => {
  if (currentPage.value > 1) {
    currentPage.value--;
    fetchBookings();
  }
};

const nextPage = (): void => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
    fetchBookings();
  }
};

onMounted(() => {
  fetchBookings();
});
</script>
