<template>
  <div class="h-full bg-gray-100 flex flex-col">
    <div class="p-6 pb-0">
      <div class="mb-6 flex justify-between items-center">
        <div>
          <h1 class="text-2xl font-bold">Messages</h1>
          <p class="text-gray-500">Communicate with users, vendors, and attendees</p>
        </div>
        <div class="flex space-x-2">
          <button class="px-4 py-2 bg-white border border-gray-300 rounded shadow-sm hover:bg-gray-50 flex items-center">
            <Icon icon="heroicons:funnel" class="w-5 h-5 text-gray-600 mr-1" />
            Filter
          </button>
          <button class="px-4 py-2 bg-red-600 text-white rounded shadow-sm hover:bg-red-700 flex items-center">
            <Icon icon="heroicons:plus" class="w-5 h-5 mr-1" />
            New Message
          </button>
        </div>
      </div>
    </div>

    <div class="flex-1 flex overflow-hidden p-6 pt-0">
      <!-- Contacts List -->
      <div class="w-1/3 bg-white shadow rounded-l overflow-hidden flex flex-col">
        <div class="p-4 border-b">
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Icon icon="heroicons:magnifying-glass" class="h-5 w-5 text-gray-400" />
            </div>
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search contacts"
              class="block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:border-red-500 focus:ring-red-500"
            />
          </div>
        </div>

        <div class="flex-1 overflow-y-auto">
          <div v-if="loading" class="flex justify-center items-center py-20">
            <CoreLoader />
          </div>
          <div v-else>
            <div
              v-for="contact in filteredContacts"
              :key="contact.id"
              @click="selectContact(contact)"
              class="p-4 border-b hover:bg-gray-50 cursor-pointer"
              :class="{ 'bg-gray-50': selectedContact && selectedContact.id === contact.id }"
            >
              <div class="flex items-center">
                <div class="relative flex-shrink-0">
                  <img class="h-12 w-12 rounded-full" :src="contact.avatar || 'https://via.placeholder.com/40'" alt="" />
                  <div
                    v-if="contact.online"
                    class="absolute bottom-0 right-0 h-3 w-3 rounded-full bg-green-500 border-2 border-white"
                  ></div>
                </div>
                <div class="ml-4 flex-1">
                  <div class="flex items-center justify-between">
                    <div class="text-sm font-medium text-gray-900">{{ contact.name }}</div>
                    <div class="text-xs text-gray-500">{{ formatTime(contact.lastMessage.timestamp) }}</div>
                  </div>
                  <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500 truncate max-w-[180px]">{{ contact.lastMessage.content }}</div>
                    <div v-if="contact.unreadCount > 0" class="bg-red-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                      {{ contact.unreadCount }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Chat Area -->
      <div class="w-2/3 bg-white shadow rounded-r overflow-hidden flex flex-col">
        <div v-if="!selectedContact" class="flex-1 flex items-center justify-center bg-gray-50">
          <div class="text-center">
            <Icon icon="heroicons:chat-bubble-left-right" class="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h3 class="text-lg font-medium text-gray-700">Select a conversation</h3>
            <p class="text-gray-500 mt-1">Choose a contact from the list to start messaging</p>
          </div>
        </div>

        <template v-else>
          <!-- Chat Header -->
          <div class="p-4 border-b flex items-center justify-between">
            <div class="flex items-center">
              <div class="relative">
                <img class="h-10 w-10 rounded-full" :src="selectedContact.avatar || 'https://via.placeholder.com/40'" alt="" />
                <div
                  v-if="selectedContact.online"
                  class="absolute bottom-0 right-0 h-2.5 w-2.5 rounded-full bg-green-500 border-2 border-white"
                ></div>
              </div>
              <div class="ml-3">
                <div class="text-sm font-medium text-gray-900">{{ selectedContact.name }}</div>
                <div class="text-xs text-gray-500">
                  {{ selectedContact.online ? 'Online' : 'Last seen ' + formatLastSeen(selectedContact.lastSeen) }}
                </div>
              </div>
            </div>
            <div class="flex space-x-2">
              <button class="p-2 text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100">
                <Icon icon="heroicons:phone" class="h-5 w-5" />
              </button>
              <button class="p-2 text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100">
                <Icon icon="heroicons:video-camera" class="h-5 w-5" />
              </button>
              <button class="p-2 text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100">
                <Icon icon="heroicons:information-circle" class="h-5 w-5" />
              </button>
            </div>
          </div>

          <!-- Messages -->
          <div class="flex-1 overflow-y-auto p-4 bg-gray-50" ref="messagesContainer">
            <div v-if="loadingMessages" class="flex justify-center items-center py-20">
              <CoreLoader />
            </div>
            <div v-else>
              <div v-for="(message, index) in messages" :key="index" class="mb-4">
                <!-- Date Separator -->
                <div v-if="showDateSeparator(message, index)" class="flex justify-center my-4">
                  <div class="bg-gray-200 text-gray-600 text-xs px-2 py-1 rounded">
                    {{ formatMessageDate(message.timestamp) }}
                  </div>
                </div>

                <!-- Message -->
                <div
                  class="flex"
                  :class="message.sender === 'me' ? 'justify-end' : 'justify-start'"
                >
                  <div
                    class="max-w-[70%] rounded-lg px-4 py-2 shadow-sm"
                    :class="message.sender === 'me' ? 'bg-red-600 text-white' : 'bg-white'"
                  >
                    <div class="text-sm">{{ message.content }}</div>
                    <div
                      class="text-xs mt-1 text-right"
                      :class="message.sender === 'me' ? 'text-red-200' : 'text-gray-500'"
                    >
                      {{ formatMessageTime(message.timestamp) }}
                      <span v-if="message.sender === 'me'" class="ml-1">
                        <Icon
                          :icon="message.read ? 'heroicons:check-circle' : 'heroicons:check'"
                          class="h-3 w-3 inline"
                        />
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Message Input -->
          <div class="p-4 border-t">
            <div class="flex items-center">
              <button class="p-2 text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100">
                <Icon icon="heroicons:paper-clip" class="h-5 w-5" />
              </button>
              <div class="flex-1 mx-2">
                <input
                  v-model="newMessage"
                  type="text"
                  placeholder="Type a message..."
                  class="w-full border-gray-300 rounded-full shadow-sm focus:border-red-500 focus:ring-red-500"
                  @keyup.enter="sendMessage"
                />
              </div>
              <button
                @click="sendMessage"
                class="p-2 text-white bg-red-600 rounded-full hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
                :disabled="!newMessage.trim()"
              >
                <Icon icon="heroicons:paper-airplane" class="h-5 w-5" />
              </button>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick } from 'vue';
import { ENDPOINTS } from '@/utils/api';

definePageMeta({
  layout: "dashboard",
});

const loading = ref(true);
const loadingMessages = ref(false);
const searchQuery = ref('');
const selectedContact = ref(null);
const newMessage = ref('');
const messages = ref([]);
const messagesContainer = ref(null);
const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

// Mock data - would be replaced with API calls
const contacts = ref([
  {
    id: 1,
    name: 'John Doe',
    avatar: 'https://randomuser.me/api/portraits/men/1.jpg',
    online: true,
    lastSeen: null,
    unreadCount: 3,
    lastMessage: {
      content: 'Hey, I have a question about the Summer Music Festival',
      timestamp: new Date(2024, 4, 25, 14, 30)
    }
  },
  {
    id: 2,
    name: 'Jane Smith',
    avatar: 'https://randomuser.me/api/portraits/women/2.jpg',
    online: false,
    lastSeen: new Date(2024, 4, 25, 10, 15),
    unreadCount: 0,
    lastMessage: {
      content: 'Thanks for your help!',
      timestamp: new Date(2024, 4, 24, 16, 45)
    }
  },
  {
    id: 3,
    name: 'Robert Johnson',
    avatar: 'https://randomuser.me/api/portraits/men/3.jpg',
    online: true,
    lastSeen: null,
    unreadCount: 1,
    lastMessage: {
      content: 'Is the Tech Conference still scheduled for May 10?',
      timestamp: new Date(2024, 4, 25, 9, 20)
    }
  },
  {
    id: 4,
    name: 'Emily Davis',
    avatar: 'https://randomuser.me/api/portraits/women/4.jpg',
    online: false,
    lastSeen: new Date(2024, 4, 24, 18, 30),
    unreadCount: 0,
    lastMessage: {
      content: 'I\'ll check and get back to you',
      timestamp: new Date(2024, 4, 23, 11, 10)
    }
  },
  {
    id: 5,
    name: 'Michael Wilson',
    avatar: 'https://randomuser.me/api/portraits/men/5.jpg',
    online: false,
    lastSeen: new Date(2024, 4, 25, 8, 45),
    unreadCount: 0,
    lastMessage: {
      content: 'Looking forward to the Food & Wine Expo!',
      timestamp: new Date(2024, 4, 22, 15, 25)
    }
  }
]);

const mockMessages = {
  1: [
    {
      sender: 'them',
      content: 'Hi there! I\'m interested in attending the Summer Music Festival.',
      timestamp: new Date(2024, 4, 25, 14, 15),
      read: true
    },
    {
      sender: 'them',
      content: 'Do you still have VIP tickets available?',
      timestamp: new Date(2024, 4, 25, 14, 16),
      read: true
    },
    {
      sender: 'me',
      content: 'Hello! Yes, we still have some VIP tickets available for the Summer Music Festival.',
      timestamp: new Date(2024, 4, 25, 14, 20),
      read: true
    },
    {
      sender: 'them',
      content: 'Great! How much are they?',
      timestamp: new Date(2024, 4, 25, 14, 22),
      read: true
    },
    {
      sender: 'me',
      content: 'VIP tickets are $150 each. They include priority entry, access to the VIP lounge, and complimentary drinks.',
      timestamp: new Date(2024, 4, 25, 14, 25),
      read: true
    },
    {
      sender: 'them',
      content: 'That sounds perfect! How can I purchase them?',
      timestamp: new Date(2024, 4, 25, 14, 28),
      read: true
    },
    {
      sender: 'them',
      content: 'Hey, I have a question about the Summer Music Festival',
      timestamp: new Date(2024, 4, 25, 14, 30),
      read: false
    }
  ],
  2: [
    {
      sender: 'them',
      content: 'Hello, I\'m having trouble accessing my tickets for the event.',
      timestamp: new Date(2024, 4, 24, 15, 30),
      read: true
    },
    {
      sender: 'me',
      content: 'I\'m sorry to hear that. Could you please provide your order number?',
      timestamp: new Date(2024, 4, 24, 15, 35),
      read: true
    },
    {
      sender: 'them',
      content: 'Sure, it\'s ORD-12345.',
      timestamp: new Date(2024, 4, 24, 15, 38),
      read: true
    },
    {
      sender: 'me',
      content: 'Thank you. I\'ve resent the ticket confirmation email. Please check your inbox.',
      timestamp: new Date(2024, 4, 24, 16, 40),
      read: true
    },
    {
      sender: 'them',
      content: 'Thanks for your help!',
      timestamp: new Date(2024, 4, 24, 16, 45),
      read: true
    }
  ],
  3: [
    {
      sender: 'them',
      content: 'Good morning! I wanted to confirm if the Tech Conference is still happening on May 10?',
      timestamp: new Date(2024, 4, 25, 9, 15),
      read: true
    },
    {
      sender: 'them',
      content: 'Is the Tech Conference still scheduled for May 10?',
      timestamp: new Date(2024, 4, 25, 9, 20),
      read: false
    }
  ],
  4: [
    {
      sender: 'them',
      content: 'Hi, do you know if there will be vegetarian options at the Food & Wine Expo?',
      timestamp: new Date(2024, 4, 23, 10, 50),
      read: true
    },
    {
      sender: 'me',
      content: 'Hello! Yes, there will be plenty of vegetarian options available at the expo.',
      timestamp: new Date(2024, 4, 23, 11, 5),
      read: true
    },
    {
      sender: 'them',
      content: 'Great! And what about vegan options?',
      timestamp: new Date(2024, 4, 23, 11, 8),
      read: true
    },
    {
      sender: 'me',
      content: 'I\'ll check and get back to you',
      timestamp: new Date(2024, 4, 23, 11, 10),
      read: true
    }
  ],
  5: [
    {
      sender: 'them',
      content: 'Hello! I just bought tickets for the Food & Wine Expo.',
      timestamp: new Date(2024, 4, 22, 15, 15),
      read: true
    },
    {
      sender: 'me',
      content: 'That\'s great! Thank you for your purchase. We hope you enjoy the event!',
      timestamp: new Date(2024, 4, 22, 15, 20),
      read: true
    },
    {
      sender: 'them',
      content: 'Looking forward to the Food & Wine Expo!',
      timestamp: new Date(2024, 4, 22, 15, 25),
      read: true
    }
  ]
};

const filteredContacts = computed(() => {
  if (!searchQuery.value) {
    return contacts.value.sort((a, b) => b.lastMessage.timestamp - a.lastMessage.timestamp);
  }

  const query = searchQuery.value.toLowerCase();
  return contacts.value
    .filter(contact => contact.name.toLowerCase().includes(query))
    .sort((a, b) => b.lastMessage.timestamp - a.lastMessage.timestamp);
});

function formatTime(date) {
  if (!date) return '';

  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);

  const messageDate = new Date(date);
  const messageDay = new Date(messageDate.getFullYear(), messageDate.getMonth(), messageDate.getDate());

  if (messageDay.getTime() === today.getTime()) {
    return messageDate.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true });
  } else if (messageDay.getTime() === yesterday.getTime()) {
    return 'Yesterday';
  } else {
    return messageDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  }
}

function formatLastSeen(date) {
  if (!date) return '';

  const now = new Date();
  const diffMs = now - date;
  const diffMins = Math.floor(diffMs / 60000);

  if (diffMins < 1) {
    return 'just now';
  } else if (diffMins < 60) {
    return `${diffMins} ${diffMins === 1 ? 'minute' : 'minutes'} ago`;
  } else {
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) {
      return `${diffHours} ${diffHours === 1 ? 'hour' : 'hours'} ago`;
    } else {
      return formatTime(date);
    }
  }
}

function formatMessageDate(date) {
  if (!date) return '';

  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);

  const messageDate = new Date(date);
  const messageDay = new Date(messageDate.getFullYear(), messageDate.getMonth(), messageDate.getDate());

  if (messageDay.getTime() === today.getTime()) {
    return 'Today';
  } else if (messageDay.getTime() === yesterday.getTime()) {
    return 'Yesterday';
  } else {
    return messageDate.toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric' });
  }
}

function formatMessageTime(date) {
  if (!date) return '';
  return date.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true });
}

function showDateSeparator(message, index) {
  if (index === 0) return true;

  const prevMessage = messages.value[index - 1];
  const prevDate = new Date(prevMessage.timestamp);
  const currDate = new Date(message.timestamp);

  return prevDate.getDate() !== currDate.getDate() ||
         prevDate.getMonth() !== currDate.getMonth() ||
         prevDate.getFullYear() !== currDate.getFullYear();
}

function selectContact(contact) {
  selectedContact.value = contact;
  loadingMessages.value = true;

  // In a real app, this would call an API
  // Simulate API call
  setTimeout(() => {
    messages.value = mockMessages[contact.id] || [];
    loadingMessages.value = false;

    // Mark messages as read
    if (contact.unreadCount > 0) {
      contact.unreadCount = 0;
      messages.value.forEach(msg => {
        if (msg.sender === 'them') {
          msg.read = true;
        }
      });
    }

    // Scroll to bottom
    nextTick(() => {
      scrollToBottom();
    });
  }, 500);
}

function scrollToBottom() {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
  }
}

function sendMessage() {
  if (!newMessage.value.trim() || !selectedContact.value) return;

  const message = {
    sender: 'me',
    content: newMessage.value,
    timestamp: new Date(),
    read: false
  };

  messages.value.push(message);

  // Update last message in contact
  selectedContact.value.lastMessage = {
    content: newMessage.value,
    timestamp: new Date()
  };

  newMessage.value = '';

  // Scroll to bottom
  nextTick(() => {
    scrollToBottom();
  });

  // Simulate reply after a delay
  if (Math.random() > 0.5) {
    setTimeout(() => {
      const reply = {
        sender: 'them',
        content: 'Thanks for your message! I\'ll get back to you soon.',
        timestamp: new Date(),
        read: true
      };

      messages.value.push(reply);

      // Update last message in contact
      selectedContact.value.lastMessage = {
        content: reply.content,
        timestamp: new Date()
      };

      // Scroll to bottom
      nextTick(() => {
        scrollToBottom();
      });
    }, 2000);
  }
}

// Simulate API call
onMounted(() => {
  setTimeout(() => {
    loading.value = false;
  }, 1000);
});
</script>
