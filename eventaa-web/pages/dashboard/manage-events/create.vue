<template>
    <CoreOverlay :loading="loading" text="Processing, please wait..." />
    <div class="flex flex-col min-h-screen">
        <div class="min-h-screen relative">
            <div class="w-full bg-pattern backdrop-blur-sm">
                <div class="px-4 py-4 flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <h1 class="text-2xl font-semibold dark:text-white">Create Event</h1>
                    </div>
                </div>

                <div class="px-4 py-4">
                    <CoreStepper :steps="formattedSteps" :current-step="currentStep"
                        :progress-percentage="progressPercentage" />
                </div>
            </div>
            <div class="mx-5 shadow-sm bg-white dark:bg-zinc-900 dark:border-zinc-800">
                <FormKit v-if="currentStep == 1" id="createEventForm" @submit="onFormSubmit" type="form"
                    submit-label="Update" @submit-invalid="onFormInvalid" :actions="false" #default="{ }">
                    <div class="flex-grow">
                        <div class="bg-white dark:bg-zinc-900 px-6 py-4 border-b dark:border-zinc-800">
                            <h3 class="text-lg text-black font-semibold dark:text-zinc-400">Basic Information</h3>
                        </div>

                        <div class="w-full relative bg-white dark:bg-zinc-900 sm:grid sm:grid-cols-3 gap-6 p-6">
                            <div class="col-span-2 order-1">
                                <div class="flex flex-col space-y-3">
                                    <FormKit type="text" name="title" label="Title" v-model="title"
                                        placeholder="Enter the title of your event" validation="required" :classes="{
                                            input: 'pl-0',
                                            prefixIcon: 'w-0 h-0'
                                        }" />

                                    <div class="form-group">
                                        <label
                                            class="block text-base font-medium mb-2 dark:text-zinc-200">Description</label>
                                        <RichTextEditor theme="snow" class="editor dark:text-white" required
                                            v-model:content="description" contentType="html" :editor="ClassicEditor"
                                            v-model="description" :config="editorConfig"></RichTextEditor>
                                    </div>

                                    <!-- Start & End Date -->
                                    <FormKit type="group" name="dateGroup">
                                        <label class="block text-base font-medium dark:text-zinc-200">Start & End
                                            Date</label>
                                        <datepicker @cleared="isCleared" required position="left"
                                            placeholder="Select start & end date" :range="true"
                                            input-class-name="w-full px-4 py-3 border border-gray-200 dark:border-zinc-700 dark:bg-zinc-800 dark:text-white"
                                            format="dd/MM/yyyy HH:mm" v-model="dateRange" />
                                    </FormKit>

                                    <FormKit type="group" name="categoryGroup">
                                        <label
                                            class="block text-base font-medium mb-3 dark:text-zinc-200">Category</label>
                                        <div class="w-full border">
                                            <CoreImageDropdown :items="$categories" v-model="selectedCategory" />
                                        </div>
                                    </FormKit>

                                    <FormKit type="radio" name="locationType" label="Location type"
                                        v-model="selectedLocationType" :options="[
                                            { label: 'Online', value: 'Online' },
                                            { label: 'Venue', value: 'Venue' }
                                        ]" :classes="{
                                        label: 'block text-base font-medium mb-2 dark:text-zinc-200',
                                        options: 'flex space-x-4 mt-1',
                                        option: 'flex items-center cursor-pointer',
                                        input: 'form-radio h-4 w-4 text-red-600 focus:ring-red-500 dark:bg-zinc-800',
                                        optionLabel: 'ml-2 dark:text-zinc-300'
                                    }" />

                                    <FormKit v-if="selectedLocationType === 'Online'" type="url" name="meetingLink"
                                        label="Meeting link" v-model="meetingLink"
                                        placeholder="Enter or copy & paste meeting link" validation="url"
                                        prefix-icon="globe" />

                                    <FormKit v-else-if="selectedLocationType == 'Venue'" type="group"
                                        name="locationGroup">
                                        <label class="block text-base font-medium mb-2 dark:text-zinc-200">Location
                                            Picker</label>
                                        <CoreLocationPicker @update:location="onUpdateLocation" />
                                        <div v-if="location?.address || location?.street"
                                            class="mt-3 p-3 bg-gray-50 dark:bg-zinc-800 border border-gray-200 dark:border-zinc-700 text-gray-700 dark:text-zinc-300">
                                            <div v-if="location?.address">{{ location?.address }}</div>
                                            <div v-else-if="location?.street">{{ location?.street }}, {{ location?.city
                                                }}, {{ location?.country }}</div>
                                        </div>
                                    </FormKit>

                                    <FormKit type="radio" name="visibility" label="Visibility"
                                        v-model="selectedVisibility" :options="[
                                            { label: 'Public', value: 'Public' },
                                            { label: 'Private', value: 'Private' }
                                        ]" :classes="{
                                        label: 'block text-base font-medium mb-2 dark:text-zinc-200',
                                        options: 'flex space-x-4 mt-1',
                                        option: 'flex items-center cursor-pointer',
                                        input: 'form-radio h-4 w-4 text-red-600 focus:ring-red-500 dark:bg-zinc-800',
                                        optionLabel: 'ml-2 dark:text-zinc-300'
                                    }" />
                                </div>
                            </div>

                            <div class="col-span-1 order-2">
                                <div
                                    class="bg-white dark:bg-zinc-900 border border-gray-200 dark:border-zinc-800 shadow-sm">
                                    <div
                                        class="w-full bg-gray-50 dark:bg-zinc-800 flex items-center justify-between border-b dark:border-zinc-700 px-4 py-3">
                                        <h3 class="text-lg font-medium dark:text-white">Uploads</h3>
                                        <button
                                            class="text-gray-500 dark:text-zinc-400 hover:text-gray-700 dark:hover:text-zinc-300">
                                            <Icon icon="lucide:minimize-2" class="w-5 h-5" />
                                        </button>
                                    </div>
                                    <div class="p-4">
                                        <h4 class="text-base font-medium mb-3 dark:text-zinc-200">Cover Art</h4>
                                        <EventsImagePicker @files-selected="onCoverPicker"
                                            @file-removed="onFileRemoved" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="hidden">
                        <CoreSubmitButton ref="submitButtonRef" color="primary" :loading="loading" />
                    </div>
                </FormKit>

                <FormKit v-else-if="currentStep === 2" id="createTicketsForm" @submit="onGenerateTickets" type="form"
                    submit-label="Update" @submit-invalid="onFormInvalid" :actions="false">
                    <div class="bg-white dark:bg-zinc-900 px-6 py-4 border-b dark:border-zinc-800">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg text-black font-semibold dark:text-zinc-100">Tickets</h3>
                            <button @click="onSkip(currentStep)"
                                class="flex items-center font-medium text-red-500 hover:text-red-600 transition-colors duration-200">
                                Skip
                                <Icon icon="heroicons:arrow-right" class="w-4 h-4 ml-1" />
                            </button>
                        </div>
                    </div>
                    <div class="w-full sm:grid sm:grid-cols-3 gap-6 p-6 bg-white dark:bg-zinc-900">
                        <div class="col-span-2 order-1">
                            <TicketsManager @update:selections="updateSelections" />
                            <TicketsGenerationSuccess :file="generationResponse" ref="ticketsGenerationRef"
                                @update:next="onNext" />
                        </div>
                        <div class="col-span-1 order-2">
                            <div
                                class="bg-gray-50 dark:bg-zinc-800 p-5 border border-zinc-100 border-dotted dark:border-zinc-700">
                                <div class="flex items-center text-lg font-medium mb-4 dark:text-white">
                                    <Icon icon="solar:help-bold-duotone" class="w-5 h-5 mr-2" />
                                    <h3>How to add and select tickets</h3>
                                </div>
                                <p class="mb-3 text-gray-600 dark:text-zinc-300">Follow these steps to select your
                                    tickets:</p>
                                <ol class="list-decimal list-inside mb-4 space-y-2 text-gray-600 dark:text-zinc-300">
                                    <li>Select a package from the dropdown menu.</li>
                                    <li>Specify the number of tickets you wish to purchase.</li>
                                    <li>If you need to add another package, click on the "Add another package" button.
                                    </li>
                                    <li>To remove a package selection, click the close button (X) on the top right of
                                        the package card.</li>
                                </ol>
                                <p class="mb-2 text-gray-600 dark:text-zinc-300">Make sure to review your selections
                                    before proceeding!</p>
                                <p class="text-gray-600 dark:text-zinc-300">If you have any questions, feel free to
                                    reach out to our support team.</p>
                            </div>
                        </div>
                    </div>
                </FormKit>

                <FormKit type="form" :actions="false" v-else-if="currentStep === 3" @submit="onSponsorSubmit">
                    <div class="bg-white dark:bg-zinc-900 px-6 py-4 border-b dark:border-zinc-800">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg text-black font-semibold dark:text-zinc-100">Sponsors</h3>
                            <div class="flex items-center">
                                <button type="button" @click="showSponsorManual = true"
                                    class="text-gray-500 dark:text-zinc-400 hover:text-gray-700 dark:hover:text-zinc-300 mr-3 sm:hidden"
                                    aria-label="Show Info Panel">
                                    <Icon icon="game-icons:info" class="w-5 h-5" />
                                </button>
                                <button type="button" @click="onSkip(currentStep)"
                                    class="flex items-center font-medium text-red-500 hover:text-red-600 transition-colors duration-200">
                                    Skip
                                    <Icon icon="heroicons:arrow-right" class="w-4 h-4 ml-1" />
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="w-full sm:grid sm:grid-cols-3 gap-6 bg-white dark:bg-zinc-900">
                        <div class="col-span-2">
                            <SponsorManager @update:selectedSponsors="getSelectedSponsors" />
                        </div>
                        <div :class="[
                            'col-span-1 bg-gray-50 dark:bg-zinc-800 p-5 border border-gray-200 dark:border-zinc-700 shadow-sm',
                            {
                                'hidden': !showSponsorManual,
                                'block fixed inset-0 z-50 bg-white dark:bg-zinc-900 p-4 overflow-auto': isMobile && showSponsorManual
                            }
                        ]" ref="infoPanel">
                            <div v-if="isMobile && showSponsorManual" class="flex justify-end mb-4">
                                <button @click="showSponsorManual = false"
                                    class="text-gray-500 dark:text-zinc-400 hover:text-gray-700 dark:hover:text-zinc-300">
                                    <Icon icon="heroicons:x-mark" class="w-6 h-6" />
                                </button>
                            </div>
                            <h2 class="flex items-center text-lg font-medium mb-4 dark:text-white">
                                <Icon icon="ic:baseline-info" class="w-5 h-5 mr-2 text-red-500" />
                                Sponsor Information
                            </h2>
                            <p class="text-gray-600 dark:text-zinc-300">
                                Adding sponsors to your event can increase visibility and provide additional resources.
                                Select from the available sponsors or skip this step if you don't have sponsors yet.
                            </p>
                        </div>
                    </div>
                </FormKit>

                <FormKit type="form" :actions="false" v-else-if="currentStep === 4" @submit="onPublishSubmit">
                    <div class="bg-white dark:bg-zinc-900 px-6 py-4 border-b dark:border-zinc-800">
                        <h3 class="text-lg text-black font-semibold dark:text-zinc-100">Publish Event</h3>
                        <p class="text-gray-600 dark:text-zinc-400 mt-1">Choose when you want your event to be visible
                            to the public</p>
                    </div>

                    <div class="w-full sm:grid sm:grid-cols-3 gap-6 p-6 bg-white dark:bg-zinc-900">
                        <div class="col-span-2 space-y-6">
                            <EventsPublish ref="publishRef" :event="eventDetails!" />

                            <div
                                class="bg-white dark:bg-zinc-900 p-6 border border-gray-200 dark:border-zinc-800 shadow-sm">
                                <h4 class="text-base font-medium mb-4 dark:text-zinc-200">Publishing Options</h4>
                                <FormKit type="radio" name="publishType" v-model="publishType" :options="[
                                    { label: 'Publish now', value: 'immediate' },
                                    { label: 'Schedule for later', value: 'schedule' }
                                ]" :classes="{
                                    options: 'space-y-4',
                                    option: 'flex items-center cursor-pointer',
                                    input: 'form-radio h-4 w-4 text-red-600 focus:ring-red-500 dark:bg-zinc-800',
                                    optionLabel: 'ml-2 dark:text-zinc-300'
                                }" />

                                <div v-if="publishType === 'schedule'" class="mt-4">
                                    <FormKit type="group" name="scheduleDateGroup">
                                        <datepicker v-model="scheduleDate"
                                            placeholder="Enter the publishing date and time"
                                            input-class-name="w-full px-4 py-3 border border-gray-200 dark:border-zinc-700 dark:bg-zinc-800 dark:text-white" />
                                    </FormKit>
                                </div>
                            </div>

                            <div
                                class="bg-white dark:bg-zinc-900 p-6 border border-gray-200 dark:border-zinc-800 shadow-sm">
                                <h4 class="text-base font-medium mb-4 dark:text-zinc-200">Event Preview</h4>
                                <div class="space-y-4">
                                    <div class="flex space-x-4">
                                        <img :src="`${runtimeConfig.public.baseUrl}storage/events/${eventDetails?.cover_art}`"
                                            alt="event-cover"
                                            class="w-24 h-24 object-cover border border-gray-200 dark:border-zinc-700" />
                                        <div>
                                            <h5 class="font-medium text-lg dark:text-white">{{ eventDetails?.title }}
                                            </h5>
                                            <p class="text-gray-600 dark:text-zinc-400">
                                                {{ dayjs(eventDetails?.start).format('MMM D, YYYY h:mm A') }}
                                            </p>
                                        </div>
                                    </div>
                                    <div class="text-gray-600 dark:text-zinc-400 prose dark:prose-invert max-w-none"
                                        v-html="eventDetails?.description"></div>
                                </div>
                            </div>
                        </div>

                        <div class="col-span-1">
                            <div
                                class="bg-gray-50 dark:bg-zinc-800 p-6 border border-gray-200 dark:border-zinc-700 shadow-sm">
                                <h4 class="text-base font-medium mb-4 dark:text-white">Publishing Checklist</h4>
                                <ul class="space-y-4">
                                    <li class="flex items-center">
                                        <Icon
                                            :icon="eventDetails?.title ? 'heroicons:check-circle' : 'heroicons:x-circle'"
                                            class="w-5 h-5 mr-2"
                                            :class="eventDetails?.title ? 'text-green-500' : 'text-red-500'" />
                                        <span class="dark:text-zinc-300">Event title and description</span>
                                    </li>
                                    <li class="flex items-center">
                                        <Icon
                                            :icon="eventDetails?.start || eventDetails?.end ? 'heroicons:check-circle' : 'heroicons:x-circle'"
                                            class="w-5 h-5 mr-2"
                                            :class="eventDetails?.start || eventDetails?.end ? 'text-green-500' : 'text-red-500'" />
                                        <span class="dark:text-zinc-300">Event date and time</span>
                                    </li>
                                    <li class="flex items-center">
                                        <Icon
                                            :icon="eventDetails?.category_id ? 'heroicons:check-circle' : 'heroicons:x-circle'"
                                            class="w-5 h-5 mr-2"
                                            :class="eventDetails?.category_id ? 'text-green-500' : 'text-red-500'" />
                                        <span class="dark:text-zinc-300">Event category</span>
                                    </li>
                                    <li class="flex items-center">
                                        <Icon
                                            :icon="eventDetails?.cover_art ? 'heroicons:check-circle' : 'heroicons:x-circle'"
                                            class="w-5 h-5 mr-2"
                                            :class="eventDetails?.cover_art ? 'text-green-500' : 'text-red-500'" />
                                        <span class="dark:text-zinc-300">Cover image</span>
                                    </li>
                                </ul>

                                <div
                                    class="mt-6 p-4 bg-yellow-50 dark:bg-amber-900/20 border border-yellow-100 dark:border-amber-900/30">
                                    <p class="text-yellow-700 dark:text-yellow-500 text-sm flex items-start">
                                        <Icon icon="heroicons:exclamation-triangle"
                                            class="w-5 h-5 mr-2 flex-shrink-0" />
                                        <span>Once published, basic event details cannot be modified. Make sure all
                                            information is correct.</span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </FormKit>
            </div>
            <div
                class="absolute bottom-0 left-0 right-0 bg-white dark:bg-black shadow border-t dark:border-zinc-800 p-4">
                <div class="flex justify-between items-center">
                    <button v-if="currentStep > 1" @click="currentStep--"
                        class="border dark:border-zinc-700 text-gray-700 dark:text-zinc-300 hover:bg-gray-50 dark:hover:bg-zinc-800 px-4 py-2 flex items-center transition-colors duration-200">
                        <Icon icon="fluent:arrow-previous-16-regular" class="w-5 h-5 mr-2" />
                        Previous
                    </button>
                    <div v-else></div>

                    <button @click="navigateNextStep"
                        class="bg-red-600 hover:bg-red-700 text-white flex items-center px-4 py-2 transition-colors duration-200">
                        {{ currentStep === 1 ? "Create Event" : currentStep === 2 ? "Generate tickets & Continue" :
                            currentStep === 3 ? "Add Sponsors & Continue" : "Publish Event" }}
                        <Icon icon="fluent:arrow-next-16-regular" class="w-5 h-5 ml-2" />
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import type { Category, EventItem, Sponsor } from '@/types';
import { ClassicEditor, Bold, Essentials, Heading, BlockQuote, Table, MediaEmbed, Font, FontColor, FontSize, Italic, Mention, Paragraph, Undo, Link, InputNumberView, List, } from 'ckeditor5';
import dayjs from 'dayjs';

definePageMeta({
    layout: "dashboard",
});
useHead({
    title: "Create Event - EventaHub Malawi",
});

const currentStep = ref<number>(1);
const route = useRoute();
const routerQuery = useRouterQuery();
const submitButtonRef = ref()
const title = ref<string>("");
const steps = ref([
    { name: 'Basic Information', completed: false },
    { name: 'Tickets', completed: false },
    { name: 'Sponsors', completed: false },
    { name: 'Publish', completed: false }
])

const formattedSteps = computed(() => {
    return steps.value.map((step, index) => ({
        step: index + 1,
        label: step.name,
        sublabel: step.completed ? 'Completed' : ''
    }));
});

const progressPercentage = computed(() => {
    const completedSteps = steps.value.filter(step => step.completed).length;
    return Math.round((completedSteps / steps.value.length) * 100);
});
const description = ref<string>("");
const selectedCategory = ref<Category>({ id: 0, name: "select Category", icon: "other.png" });
const selectedLocationType = ref<string>("");
const selectedVisibility = ref<string>("");
const dateRange = ref<any>([]);
const ticketsGenerationRef = ref<any>(null);
const location = ref();
const coverArt = ref();
const meetingLink = ref<string>("");
const meetingIcon = ref<string>("globe");
const loading = ref<boolean>(false);
const { $toast, $categories }: any = useNuxtApp();
const httpClient = useHttpClient();
const selections = ref<any>([]);
const sponsors = ref<Sponsor[]>([]);
const generationResponse = ref<string>("");
const showSponsorManual = ref<boolean>(false);

const { start, finish } = useLoadingIndicator();

const editorConfig = {
    plugins: [Bold, Essentials, Heading, Italic, BlockQuote, Table, Font, FontColor, FontSize, MediaEmbed, Mention, Paragraph, Undo, Link, InputNumberView, List],
    toolbar: ['heading',
        '|',
        'bold',
        'italic',
        'link',
        'bulletedList',
        'numberedList',
        'blockQuote',
        'insertTable',
        'mediaEmbed',
        'undo',
        'redo',
        'imageUpload',
        'fontSize',
        'fontColor',
        'highlight'],
}

const publishRef = ref<any>(null);

const runtimeConfig = useRuntimeConfig();

const publishType = ref<string>('immediate');
const scheduleDate = ref<string>('');

const eventDetails = ref<EventItem>()

const isMobile = computed((): boolean => {
    return window.innerWidth < 640;
});

const getLocationOnBasis = computed((): string => {
    if (location.value?.address) {
        return location.value.address;
    }
    const street = location.value?.street || '';
    const city = location.value?.city || '';
    const country = location.value?.country || '';
    const parts = [street, city, country].filter(part => part).join(', ');
    return parts || "";
});

const onSkip = (step: number) => {
    routerQuery.replaceOneQuery("step", { step: step + 1 });
    currentStep.value = step + 1;
}

const onFormInvalid = (e: any) => {
    console.log(e);
};

const stepActions = {
    1: () => submitButtonRef.value?.$el.click(),
    2: () => onGenerateTickets(),
    3: () => onSponsorSubmit(),
    4: () => onPublishSubmit(),
    default: () => currentStep.value++
};

const navigateNextStep = () => {
    const action = stepActions[currentStep.value as keyof typeof stepActions] || stepActions.default;
    action();
};

const onNext = (value: boolean) => {
    if (value) {
        setTimeout(() => {
            routerQuery.replaceOneQuery("step", { step: 3 });
            currentStep.value = 3;
        }, 500);
    }
}

const isCleared = (event: any): void => {
    console.log(event);
};

const onUpdateLocation = (e: any): void => {
    location.value = e;
}

const updateSelections = (e: any[]): void => {
    selections.value = e;
};

const onFileRemoved = (): void => {
    $toast.warn("Cover art removed, please upload a new one");
}

const onCoverPicker = (e: File[]): void => {
    coverArt.value = e[0];
}

const getSelectedSponsors = (s: Sponsor[]): void => {
    sponsors.value = s;
}

const onFormSubmit = async (): Promise<void> => {
    loading.value = true;
    start();
    try {
        const formData = new FormData();
        formData.append("title", title.value);
        formData.append("description", description.value);
        formData.append("category", String(selectedCategory?.value?.id));
        formData.append("location", getLocationOnBasis.value || "Remote");
        formData.append("visibility", selectedVisibility.value == "Public" ? "1" : "0");
        formData.append("type", selectedLocationType.value == "Public" ? "1" : "0");
        formData.append("dateRange", dateRange.value);
        formData.append("meeting_link", meetingLink.value);
        formData.append("meetingIcon", meetingIcon.value);
        formData.append("start_date", dayjs(dateRange.value[0]).format("YYYY-MM-DD HH:mm:ss"));
        formData.append("end_date", dayjs(dateRange.value[1]).format("YYYY-MM-DD HH:mm:ss"));
        formData.append("locationType", selectedLocationType.value);
        formData.append("latitude", location?.value?.latlong?.lat ?? "");
        formData.append("longitude", location?.value?.latlong?.lng ?? "");
        formData.append("cover_art", coverArt.value);

        const response: { message: string, event: EventItem } = await httpClient.post(ENDPOINTS.EVENTS.CREATE, formData, {
            onError: (error: any) => {
                $toast.error(error.message);
            }
        });
        if (response) {
            eventDetails.value = response.event;
            $toast.success(response.message);
            steps.value[0].completed = true;
            routerQuery.replaceOneQuery("event_id", { event_id: response.event.id });
            setTimeout(() => {
                currentStep.value = 2;
                routerQuery.replaceOneQuery("step", { step: 2 });
            }, 1000)
        }
    } catch (error: any) {
        if (error) {
            const errors = error.errors;
            Object.keys(errors).forEach((key) => {
                if (Array.isArray(errors[key])) {
                    errors[key].forEach((message: string) => {
                        $toast.error(message);
                    });
                } else if (typeof errors[key] === 'string') {
                    $toast.error(errors[key]);
                }
            });
        } else if (error.message) {
            $toast.error(error.message);
        } else {
            $toast.error('Something went wrong, please try again later');
        }
        finish();
    } finally {
        loading.value = false;
        finish();
    }
};

const onGenerateTickets = async (): Promise<void> => {
    loading.value = true;
    start();
    try {
        const formData = new FormData();
        formData.append("event_id", String(route.query.event_id));
        formData.append("tickets", JSON.stringify(selections.value));

        const response = await httpClient.post<any>(ENDPOINTS.TICKETS.GENERATE, formData);
        if (response) {
            generationResponse.value = response.pdf;
            steps.value[1].completed = true;
            $toast.success(response.message);
            if (ticketsGenerationRef.value) {
                ticketsGenerationRef.value.openModal();
            }
        }
    } catch (error: any) {
        if (error) {
            const errors = error.errors;
            Object.keys(errors).forEach((key) => {
                if (Array.isArray(errors[key])) {
                    errors[key].forEach((message: string) => {
                        $toast.error(message);
                    });
                } else if (typeof errors[key] === 'string') {
                    $toast.error(errors[key]);
                }
            });
        } else if (error.message) {
            $toast.error(error.message);
        } else {
            $toast.error('Something went wrong while publishing the event');
        }

    } finally {
        loading.value = false;
        finish();
    }
}

const onSponsorSubmit = async (): Promise<void> => {
    loading.value = true;
    start();
    try {
        const formData = new FormData();
        formData.append('sponsors[]', JSON.stringify(sponsors.value.map((sponsor: Sponsor) => sponsor.id)));
        formData.append('event_id', String(route.query.event_id));
        const response = await httpClient.post<any>(ENDPOINTS.EVENTS.SPONSOR, formData);
        if (response) {
            steps.value[3].completed = true;
            $toast.success(response.message);
            currentStep.value = 4;
            routerQuery.replaceOneQuery("step", { step: 4 });
        }
    } catch (error: any) {
        if (error) {
            const errors = error.errors;
            Object.keys(errors).forEach((key) => {
                if (Array.isArray(errors[key])) {
                    errors[key].forEach((message: string) => {
                        $toast.error(message);
                    });
                } else if (typeof errors[key] === 'string') {
                    $toast.error(errors[key]);
                }
            });
        } else if (error.message) {
            $toast.error(error.message);
        } else {
            $toast.error('Something went wrong while publishing the event');
        }
    } finally {
        loading.value = false;
        finish();
    }
}

const getEventDetails = async () => {
    loading.value = true;
    start();
    try {
        const eventId = route.query.event_id as string;
        const response = await httpClient.get<{ event: EventItem }>(`${ENDPOINTS.EVENTS.READ}/${eventId}`);
        if (response) {
            eventDetails.value = response.event;
        }
    } catch (error: any) {
        if (error) {
            const errors = error.errors;
            Object.keys(errors).forEach((key) => {
                if (Array.isArray(errors[key])) {
                    errors[key].forEach((message: string) => {
                        $toast.error(message);
                    });
                } else if (typeof errors[key] === 'string') {
                    $toast.error(errors[key]);
                }
            });
        } else if (error.message) {
            $toast.error(error.message);
        } else {
            $toast.error('Something went wrong while publishing the event');
        }

    } finally {
        loading.value = false;
        finish();
    }
}

const onPublishSubmit = async () => {
    loading.value = true;
    start();
    try {
        const formData = new FormData();
        formData.append('event_id', String(route.query.event_id));
        formData.append('publish_type', publishType.value);
        if (publishType.value === 'schedule') {
            formData.append('scheduled_at', dayjs(scheduleDate.value).format('YYYY-MM-DD HH:mm:ss'));
        }

        const response = await httpClient.post<any>(ENDPOINTS.EVENTS.PUBLISH, formData);
        if (response) {
            steps.value[3].completed = true;
            $toast.success(response.message);
            if (publishRef.value) {
                publishRef.value.openModal();
            }
        }
    } catch (error: any) {
        if (error) {
            const errors = error.errors;
            Object.keys(errors).forEach((key) => {
                if (Array.isArray(errors[key])) {
                    errors[key].forEach((message: string) => {
                        $toast.error(message);
                    });
                } else if (typeof errors[key] === 'string') {
                    $toast.error(errors[key]);
                }
            });
        } else if (error.message) {
            $toast.error(error.message);
        } else {
            $toast.error('Something went wrong while publishing the event');
        }
    }
    finally {
        loading.value = false;
        finish();
    }
};

watch((meetingLink), (newMeetingLink) => {
    const url = newMeetingLink.toLowerCase();
    const iconMap = {
        'zoom.us': 'zoom',
        'teams.microsoft.com': 'teams',
        'meet.google.com': 'meet',
    };
    let icon = "globe";
    for (const [keyword, mappedIcon] of Object.entries(iconMap)) {
        if (url.includes(keyword)) {
            icon = mappedIcon;
        }
    }
    meetingIcon.value = icon;
});

onMounted(() => {
    if (route.query.step) {
        currentStep.value = Number(route.query.step);
    }
});

onMounted(() => {
    if (route.query.event_id) {
        getEventDetails();
    }
});
</script>

<style lang="css" scoped>
.bg-pattern {
    background-image: radial-gradient(circle at 1px 1px, rgb(226 232 240 / 0.5) 1px, transparent 0);
    background-size: 24px 24px;
}
</style>