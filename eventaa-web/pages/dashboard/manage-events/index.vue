<template>
  <div class="w-full h-full bg-gray-50 dark:bg-zinc-900 min-h-screen">
    <div class="bg-white dark:bg-zinc-800 shadow-sm border-b border-gray-200 dark:border-zinc-700">
      <div class="mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center py-3 text-sm">
          <nav class="flex" aria-label="Breadcrumb">
            <ol class="flex items-center space-x-2">
              <li>
                <div class="flex items-center">
                  <a href="/dashboard" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300">
                    Dashboard
                  </a>
                </div>
              </li>
              <li>
                <div class="flex items-center">
                  <Icon icon="heroicons:chevron-right" class="w-4 h-4 text-gray-400 dark:text-gray-500 mx-2" />
                  <span class="text-gray-900 dark:text-gray-100 font-medium">Event Management</span>
                </div>
              </li>
            </ol>
          </nav>
        </div>

        <div class="flex items-center justify-between pb-6">
          <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">Event Management</h1>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Manage your events, bookings, and attendees</p>
          </div>
          <div class="flex items-center space-x-3">
            <!-- Export Button Group -->
            <div class="flex items-center space-x-2">
              <button
                @click="exportToPDF"
                class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-zinc-800 hover:bg-gray-50 dark:hover:bg-zinc-700 focus:outline-none transition-colors border border-gray-300 dark:border-zinc-600"
              >
                <Icon icon="heroicons:document-arrow-down" class="w-4 h-4 mr-1" />
                PDF
              </button>
              <button
                @click="exportToExcel"
                class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-zinc-800 hover:bg-gray-50 dark:hover:bg-zinc-700 focus:outline-none transition-colors border border-gray-300 dark:border-zinc-600"
              >
                <Icon icon="heroicons:table-cells" class="w-4 h-4 mr-1" />
                Excel
              </button>
            </div>

            <!-- Filters Popover Button -->
            <Popover class="relative">
              <PopoverButton class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-zinc-600 shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-zinc-800 hover:bg-gray-50 dark:hover:bg-zinc-700 focus:outline-none transition-colors">
                <Icon icon="heroicons:funnel" class="w-4 h-4 mr-2" />
                Filters
                <span v-if="hasActiveFilters" class="ml-2 inline-flex items-center justify-center w-5 h-5 text-xs font-medium text-white bg-red-500 rounded-full">
                  {{ activeFiltersCount }}
                </span>
              </PopoverButton>

              <transition
                enter-active-class="transition duration-200 ease-out"
                enter-from-class="translate-y-1 opacity-0"
                enter-to-class="translate-y-0 opacity-100"
                leave-active-class="transition duration-150 ease-in"
                leave-from-class="translate-y-0 opacity-100"
                leave-to-class="translate-y-1 opacity-0"
              >
                <PopoverPanel class="absolute right-0 z-10 mt-2 w-80 origin-top-right">
                  <div class="bg-white dark:bg-zinc-800 shadow-lg border border-gray-200 dark:border-zinc-700">
                    <div class="p-4 border-b border-gray-200 dark:border-zinc-700">
                      <div class="flex items-center justify-between">
                        <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100">Filters & Search</h3>
                        <button
                          v-if="hasActiveFilters"
                          @click="clearFilters"
                          class="inline-flex items-center px-2 py-1 text-xs font-medium text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"
                        >
                          <Icon icon="heroicons:x-mark" class="w-3 h-3 mr-1" />
                          Clear All
                        </button>
                      </div>
                    </div>

                    <div class="p-4 space-y-4">
                      <!-- Search Input -->
                      <div class="space-y-2">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Search Events</label>
                        <div class="relative">
                          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <Icon icon="heroicons:magnifying-glass" class="h-4 w-4 text-gray-400 dark:text-gray-500" />
                          </div>
                          <input
                            v-model="searchQuery"
                            type="text"
                            placeholder="Search by title, description..."
                            class="block w-full pl-9 pr-3 py-2 border border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:ring-red-500 focus:border-red-500 text-sm"
                          />
                        </div>
                      </div>

                      <!-- Status Filter -->
                      <div class="space-y-2">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Status</label>
                        <Listbox v-model="selectedStatus" @update:model-value="onStatusChange">
                          <div class="relative">
                            <ListboxButton class="relative w-full cursor-pointer bg-white dark:bg-zinc-700 py-2 pl-3 pr-8 text-left border border-gray-300 dark:border-zinc-600 focus:outline-none focus:ring-red-500 focus:border-red-500 text-sm">
                              <span class="block truncate text-gray-900 dark:text-gray-100">{{ selectedStatus.name }}</span>
                              <span class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                                <Icon icon="heroicons:chevron-up-down" class="h-4 w-4 text-gray-400 dark:text-gray-500" />
                              </span>
                            </ListboxButton>
                            <transition
                              leave-active-class="transition duration-100 ease-in"
                              leave-from-class="opacity-100"
                              leave-to-class="opacity-0"
                            >
                              <ListboxOptions class="absolute z-10 mt-1 max-h-48 w-full overflow-auto bg-white dark:bg-zinc-700 py-1 text-sm shadow-lg ring-1 ring-black ring-opacity-5 dark:ring-zinc-600 focus:outline-none">
                                <ListboxOption
                                  v-for="status in statusOptions"
                                  :key="status.value"
                                  :value="status"
                                  v-slot="{ active, selected }"
                                  as="template"
                                >
                                  <li
                                    :class="[
                                      active ? 'bg-red-50 dark:bg-red-900/20 text-red-900 dark:text-red-100' : 'text-gray-900 dark:text-gray-100',
                                      'relative cursor-pointer select-none py-2 pl-3 pr-8'
                                    ]"
                                  >
                                    <span :class="[selected ? 'font-semibold' : 'font-normal', 'block truncate']">
                                      {{ status.name }}
                                    </span>
                                    <span
                                      v-if="selected"
                                      :class="[
                                        active ? 'text-red-600 dark:text-red-400' : 'text-red-600 dark:text-red-400',
                                        'absolute inset-y-0 right-0 flex items-center pr-2'
                                      ]"
                                    >
                                      <Icon icon="heroicons:check" class="h-4 w-4" />
                                    </span>
                                  </li>
                                </ListboxOption>
                              </ListboxOptions>
                            </transition>
                          </div>
                        </Listbox>
                      </div>

                      <!-- Date Range Filter -->
                      <div class="space-y-2">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Date Range</label>
                        <Listbox v-model="selectedDateRange" @update:model-value="onDateRangeChange">
                          <div class="relative">
                            <ListboxButton class="relative w-full cursor-pointer bg-white dark:bg-zinc-700 py-2 pl-3 pr-8 text-left border border-gray-300 dark:border-zinc-600 focus:outline-none focus:ring-red-500 focus:border-red-500 text-sm">
                              <span class="block truncate text-gray-900 dark:text-gray-100">{{ selectedDateRange.name }}</span>
                              <span class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                                <Icon icon="heroicons:chevron-up-down" class="h-4 w-4 text-gray-400 dark:text-gray-500" />
                              </span>
                            </ListboxButton>
                            <transition
                              leave-active-class="transition duration-100 ease-in"
                              leave-from-class="opacity-100"
                              leave-to-class="opacity-0"
                            >
                              <ListboxOptions class="absolute z-10 mt-1 max-h-48 w-full overflow-auto bg-white dark:bg-zinc-700 py-1 text-sm shadow-lg ring-1 ring-black ring-opacity-5 dark:ring-zinc-600 focus:outline-none">
                                <ListboxOption
                                  v-for="dateRange in dateRangeOptions"
                                  :key="dateRange.value"
                                  :value="dateRange"
                                  v-slot="{ active, selected }"
                                  as="template"
                                >
                                  <li
                                    :class="[
                                      active ? 'bg-red-50 dark:bg-red-900/20 text-red-900 dark:text-red-100' : 'text-gray-900 dark:text-gray-100',
                                      'relative cursor-pointer select-none py-2 pl-3 pr-8'
                                    ]"
                                  >
                                    <span :class="[selected ? 'font-semibold' : 'font-normal', 'block truncate']">
                                      {{ dateRange.name }}
                                    </span>
                                    <span
                                      v-if="selected"
                                      :class="[
                                        active ? 'text-red-600 dark:text-red-400' : 'text-red-600 dark:text-red-400',
                                        'absolute inset-y-0 right-0 flex items-center pr-2'
                                      ]"
                                    >
                                      <Icon icon="heroicons:check" class="h-4 w-4" />
                                    </span>
                                  </li>
                                </ListboxOption>
                              </ListboxOptions>
                            </transition>
                          </div>
                        </Listbox>
                      </div>

                      <!-- Active Filters Display -->
                      <div v-if="hasActiveFilters" class="pt-3 border-t border-gray-200 dark:border-zinc-700">
                        <div class="flex items-center space-x-2">
                          <span class="text-xs font-medium text-gray-700 dark:text-gray-300">Active:</span>
                          <div class="flex flex-wrap gap-1">
                            <span
                              v-if="searchQuery.trim()"
                              class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/30 border border-blue-300 dark:border-blue-700 text-blue-800 dark:text-blue-200"
                            >
                              Search
                              <button
                                @click="searchQuery = ''"
                                class="ml-1 inline-flex items-center justify-center w-3 h-3 rounded-full text-blue-400 dark:text-blue-300 hover:bg-blue-200 dark:hover:bg-blue-800"
                              >
                                <Icon icon="heroicons:x-mark" class="w-2 h-2" />
                              </button>
                            </span>
                            <span
                              v-if="statusFilter"
                              class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/30 border border-green-300 dark:border-green-700 text-green-800 dark:text-green-200"
                            >
                              {{ selectedStatus.name }}
                              <button
                                @click="selectedStatus = statusOptions[0]; onStatusChange(statusOptions[0])"
                                class="ml-1 inline-flex items-center justify-center w-3 h-3 rounded-full text-green-400 dark:text-green-300 hover:bg-green-200 dark:hover:bg-green-800"
                              >
                                <Icon icon="heroicons:x-mark" class="w-2 h-2" />
                              </button>
                            </span>
                            <span
                              v-if="dateRangeFilter"
                              class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-purple-100 dark:bg-purple-900/30 border border-purple-300 dark:border-purple-700 text-purple-800 dark:text-purple-200"
                            >
                              {{ selectedDateRange.name }}
                              <button
                                @click="selectedDateRange = dateRangeOptions[0]; onDateRangeChange(dateRangeOptions[0])"
                                class="ml-1 inline-flex items-center justify-center w-3 h-3 rounded-full text-purple-400 dark:text-purple-300 hover:bg-purple-200 dark:hover:bg-purple-800"
                              >
                                <Icon icon="heroicons:x-mark" class="w-2 h-2" />
                              </button>
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </PopoverPanel>
              </transition>
            </Popover>

            <CorePrimaryButton
              @click="$router.push('/dashboard/manage-events/create')"
              text="Create Event"
              start-icon="heroicons:plus"
            />
          </div>
        </div>
      </div>
    </div>

    <div class="mx-auto px-4 sm:px-6 lg:px-8 py-6">

      <EventsDashboardDatatable
        :headers="headers"
        :events="filteredEvents"
        :totalItems="filteredEvents.length"
        :loading="loading"
        :server-options="serverOptions"
        @update:events="fetchUserEvents"
        @update:options="handleOptionsUpdate"
      />
    </div>
    </div>
    <TransitionRoot appear :show="viewDialogOpen" as="template">
      <Dialog as="div" @close="closeViewDialog" class="relative z-10">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black/25 dark:bg-black/50" />
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div
            class="flex min-h-full items-center justify-center p-4 text-center"
          >
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel
                class="w-full max-w-3xl transform overflow-hidden bg-white dark:bg-zinc-800 text-left align-middle shadow-xl transition-all"
              >
                <div class="px-6 py-4 border-b border-gray-200 dark:border-zinc-700">
                  <DialogTitle
                    as="h3"
                    class="text-lg font-medium leading-6 text-gray-900 dark:text-gray-100"
                  >
                    {{ selectedEvent?.title }}
                  </DialogTitle>
                </div>

                <div class="p-6">
                  <div class="space-y-4">
                    <div>
                      <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Description
                      </h4>
                      <div
                        class="mt-1 text-sm text-gray-500 dark:text-gray-400"
                        v-html="selectedEvent?.description"
                      ></div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">
                          Location
                        </h4>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                          {{ selectedEvent?.location }}
                        </p>
                      </div>

                      <div>
                        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">
                          Status
                        </h4>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                          {{ selectedEvent?.status }}
                        </p>
                      </div>

                      <div>
                        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">
                          Start Date
                        </h4>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                          {{ selectedEvent?.start }}
                        </p>
                      </div>

                      <div>
                        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">
                          End Date
                        </h4>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                          {{ selectedEvent?.end }}
                        </p>
                      </div>

                      <div>
                        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">
                          Published At
                        </h4>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                          {{ selectedEvent?.published_at }}
                        </p>
                      </div>

                      <div>
                        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">
                          Date Created
                        </h4>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                          {{ selectedEvent?.created_at }}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="px-6 py-4 bg-gray-50 dark:bg-zinc-700 flex justify-end">
                  <CorePrimaryButton @click="closeViewDialog" text="Close" />
                </div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
</template>

<script setup lang="ts">
import {
  Listbox,
  ListboxButton,
  ListboxOptions,
  ListboxOption,
  Dialog,
  DialogPanel,
  DialogTitle,
  TransitionRoot,
  TransitionChild,
  Popover,
  PopoverButton,
  PopoverPanel,
} from "@headlessui/vue";
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';

definePageMeta({
  layout: "dashboard",
});
useHead({
  title: "Event Management - EventaHub Malawi",
});

import type { Header } from "vue3-easy-data-table";
import type { EventItem } from "@/types";
import type { FilterOption, ServerOptions } from "@/types/datatable";
import dayjs from "dayjs";

const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();
const headers: Header[] = [
  { text: "TITLE", value: "title", sortable: true },
  { text: "DESCRIPTION", value: "description", sortable: true },
  { text: "LOCATION", value: "location", sortable: true },
  { text: "START DATE", value: "start", sortable: true },
  { text: "END DATE", value: "end", sortable: true },
  { text: "STATUS", value: "status", sortable: true },
  { text: "PUBLISHED AT", value: "published_at", sortable: true },
  { text: "DATE CREATED", value: "created_at", sortable: true },
  { text: "ACTIONS", value: "actions" },
];

const loading = ref<boolean>(false);
const events = ref<EventItem[]>([]);
const totalItems = ref<number>(0);
const searchQuery = ref<string>("");
const statusFilter = ref<string | number>("");
const dateRangeFilter = ref<string | number>("");
const itemsPerPage = ref<number>(25);
const currentPage = ref<number>(1);
const viewDialogOpen = ref<boolean>(false);

interface DisplayEventItem extends EventItem {
  status?: string;
}

const selectedEvent = ref<DisplayEventItem | null>(null);

const serverOptions = ref<ServerOptions>({
  page: 1,
  rowsPerPage: 25,
});
const statusOptions: FilterOption[] = [
  { name: "All Status", value: "" },
  { name: "Published", value: "published" },
  { name: "Draft", value: "draft" },
  { name: "Cancelled", value: "cancelled" },
];

const dateRangeOptions: FilterOption[] = [
  { name: "All Dates", value: "" },
  { name: "Today", value: "today" },
  { name: "This Week", value: "week" },
  { name: "This Month", value: "month" },
  { name: "Upcoming", value: "upcoming" },
  { name: "Past Events", value: "past" },
];

const selectedStatus = ref<FilterOption>(statusOptions[0]);
const selectedDateRange = ref<FilterOption>(dateRangeOptions[0]);

const formattedEvents = computed(() => {
  return events.value.map((event) => {
    return {
      ...event,
      start: dayjs(event.start).format("DD/MM/YYYY HH:mm"),
      end: dayjs(event.end).format("DD/MM/YYYY HH:mm"),
      created_at: dayjs(event.created_at).format("DD/MM/YYYY"),
      published_at: event.published_at
        ? dayjs(event.published_at).format("DD/MM/YYYY")
        : "Not Published",
      status: getEventStatus(event),
    };
  });
});

const filteredEvents = computed(() => {
  let filtered = [...formattedEvents.value];
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase().trim();
    filtered = filtered.filter(
      (event) =>
        event.title?.toLowerCase().includes(query) ||
        event.description?.toLowerCase().includes(query) ||
        event.location?.toLowerCase().includes(query)
    );
  }

  if (statusFilter.value) {
    const statusVal = String(statusFilter.value).toLowerCase();
    filtered = filtered.filter(
      (event) => event.status?.toLowerCase() === statusVal
    );
  }

  if (dateRangeFilter.value) {
    const now = dayjs();
    filtered = filtered.filter((event) => {
      const eventStart = dayjs(event.start, "DD/MM/YYYY HH:mm");

      switch (dateRangeFilter.value) {
        case "today":
          return eventStart.isSame(now, "day");
        case "week":
          return (
            eventStart.isAfter(now.startOf("week")) &&
            eventStart.isBefore(now.endOf("week"))
          );
        case "month":
          return (
            eventStart.isAfter(now.startOf("month")) &&
            eventStart.isBefore(now.endOf("month"))
          );
        case "upcoming":
          return eventStart.isAfter(now);
        case "past":
          return eventStart.isBefore(now);
        default:
          return true;
      }
    });
  }

  return filtered;
});

const hasActiveFilters = computed(() => {
  return (
    searchQuery.value.trim() !== "" ||
    statusFilter.value !== "" ||
    dateRangeFilter.value !== ""
  );
});

const activeFiltersCount = computed(() => {
  let count = 0;
  if (searchQuery.value.trim()) count++;
  if (statusFilter.value) count++;
  if (dateRangeFilter.value) count++;
  return count;
});

const getEventStatus = (event: EventItem): string => {
  const now = dayjs();
  const eventStart = dayjs(event.start);
  const eventEnd = dayjs(event.end);

  if (!event.published_at) return "Draft";
  if (eventEnd.isBefore(now)) return "Completed";
  if (eventStart.isAfter(now)) return "Upcoming";
  if (eventStart.isBefore(now) && eventEnd.isAfter(now)) return "Live";
  return "Published";
};

const fetchUserEvents = async (): Promise<void> => {
  loading.value = true;
  try {
    const response = await httpClient.get<any>(ENDPOINTS.EVENTS.USER);

    if (response) {
      events.value = response.events.data;
      totalItems.value = response.events.total;
    }
  } catch (error) {
    $toast.error("An error occurred while fetching events.");
  } finally {
    loading.value = false;
  }
};

const closeViewDialog = (): void => {
  viewDialogOpen.value = false;
  selectedEvent.value = null;
};

const exportToPDF = async (): Promise<void> => {
  loading.value = true;
  try {
    $toast.info('Generating PDF...');

    const doc = new jsPDF();
    const tableColumn = ["Title", "Location", "Start Date", "End Date", "Status", "Attendees", "Likes"];
    const tableRows: any[] = [];

    formattedEvents.value.forEach(event => {
      const eventData = [
        event.title,
        event.location,
        event.start,
        event.end,
        event.status || 'N/A',
        event.attendees_count || 0,
        event.likes_count || 0
      ];
      tableRows.push(eventData);
    });

    // Add title
    doc.setFontSize(20);
    doc.text('Events Export', 14, 22);
    doc.setFontSize(12);
    doc.text(`Generated on: ${dayjs().format('MMMM D, YYYY')}`, 14, 32);
    doc.text(`Total Events: ${formattedEvents.value.length}`, 14, 42);

    autoTable(doc, {
      head: [tableColumn],
      body: tableRows,
      startY: 50,
      theme: 'grid',
      styles: {
        fontSize: 10,
        cellPadding: 3,
        overflow: 'linebreak',
      },
      headStyles: {
        fillColor: [220, 38, 38],
        textColor: 255,
        fontStyle: 'bold',
      },
      alternateRowStyles: {
        fillColor: [245, 245, 245]
      }
    });

    doc.save(`events-export-${dayjs().format('YYYY-MM-DD')}.pdf`);
    $toast.success('PDF exported successfully!');
  } catch (error) {
    console.error('Export error:', error);
    $toast.error('An error occurred while exporting to PDF.');
  } finally {
    loading.value = false;
  }
};

const exportToExcel = async (): Promise<void> => {
  loading.value = true;
  try {
    interface ExportRow {
      Title: string;
      Description: string;
      Location: string;
      "Start Date": string;
      "End Date": string;
      Status: string;
      "Published At": string;
      "Date Created": string;
      Attendees: number;
      Likes: number;
      Category: string;
    }

    const dataToExport: ExportRow[] = formattedEvents.value.map((event) => ({
      Title: event.title,
      Description: event.description?.replace(/<[^>]*>/g, "") || "",
      Location: event.location,
      "Start Date": event.start,
      "End Date": event.end,
      Status: event.status || "",
      "Published At": event.published_at,
      "Date Created": event.created_at,
      Attendees: event.attendees_count || 0,
      Likes: event.likes_count || 0,
      Category: event.category?.name || "Uncategorized",
    }));

    if (dataToExport.length === 0) {
      $toast.info("No events to export.");
      loading.value = false;
      return;
    }

    const headers = Object.keys(dataToExport[0]).join(",");
    const csv = dataToExport.map((row) =>
      Object.values(row)
        .map((value) => `"${String(value).replace(/"/g, '""')}"`)
        .join(",")
    );

    const csvContent = [headers, ...csv].join("\n");
    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement("a");

    link.setAttribute("href", url);
    link.setAttribute(
      "download",
      `events-export-${dayjs().format("YYYY-MM-DD")}.csv`
    );
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    $toast.success("Events exported to Excel successfully!");
  } catch (error) {
    console.error("Export error:", error);
    $toast.error("An error occurred while exporting to Excel.");
  } finally {
    loading.value = false;
  }
};

const clearFilters = (): void => {
  searchQuery.value = "";
  statusFilter.value = "";
  dateRangeFilter.value = "";
  currentPage.value = 1;
  serverOptions.value.page = 1;

  selectedStatus.value = statusOptions[0];
  selectedDateRange.value = dateRangeOptions[0];
};

const handleOptionsUpdate = (options: ServerOptions): void => {
  serverOptions.value = options;
  currentPage.value = options.page;
  itemsPerPage.value = options.rowsPerPage;
};

// Headless UI event handlers
const onStatusChange = (status: FilterOption): void => {
  statusFilter.value = status.value;
  currentPage.value = 1;
  serverOptions.value.page = 1;
};

const onDateRangeChange = (dateRange: FilterOption): void => {
  dateRangeFilter.value = dateRange.value;
  currentPage.value = 1;
  serverOptions.value.page = 1;
};

watch([searchQuery, statusFilter, dateRangeFilter], () => {
  currentPage.value = 1;
  serverOptions.value.page = 1;
});

onMounted(() => {
  fetchUserEvents();
});
</script>

<style lang="css" scoped></style>
