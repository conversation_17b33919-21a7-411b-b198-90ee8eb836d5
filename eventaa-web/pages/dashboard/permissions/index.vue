<template>
  <div class="p-6">
    <div class="mb-6">
      <h1 class="text-2xl font-bold dashboard-text-primary">Permission Management</h1>
      <p class="dashboard-text-secondary mt-1">Manage vendor permissions</p>
    </div>

    <div class="dashboard-bg-card rounded-lg shadow-sm p-4 mb-6">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-lg font-semibold dashboard-text-primary">Permissions by Category</h2>
        <button
          v-if="vendorStore.hasPermissions(['permissions.create'])"
          class="px-3 py-1 text-sm rounded-md dashboard-bg-active dashboard-text-primary"
          @click="createNewPermission"
        >
          Add Permission
        </button>
      </div>

      <div v-if="loading" class="py-8 text-center">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 dashboard-border"></div>
      </div>

      <div v-else>
        <div v-for="(permissions, category) in permissionsByCategory" :key="category" class="mb-6">
          <div class="flex items-center mb-2">
            <h3 class="text-md font-medium dashboard-text-primary">{{ formatCategory(category) }}</h3>
            <div class="ml-2 px-2 py-0.5 text-xs rounded-full dashboard-bg-hover dashboard-text-secondary">
              {{ permissions.length }}
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            <div
              v-for="permission in permissions"
              :key="permission.id"
              class="p-3 rounded-md dashboard-bg-hover"
            >
              <div class="flex justify-between">
                <div>
                  <h4 class="font-medium dashboard-text-primary">{{ permission.display_name }}</h4>
                  <p class="text-xs dashboard-text-secondary">{{ permission.name }}</p>
                </div>
                <div class="flex space-x-2">
                  <button
                    v-if="vendorStore.hasPermissions(['permissions.edit'])"
                    class="p-1 rounded-full hover:dashboard-bg-hover"
                    @click="editPermission(permission)"
                  >
                    <Icon icon="heroicons:pencil-square" class="w-4 h-4 dashboard-text-secondary" />
                  </button>
                  <button
                    v-if="vendorStore.hasPermissions(['permissions.delete'])"
                    class="p-1 rounded-full hover:dashboard-bg-hover"
                    @click="deletePermission(permission)"
                  >
                    <Icon icon="heroicons:trash" class="w-4 h-4 text-red-500" />
                  </button>
                </div>
              </div>
              <p v-if="permission.description" class="text-xs dashboard-text-secondary mt-1">
                {{ permission.description }}
              </p>
            </div>
          </div>
        </div>

        <div v-if="Object.keys(permissionsByCategory).length === 0" class="py-8 text-center dashboard-text-secondary">
          No permissions found
        </div>
      </div>
    </div>

    <div class="dashboard-bg-card rounded-lg shadow-sm p-4">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-lg font-semibold dashboard-text-primary">Assign Permissions to Roles</h2>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label class="block text-sm font-medium dashboard-text-secondary mb-2">Select Role</label>
          <select
            v-model="selectedRoleId"
            class="w-full p-2 rounded-md dashboard-bg-hover dashboard-text-primary border dashboard-border"
            @change="fetchRolePermissions"
          >
            <option value="">Select a role</option>
            <option v-for="role in roles" :key="role.id" :value="role.id">{{ role.display_name }}</option>
          </select>
        </div>

        <div v-if="selectedRoleId">
          <label class="block text-sm font-medium dashboard-text-secondary mb-2">Assigned Permissions</label>
          <div v-if="rolePermissionsLoading" class="py-4 text-center">
            <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 dashboard-border"></div>
          </div>
          <div v-else-if="rolePermissions.length === 0" class="py-4 text-center dashboard-text-secondary">
            No permissions assigned to this role
          </div>
          <div v-else class="max-h-60 overflow-y-auto p-2 dashboard-bg-hover rounded-md">
            <div
              v-for="permission in rolePermissions"
              :key="permission.id"
              class="flex items-center justify-between p-2 mb-1 rounded-md dashboard-bg-card"
            >
              <div>
                <p class="text-sm font-medium dashboard-text-primary">{{ permission.display_name }}</p>
                <p class="text-xs dashboard-text-secondary">{{ permission.name }}</p>
              </div>
              <button
                v-if="vendorStore.hasPermissions(['permissions.roles.assign'])"
                class="p-1 rounded-full hover:dashboard-bg-hover"
                @click="removePermissionFromRole(permission)"
              >
                <Icon icon="heroicons:x-mark" class="w-4 h-4 text-red-500" />
              </button>
            </div>
          </div>
        </div>
      </div>

      <div v-if="selectedRoleId && vendorStore.hasPermissions(['permissions.roles.assign'])" class="mt-6">
        <label class="block text-sm font-medium dashboard-text-secondary mb-2">Add Permissions</label>
        <div class="flex space-x-2">
          <select
            v-model="permissionToAdd"
            class="flex-1 p-2 rounded-md dashboard-bg-hover dashboard-text-primary border dashboard-border"
          >
            <option value="">Select a permission</option>
            <option
              v-for="permission in availablePermissions"
              :key="permission.id"
              :value="permission.id"
            >
              {{ permission.display_name }} ({{ permission.name }})
            </option>
          </select>
          <button
            class="px-4 py-2 rounded-md dashboard-bg-active dashboard-text-primary"
            :disabled="!permissionToAdd"
            @click="addPermissionToRole"
          >
            Add
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useVendorStore } from '@/store/vendor';
import type { Permission, Role, PermissionsByCategory } from '@/types/role';
import { ENDPOINTS } from '@/utils/api';

definePageMeta({
  layout: 'vendor-dashboard',
  middleware: ['vendor'],
  meta: {
    requiredPermissions: ['permissions.view']
  }
});

const vendorStore = useVendorStore();
const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

const permissions = ref<Permission[]>([]);
const roles = ref<Role[]>([]);
const loading = ref<boolean>(true);
const selectedRoleId = ref<number | string>('');
const rolePermissions = ref<Permission[]>([]);
const rolePermissionsLoading = ref<boolean>(false);
const permissionToAdd = ref<number | string>('');

const permissionsByCategory = computed<PermissionsByCategory>(() => {
  const result: PermissionsByCategory = {};

  permissions.value.forEach(permission => {
    if (!result[permission.category]) {
      result[permission.category] = [];
    }
    result[permission.category].push(permission);
  });

  return result;
});

const availablePermissions = computed(() => {
  return permissions.value.filter(permission =>
    !rolePermissions.value.some(rp => rp.id === permission.id)
  );
});

onMounted(async () => {
  await Promise.all([
    fetchPermissions(),
    fetchRoles()
  ]);
});

const fetchPermissions = async () => {
  loading.value = true;
  try {
    const response = await httpClient.get<Permission[]>(ENDPOINTS.PERMISSIONS.GET);
    if (response) {
      permissions.value = response;
    }
  } catch (error) {
    console.error('Error fetching permissions:', error);
    $toast.error('Failed to fetch permissions');
  } finally {
    loading.value = false;
  }
};

const fetchRoles = async () => {
  try {
    const response = await httpClient.get<Role[]>(ENDPOINTS.ROLES.GET);
    if (response) {
      roles.value = response;
    }
  } catch (error) {
    console.error('Error fetching roles:', error);
    $toast.error('Failed to fetch roles');
  }
};

const fetchRolePermissions = async () => {
  if (!selectedRoleId.value) {
    rolePermissions.value = [];
    return;
  }

  rolePermissionsLoading.value = true;
  try {
    const response = await httpClient.get<Permission[]>(`${ENDPOINTS.ROLES.SHOW}/${selectedRoleId.value}/permissions`);
    if (response) {
      rolePermissions.value = response;
    }
  } catch (error) {
    console.error('Error fetching role permissions:', error);
    $toast.error('Failed to fetch role permissions');
  } finally {
    rolePermissionsLoading.value = false;
  }
};

const formatCategory = (category: string): string => {
  return category
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

const createNewPermission = () => {
  $toast.info('Create permission functionality will be implemented');
};

const editPermission = (permission: Permission) => {
  $toast.info(`Edit permission: ${permission.name}`);
};

const deletePermission = (permission: Permission) => {
  $toast.info(`Delete permission: ${permission.name}`);
};

const addPermissionToRole = async () => {
  if (!selectedRoleId.value || !permissionToAdd.value) return;

  try {
    await httpClient.post(`${ENDPOINTS.ROLES.ASSIGN_PERMISSIONS}/${selectedRoleId.value}`, {
      permissions: [permissionToAdd.value]
    });

    $toast.success('Permission added to role');
    await fetchRolePermissions();
    permissionToAdd.value = '';
  } catch (error) {
    console.error('Error adding permission to role:', error);
    $toast.error('Failed to add permission to role');
  }
};

const removePermissionFromRole = async (permission: Permission) => {
  try {
    await httpClient.post(`${ENDPOINTS.ROLES.ASSIGN_PERMISSIONS}/${selectedRoleId.value}`, {
      permissions: rolePermissions.value
        .filter(p => p.id !== permission.id)
        .map(p => p.id)
    });

    $toast.success('Permission removed from role');
    await fetchRolePermissions();
  } catch (error) {
    console.error('Error removing permission from role:', error);
    $toast.error('Failed to remove permission from role');
  }
};
</script>
