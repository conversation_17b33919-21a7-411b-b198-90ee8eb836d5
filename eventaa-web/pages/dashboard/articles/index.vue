<template>
  <div class="min-h-screen bg-gray-50">
    <div class="mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="flex justify-between items-center mb-8">
        <h1 class="text-2xl font-semibold text-gray-900">Manage Posts</h1>
        <button @click="$router.push('/dashboard/articles/create')"
          class="bg-red-600 text-white px-2 py-1 flex items-center space-x-2">
          <Icon icon="proicons:add" class="w-5 h-5 mr-2" />
          Create
        </button>
      </div>

      <div class="mb-6 flex items-center justify-end space-x-3">
        <div class="relative rounded-md shadow-sm">
          <input type="text" v-model="searchQuery"
            class="block w-full pr-10 px-2 py-2 border focus:outline-none focus:ring-0"
            placeholder="Search by post title or author" />
          <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>

        <Popover v-slot="{ open }" class="relative">
          <PopoverButton class="border px-4 py-2 bg-white flex font-medium items-center text-gray-500 hover:bg-gray-50"
            :class="{ 'text-sky-500': hasActiveFilters }">
            <Icon icon="proicons:filter-2" class="w-5 h-5 mr-2" />
            Filters
            <span v-if="hasActiveFilters" class="ml-2 bg-sky-100 text-sky-500 px-2 py-0.5 rounded-full text-xs">
              {{ activeFilterCount }}
            </span>
          </PopoverButton>

          <TransitionRoot leave="transition ease-in duration-200" leaveFrom="opacity-100" leaveTo="opacity-0">
            <PopoverPanel
              class="absolute right-0 z-10 w-80 origin-top-right rounded-none bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
              <div class="p-4">
                <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Filter Posts</h3>

                <div class="mb-4">
                  <label class="block text-lg font-medium text-black mb-2">Date Range</label>
                  <datepicker required position="left" placeholder="select start & end date" :range="true"
                    input-class-name="datepicker bg-red-500" format="dd/MM/yyyy HH:mm" v-model="dateRange" />
                </div>

                <Listbox v-model="filters.status">
                  <div class="relative mt-1 mb-4">
                    <ListboxLabel class="block text-lg font-medium text-black mb-2">Status</ListboxLabel>
                    <ListboxButton
                      class="relative w-full cursor-default rounded-none bg-white py-2 pl-3 pr-10 text-left border focus:outline-none focus-visible:border-indigo-500 focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-orange-300 sm:text-sm">
                      <span class="block truncate">{{ filters.status }}</span>
                      <span class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                        <ChevronUpDownIcon class="h-5 w-5 text-gray-400" aria-hidden="true" />
                      </span>
                    </ListboxButton>

                    <transition leave-active-class="transition duration-100 ease-in" leave-from-class="opacity-100"
                      leave-to-class="opacity-0">
                      <ListboxOptions
                        class="absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm">
                        <ListboxOption v-slot="{ active, selected }" v-for="status in statuses" :key="status"
                          :value="status" as="template">
                          <li :class="[
                            active ? 'bg-amber-100 text-amber-900' : 'text-gray-900',
                            'relative cursor-default select-none py-2 pl-10 pr-4',
                          ]">
                            <span :class="[
                              selected ? 'font-medium' : 'font-normal',
                              'block truncate',
                            ]">{{ status }}</span>
                            <span v-if="selected"
                              class="absolute inset-y-0 left-0 flex items-center pl-3 text-amber-600">
                              <CheckIcon class="h-5 w-5" aria-hidden="true" />
                            </span>
                          </li>
                        </ListboxOption>
                      </ListboxOptions>
                    </transition>
                  </div>
                </Listbox>

                <Listbox v-model="filters.categoryId">
                  <ListboxLabel class="block text-lg font-medium text-black mb-2">Category</ListboxLabel>
                  <div class="relative mt-1 mb-4">
                    <ListboxButton
                      class="relative w-full cursor-default rounded-none bg-white py-2 pl-3 pr-10 text-left border focus:outline-none focus-visible:border-indigo-500 focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-orange-300 sm:text-sm">
                      <span class="block truncate">{{ filters.categoryId }}</span>
                      <span class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                        <ChevronUpDownIcon class="h-5 w-5 text-gray-400" aria-hidden="true" />
                      </span>
                    </ListboxButton>

                    <transition leave-active-class="transition duration-100 ease-in" leave-from-class="opacity-100"
                      leave-to-class="opacity-0">
                      <ListboxOptions
                        class="absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm">
                        <ListboxOption v-slot="{ active, selected }" v-for="category in categories" :key="category.name"
                          :value="category.name" as="template">
                          <li :class="[
                            active ? 'bg-amber-100 text-amber-900' : 'text-gray-900',
                            'relative cursor-default select-none py-2 pl-10 pr-4',
                          ]">
                            <span :class="[
                              selected ? 'font-medium' : 'font-normal',
                              'block truncate',
                            ]">{{ category.name }}</span>
                            <span v-if="selected"
                              class="absolute inset-y-0 left-0 flex items-center pl-3 text-amber-600">
                              <CheckIcon class="h-5 w-5" aria-hidden="true" />
                            </span>
                          </li>
                        </ListboxOption>
                      </ListboxOptions>
                    </transition>
                  </div>
                </Listbox>

                <div class="mt-6 flex justify-end space-x-3">
                  <button @click="resetFilters" class="text-gray-500 hover:text-gray-700">
                    Reset Filters
                  </button>
                  <button @click="applyFilters"
                    class="inline-flex justify-center border border-transparent bg-red-600 px-4 py-2 font-medium text-white hover:bg-red-700">
                    Apply Filters
                  </button>
                </div>
              </div>
            </PopoverPanel>
          </TransitionRoot>
        </Popover>
      </div>

      <div class="bg-white border">
        <ul class="divide-y divide-gray-200">
          <template v-if="loading">
            <li v-for="n in 3" :key="`skeleton-${n}`" class="px-6 py-4">
              <div class="flex items-center space-x-4">
                <div class="flex-shrink-0 h-16 w-16">
                  <div class="h-16 w-16 bg-gray-200 animate-pulse"></div>
                </div>
                <div class="flex-1 min-w-0">
                  <div class="flex items-center justify-between">
                    <div class="space-y-3">
                      <div class="h-4 w-32 bg-gray-200 animate-pulse"></div>
                      <div class="h-6 w-64 bg-gray-200 animate-pulse"></div>
                      <div class="h-4 w-48 bg-gray-200 animate-pulse"></div>
                    </div>
                    <div class="flex items-center space-x-4">
                      <div class="h-8 w-20 bg-gray-200 rounded-full animate-pulse"></div>
                    </div>
                  </div>
                </div>
              </div>
            </li>
          </template>

          <li v-else v-for="post in posts.data" :key="post.id" class="px-6 py-4">
            <div class="flex items-center space-x-4">
              <div class="flex-shrink-0 h-16 w-16">
                <img :src="`${runtimeConfig.public.baseUrl}storage/${post.image}`" class="h-16 w-16 object-cover"
                  :alt="post.slug" />
              </div>

              <div class="flex-1 min-w-0">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-gray-500">
                      Created: {{ formatDate(post.created_at) }} | By: {{ post.user.name }}
                    </p>
                    <p class="text-lg font-medium text-gray-900 truncate">
                      {{ post.title }}
                    </p>
                    <p class="text-gray-500">
                      {{ post.slug }}
                    </p>
                  </div>

                  <div class="flex items-center space-x-4">
                    <button @click="$router.push(`/dashboard/articles/edit?id=${post.id}`)" class="p-2 bg-gray-100 rounded-full text-gray-500 hover:text-gray-500">
                      <Icon icon="uil:edit-alt" class="w-5 h-5" />
                    </button>
                    <button @click="deleteArticle(post.id)"
                      class="p-2 bg-gray-100 rounded-full text-gray-500 hover:text-gray-500">
                      <Icon v-if="!isDeleting" icon="iconamoon:trash-light" class="w-5 h-5" />
                      <div v-else class="flex items-center">
                        <CoreLoader height="25" width="25" color="dodgerblue" />
                        <p class="px-2 text-sky-500">Deleting, please wait...</p>
                      </div>
                    </button>
                    <div class="flex items-center space-x-2">
                      <span :class="getStatusClass(post.is_published)">
                        {{ post.is_published ? 'PUBLISHED' : 'DRAFT' }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </li>
        </ul>
      </div>

      <div class="mt-6 flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6">
        <div class="flex flex-1 justify-between sm:hidden">
          <button @click="handlePageChange(currentPage - 1)" :disabled="!posts.prev_page_url" :class="[
            'relative inline-flex items-center px-4 py-2 text-sm font-medium rounded-md',
            !posts.prev_page_url
              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
              : 'bg-white text-gray-700 hover:bg-gray-50'
          ]">
            Previous
          </button>
          <button @click="handlePageChange(currentPage + 1)" :disabled="!posts.next_page_url" :class="[
            'relative inline-flex items-center px-4 py-2 text-sm font-medium rounded-md',
            !posts.next_page_url
              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
              : 'bg-white text-gray-700 hover:bg-gray-50'
          ]">
            Next
          </button>
        </div>
        <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
          <div>
            <p class="text-gray-700">
              Showing
              <span class="font-medium">{{ posts.from }}</span>
              to
              <span class="font-medium">{{ posts.to }}</span>
              of
              <span class="font-medium">{{ posts.total }}</span>
              results
            </p>
          </div>
          <div>
            <nav class="isolate inline-flex -space-x-px rounded-none" aria-label="Pagination">
              <button v-for="link in posts.links" :key="link.label" @click="handlePageChange(getPageNumber(link.url))"
                :disabled="!link.url" :class="[
                  'relative inline-flex items-center px-4 py-2 text-sm font-semibold',
                  link.active
                    ? 'z-10 bg-red-600 text-white focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600'
                    : link.url
                      ? 'text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:outline-offset-0'
                      : 'text-gray-400 cursor-not-allowed',
                ]" v-html="link.label" />
            </nav>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { debounce } from 'lodash';
import dayjs from 'dayjs';
import {
  Popover,
  PopoverButton,
  PopoverPanel,
  TransitionRoot,
  Listbox,
  ListboxLabel,
  ListboxButton,
  ListboxOptions,
  ListboxOption,
} from '@headlessui/vue';
import { CheckIcon, ChevronUpDownIcon } from '@heroicons/vue/20/solid'
import type { ArticleFilters, Category, PaginatedResponse } from '@/types';

definePageMeta({
  layout: "dashboard"
});

const httpClient = useHttpClient();
const posts = ref<PaginatedResponse>({
  current_page: 1,
  data: [],
  first_page_url: '',
  from: 0,
  last_page: 1,
  last_page_url: '',
  links: [],
  next_page_url: null,
  path: '',
  per_page: 10,
  prev_page_url: null,
  to: 0,
  total: 0
});

const searchQuery = ref<string>('');
const runtimeConfig = useRuntimeConfig();
const loading = ref<boolean>(false);
const currentPage = ref<number>(1);
const categories = ref<Category[]>([]);
const { $toast }: any = useNuxtApp();
const statuses = ref<string[]>(['All', 'Published', 'Draft']);
const dateRange = ref([]);
const isDeleting = ref<boolean>(false);

const filters = ref<ArticleFilters>({
  dateFrom: '',
  dateTo: '',
  status: 'All',
  categoryId: 'All',
});

const hasActiveFilters = computed(() => {
  return filters.value.dateFrom !== '' ||
    filters.value.dateTo !== '' ||
    filters.value.status !== 'All' ||
    filters.value.categoryId !== 'All';
});

const activeFilterCount = computed(() => {
  return Object.values(filters.value).filter(value => value !== '').length;
});

const formatDate = (date: string): string => {
  return dayjs(date).format('MMM D, YYYY');
};

const getStatusClass = (isPublished: number): string => {
  return isPublished
    ? 'px-3 py-1 text-sm font-medium text-green-500 bg-green-100 rounded-full'
    : 'px-3 py-1 text-sm font-medium text-orange-500 bg-orange-100 rounded-full';
};

const getPageNumber = (url: string | null): number => {
  if (!url) return currentPage.value;
  const match = url.match(/page=(\d+)/);
  return match ? parseInt(match[1]) : 1;
};

const handlePageChange = async (page: number) => {
  if (page < 1 || page > posts.value.last_page) return;
  currentPage.value = page;
  await fetchArticles(page);
};

const buildQueryParams = (): URLSearchParams => {
  const params = new URLSearchParams();

  if (currentPage.value > 1) {
    params.append('page', currentPage.value.toString());
  }

  if (searchQuery.value) {
    params.append('search', searchQuery.value);
  }

  if (filters.value.dateFrom) {
    params.append('date_from', filters.value.dateFrom);
  }

  if (filters.value.dateTo) {
    params.append('date_to', filters.value.dateTo);
  }

  if (filters.value.status) {
    params.append('status', filters.value.status);
  }

  if (filters.value.categoryId) {
    params.append('category_id', filters.value.categoryId.toString());
  }

  return params;
};

const fetchArticles = async (page: number = 1): Promise<void> => {
  loading.value = true;
  try {
    const params = buildQueryParams();
    const response = await httpClient.get<PaginatedResponse>(`${ENDPOINTS.BLOG.READ}?${params.toString()}`);
    if (response) {
      posts.value = response;
    }
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

const deleteArticle = async (id: number): Promise<void> => {
  isDeleting.value = true;
  try {
    const response = await httpClient.delete(`${ENDPOINTS.BLOG.DELETE}/${id}`);
    if (response) {
      $toast.success('Article deleted successfully');
      await fetchArticles();
    }
  } catch (error) {
    console.error(error);
  } finally {
    isDeleting.value = false;
  }
}

const fetchCategories = async (): Promise<void> => {
  try {
    const response = await httpClient.get<Category[]>(ENDPOINTS.CATEGORIES.BASE);
    if (response) {
      categories.value = response;
    }
  } catch (error) {
    console.error(error);
  }
};

const resetFilters = () => {
  filters.value = {
    dateFrom: '',
    dateTo: '',
    status: 'All',
    categoryId: 'All',
  };
  currentPage.value = 1;
  fetchArticles();
};

const applyFilters = () => {
  currentPage.value = 1;
  fetchArticles();
};

watch((dateRange), (newDateRange, oldDateRange) => {
  if (newDateRange) {
    filters.value.dateFrom = newDateRange[0]
    filters.value.dateTo = newDateRange[1]
  }
})

watch(searchQuery, debounce(() => {
  currentPage.value = 1;
  fetchArticles();
}, 300));

onMounted(async () => {
  await Promise.all([
    fetchArticles(),
    fetchCategories()
  ]);
});
</script>