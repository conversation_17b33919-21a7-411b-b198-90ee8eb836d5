<template>
    <div class="min-h-screen bg-gray-50 py-8">
        <FormKit id="createEventForm" @submit="saveArticle" type="form" submit-label="Update" @submit-invalid="true"
            :actions="false" #default="{}">
            <div class="px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center mb-8">
                    <h1 class="text-2xl font-bold text-gray-900">Create New Article</h1>
                    <div class="flex space-x-4">
                        <button type="button" class="px-4 py-2 border border-gray-300 rounded-none text-gray-700 hover:bg-gray-50"
                            @click="router.push('/dashboard/articles')">
                            Cancel
                        </button>
                        <CoreSubmitButton :text="isSubmitting ? 'Saving...' : 'Save Article'" :disabled="isSubmitting"
                            :loading="isSubmitting" />
                    </div>
                </div>

                <div class="bg-white shadow p-6 space-y-6">
                    <div>
                        <FormKit type="text" name="title" v-model="form.title" label="Title"
                            placeholder="Enter article title" :classes="{
                                input: 'w-full text-blue-500 py-1.5 border border-gray-300 rounded-none focus:ring-0 focus:outline-none',
                            }" />
                    </div>

                    <div>
                        <label class="block font-semibold text-lg text-black mb-2">Cover Art</label>
                        <input type="file" accept="image/*" @change="handleThumbnailChange" class="block w-full text-gray-500
                        file:mr-4 file:py-2 file:px-4
                        file:rounded-full
                        file:text-base file:border file:border-red-200 file:font-semibold
                        file:bg-red-50 file:text-red-600
                        hover:file:bg-red-100
                        cursor-pointer rounded-lg
                        p-2" />

                        <div class="mt-4 flex items-center space-x-4">
                            <div class="w-32 h-32 border-2 border-gray-300 border-dashed rounded-lg flex items-center justify-center overflow-hidden"
                                :class="{ 'border-none': thumbnailPreview }">
                                <img v-if="thumbnailPreview" :src="thumbnailPreview"
                                    class="w-full h-full object-cover rounded-lg" alt="Thumbnail preview" />
                                <span v-else class="text-gray-500">No image</span>
                            </div>
                        </div>
                    </div>

                    <div class="w-full flex flex-col space-y-2">
                        <label class="text-lg font-semibold">Category</label>
                        <div class="flex flex-wrap space-x-2">
                            <button type="button" @click="selectedCategory = category"
                                class="flex items-center bg-gray-100 font-light hover:bg-red-600 hover:text-white transition duration-150 rounded-full px-1.5 py-1.5 mb-1.5"
                                v-for="category in $categories"
                                :class="selectedCategory.name === category.name ? 'bg-red-600 text-white' : ''">
                                <img :src="`${runtimeConfig.public.baseUrl}storage/categories/${category.icon}`"
                                    class="w-6 h-6 mr-2" />
                                {{ category.name }}
                            </button>
                        </div>
                    </div>

                    <div>
                        <FormsTagInput v-model="form.tags" label="Tags" placeholder="Add a tag" />
                    </div>

                    <div class="w-full flex flex-col space-y-2">
                        <label class="text-lg font-semibold">Content</label>
                        <RichTextEditor theme="snow" class="editor" required v-model:content="form.content"
                            contentType="html" :editor="ClassicEditor" v-model="form.content" :config="editorConfig">
                        </RichTextEditor>
                    </div>

                    <Vueform>
                        <CheckboxElement name="checkbox" v-model="isPublished">
                            Publish article right away
                        </CheckboxElement>
                    </Vueform>
                </div>
            </div>
        </FormKit>
    </div>
</template>

<script setup lang="ts">
import { ClassicEditor, Bold, Essentials, Heading, BlockQuote, Table, MediaEmbed, Font, FontColor, FontSize, Italic, Mention, Paragraph, Undo, Link, InputNumberView, List, } from 'ckeditor5';
import type { Category } from '~/types';

definePageMeta({
    layout: "dashboard"
})

interface ArticleForm {
    title: string;
    content: string;
    thumbnail: File | null;
    tags: string[];
}

const editorConfig = {
    plugins: [Bold, Essentials, Heading, Italic, BlockQuote, Table, Font, FontColor, FontSize, MediaEmbed, Mention, Paragraph, Undo, Link, InputNumberView, List],
    toolbar: ['heading',
        '|',
        'bold',
        'italic',
        'link',
        'bulletedList',
        'numberedList',
        'blockQuote',
        'insertTable',
        'mediaEmbed',
        'undo',
        'redo',
        'imageUpload',
        'fontSize',
        'fontColor',
        'highlight'],
}

const router = useRouter();
const isSubmitting = ref(false);
const isPublished = ref(false);
const thumbnailPreview = ref<string>('');
const httpClient = useHttpClient();
const runtimeConfig = useRuntimeConfig();
const selectedCategory = ref<Category>({ id: 0, name: "select Category", icon: "other.png" });


const form = ref<ArticleForm>({
    title: '',
    content: '',
    thumbnail: null,
    tags: [],
});
const { $toast, $categories }: any = useNuxtApp();

const handleThumbnailChange = (event: Event) => {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files[0]) {
        form.value.thumbnail = input.files[0];
        const reader = new FileReader();
        reader.onload = (e) => {
            thumbnailPreview.value = e.target?.result as string;
        };
        reader.readAsDataURL(input.files[0]);
    }
};

const saveArticle = async (): Promise<void> => {
    isSubmitting.value = true;
    try {
        const formData = new FormData();
        formData.append('title', form.value.title);
        formData.append('content', form.value.content);
        formData.append('image', form.value.thumbnail as File);
        formData.append('tags', form.value.tags.join(','));
        formData.append('category_id', String(selectedCategory.value.id));
        formData.append('is_published', isPublished.value ? '1' : '0');
        formData.append('tags', JSON.stringify(form.value.tags));

        const response = await httpClient.post(ENDPOINTS.BLOG.CREATE, formData);
        if (response) {
            $toast.success('Article created successfully');
            setTimeout(() => {
                router.push('/dashboard/articles');
            }, 1000)
        }
    } catch (error: any) {
        if (error) {
            const errors = error.errors;
            Object.keys(errors).forEach((key) => {
                if (Array.isArray(errors[key])) {
                    errors[key].forEach((message: string) => {
                        $toast.error(message);
                    });
                } else if (typeof errors[key] === 'string') {
                    $toast.error(errors[key]);
                }
            });
        } else if (error.message) {
            $toast.error(error.message);
        } else {
            $toast.error('Something went wrong, please try again later');
        }
    } finally {
        isSubmitting.value = false;
    }
};
</script>