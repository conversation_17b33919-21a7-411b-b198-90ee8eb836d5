<template>
  <div class="p-6 bg-gray-100">
    <div class="mb-6 flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold">Calendar</h1>
        <p class="text-gray-500">View and manage all your scheduled events</p>
      </div>
      <div class="flex space-x-2">
        <button class="px-4 py-2 bg-white border border-gray-300 rounded shadow-sm hover:bg-gray-50">
          <Icon icon="heroicons:arrow-down-tray" class="w-5 h-5 text-gray-600" />
          <span class="ml-1">Export</span>
        </button>
        <button class="px-4 py-2 bg-red-600 text-white rounded shadow-sm hover:bg-red-700">
          <Icon icon="heroicons:plus" class="w-5 h-5" />
          <span class="ml-1">Add Event</span>
        </button>
      </div>
    </div>

    <div v-if="loading" class="flex justify-center items-center h-64">
      <CoreLoader />
    </div>
    <div v-else class="bg-white shadow">
      <div class="p-4 border-b flex justify-between items-center">
        <div class="flex items-center space-x-2">
          <button class="p-2 hover:bg-gray-100 rounded" @click="previousMonth">
            <Icon icon="heroicons:chevron-left" class="w-5 h-5" />
          </button>
          <h2 class="text-lg font-semibold">{{ currentMonthName }} {{ currentYear }}</h2>
          <button class="p-2 hover:bg-gray-100 rounded" @click="nextMonth">
            <Icon icon="heroicons:chevron-right" class="w-5 h-5" />
          </button>
        </div>
        <div class="flex space-x-2">
          <button 
            v-for="view in views" 
            :key="view.value"
            @click="currentView = view.value"
            class="px-3 py-1 text-sm rounded"
            :class="currentView === view.value ? 'bg-red-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'"
          >
            {{ view.label }}
          </button>
        </div>
      </div>

      <!-- Month View -->
      <div v-if="currentView === 'month'" class="p-4">
        <div class="grid grid-cols-7 gap-px bg-gray-200">
          <!-- Day headers -->
          <div v-for="day in weekDays" :key="day" class="bg-gray-100 p-2 text-center text-sm font-medium">
            {{ day }}
          </div>
          
          <!-- Calendar days -->
          <div 
            v-for="(day, index) in calendarDays" 
            :key="index"
            class="min-h-[100px] bg-white p-2 border border-gray-100"
            :class="{ 
              'bg-gray-50': !day.isCurrentMonth,
              'bg-blue-50': day.isToday
            }"
          >
            <div class="flex justify-between items-start">
              <span 
                class="text-sm font-medium"
                :class="{ 
                  'text-gray-400': !day.isCurrentMonth,
                  'bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center': day.isToday
                }"
              >
                {{ day.date.getDate() }}
              </span>
              <span v-if="day.events.length > 0" class="text-xs bg-gray-200 rounded-full px-2 py-0.5">
                {{ day.events.length }}
              </span>
            </div>
            
            <!-- Events for this day -->
            <div class="mt-1 space-y-1 max-h-[80px] overflow-y-auto">
              <div 
                v-for="event in day.events" 
                :key="event.id"
                class="text-xs p-1 rounded truncate cursor-pointer"
                :class="`bg-${event.color}-100 text-${event.color}-800`"
                @click="showEventDetails(event)"
              >
                {{ event.title }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Week View -->
      <div v-else-if="currentView === 'week'" class="p-4">
        <div class="grid grid-cols-8 gap-px bg-gray-200">
          <!-- Time column -->
          <div class="bg-white">
            <div class="h-10"></div> <!-- Empty header cell -->
            <div v-for="hour in hours" :key="hour" class="h-20 border-b border-gray-100 text-xs text-gray-500 pr-2 text-right">
              {{ formatHour(hour) }}
            </div>
          </div>
          
          <!-- Days columns -->
          <div v-for="day in weekDaysWithDates" :key="day.date" class="bg-white">
            <div class="h-10 p-2 text-center border-b">
              <div class="text-sm font-medium">{{ day.name }}</div>
              <div 
                class="text-sm"
                :class="{ 'bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center mx-auto': day.isToday }"
              >
                {{ day.date.getDate() }}
              </div>
            </div>
            
            <div v-for="hour in hours" :key="hour" class="h-20 border-b border-gray-100 relative">
              <!-- Events would be positioned here -->
            </div>
          </div>
        </div>
      </div>

      <!-- Day View -->
      <div v-else-if="currentView === 'day'" class="p-4">
        <div class="grid grid-cols-1 gap-px">
          <div v-for="hour in hours" :key="hour" class="flex border-b border-gray-100">
            <div class="w-20 py-4 text-sm text-gray-500 text-right pr-4">
              {{ formatHour(hour) }}
            </div>
            <div class="flex-1 min-h-[60px] relative">
              <!-- Events would be positioned here -->
            </div>
          </div>
        </div>
      </div>

      <!-- Agenda View -->
      <div v-else-if="currentView === 'agenda'" class="p-4">
        <div class="space-y-4">
          <div v-for="(group, date) in groupedEvents" :key="date" class="border-b pb-4">
            <h3 class="font-medium mb-2">{{ formatDate(date) }}</h3>
            <div class="space-y-2">
              <div 
                v-for="event in group" 
                :key="event.id"
                class="flex p-3 border rounded hover:bg-gray-50 cursor-pointer"
                @click="showEventDetails(event)"
              >
                <div class="w-2 self-stretch rounded-l" :class="`bg-${event.color}-500`"></div>
                <div class="ml-3 flex-1">
                  <div class="font-medium">{{ event.title }}</div>
                  <div class="text-sm text-gray-500">{{ formatTimeRange(event.start, event.end) }}</div>
                </div>
                <div class="text-gray-400">
                  <Icon icon="heroicons:chevron-right" class="w-5 h-5" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Event Details Modal -->
    <TransitionRoot appear :show="isEventModalOpen" as="template">
      <Dialog as="div" @close="closeEventModal" class="relative z-10">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-25" />
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div class="flex min-h-full items-center justify-center p-4 text-center">
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel class="w-full max-w-md transform overflow-hidden bg-white p-6 text-left align-middle shadow-xl transition-all">
                <DialogTitle as="h3" class="text-lg font-medium leading-6 text-gray-900">
                  {{ selectedEvent?.title }}
                </DialogTitle>
                <div class="mt-2">
                  <p class="text-sm text-gray-500">
                    {{ formatDate(selectedEvent?.start) }} • {{ formatTimeRange(selectedEvent?.start, selectedEvent?.end) }}
                  </p>
                  <p class="mt-4 text-sm text-gray-700">
                    {{ selectedEvent?.description || 'No description provided.' }}
                  </p>
                </div>

                <div class="mt-4 flex justify-end space-x-2">
                  <button
                    type="button"
                    class="inline-flex justify-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-900 hover:bg-blue-200 focus:outline-none"
                    @click="editEvent"
                  >
                    Edit
                  </button>
                  <button
                    type="button"
                    class="inline-flex justify-center rounded-md border border-transparent bg-red-100 px-4 py-2 text-sm font-medium text-red-900 hover:bg-red-200 focus:outline-none"
                    @click="closeEventModal"
                  >
                    Close
                  </button>
                </div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue';
import { ENDPOINTS } from '@/utils/api';
import dayjs from 'dayjs';

definePageMeta({
  layout: "dashboard",
});

const loading = ref(true);
const currentDate = ref(new Date());
const currentView = ref('month');
const isEventModalOpen = ref(false);
const selectedEvent = ref(null);

const views = [
  { label: 'Month', value: 'month' },
  { label: 'Week', value: 'week' },
  { label: 'Day', value: 'day' },
  { label: 'Agenda', value: 'agenda' },
];

const weekDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
const hours = Array.from({ length: 24 }, (_, i) => i);

// Mock events data - would be replaced with API calls
const events = ref([
  {
    id: 1,
    title: 'Team Meeting',
    description: 'Weekly team sync to discuss project progress',
    start: new Date(2024, 5, 15, 10, 0),
    end: new Date(2024, 5, 15, 11, 30),
    color: 'blue'
  },
  {
    id: 2,
    title: 'Product Launch',
    description: 'New product launch event',
    start: new Date(2024, 5, 20, 14, 0),
    end: new Date(2024, 5, 20, 16, 0),
    color: 'green'
  },
  {
    id: 3,
    title: 'Client Meeting',
    description: 'Meeting with client to discuss requirements',
    start: new Date(2024, 5, 18, 9, 0),
    end: new Date(2024, 5, 18, 10, 0),
    color: 'purple'
  }
]);

const currentMonthName = computed(() => {
  return currentDate.value.toLocaleString('default', { month: 'long' });
});

const currentYear = computed(() => {
  return currentDate.value.getFullYear();
});

const calendarDays = computed(() => {
  const year = currentDate.value.getFullYear();
  const month = currentDate.value.getMonth();
  
  const firstDayOfMonth = new Date(year, month, 1);
  const lastDayOfMonth = new Date(year, month + 1, 0);
  
  const daysInMonth = lastDayOfMonth.getDate();
  const firstDayOfWeek = firstDayOfMonth.getDay();
  
  const days = [];
  
  // Previous month days
  const prevMonthLastDay = new Date(year, month, 0).getDate();
  for (let i = firstDayOfWeek - 1; i >= 0; i--) {
    const date = new Date(year, month - 1, prevMonthLastDay - i);
    days.push({
      date,
      isCurrentMonth: false,
      isToday: isSameDay(date, new Date()),
      events: getEventsForDay(date)
    });
  }
  
  // Current month days
  for (let i = 1; i <= daysInMonth; i++) {
    const date = new Date(year, month, i);
    days.push({
      date,
      isCurrentMonth: true,
      isToday: isSameDay(date, new Date()),
      events: getEventsForDay(date)
    });
  }
  
  // Next month days
  const remainingDays = 42 - days.length; // 6 rows of 7 days
  for (let i = 1; i <= remainingDays; i++) {
    const date = new Date(year, month + 1, i);
    days.push({
      date,
      isCurrentMonth: false,
      isToday: isSameDay(date, new Date()),
      events: getEventsForDay(date)
    });
  }
  
  return days;
});

const weekDaysWithDates = computed(() => {
  const date = new Date(currentDate.value);
  const day = date.getDay();
  date.setDate(date.getDate() - day); // Start of week (Sunday)
  
  return Array.from({ length: 7 }, (_, i) => {
    const d = new Date(date);
    d.setDate(d.getDate() + i);
    return {
      name: weekDays[i],
      date: d,
      isToday: isSameDay(d, new Date())
    };
  });
});

const groupedEvents = computed(() => {
  const grouped = {};
  
  events.value.forEach(event => {
    const dateKey = event.start.toISOString().split('T')[0];
    if (!grouped[dateKey]) {
      grouped[dateKey] = [];
    }
    grouped[dateKey].push(event);
  });
  
  // Sort by date
  return Object.keys(grouped)
    .sort()
    .reduce((obj, key) => {
      obj[key] = grouped[key];
      return obj;
    }, {});
});

function getEventsForDay(date) {
  return events.value.filter(event => isSameDay(event.start, date));
}

function isSameDay(date1, date2) {
  return date1.getFullYear() === date2.getFullYear() &&
         date1.getMonth() === date2.getMonth() &&
         date1.getDate() === date2.getDate();
}

function formatHour(hour) {
  return hour === 0 || hour === 12 ? '12' : hour % 12;
}

function formatDate(date) {
  if (!date) return '';
  return new Date(date).toLocaleDateString('en-US', { 
    weekday: 'short', 
    month: 'short', 
    day: 'numeric' 
  });
}

function formatTimeRange(start, end) {
  if (!start || !end) return '';
  const startTime = new Date(start).toLocaleTimeString('en-US', { 
    hour: 'numeric', 
    minute: '2-digit',
    hour12: true 
  });
  const endTime = new Date(end).toLocaleTimeString('en-US', { 
    hour: 'numeric', 
    minute: '2-digit',
    hour12: true 
  });
  return `${startTime} - ${endTime}`;
}

function previousMonth() {
  const date = new Date(currentDate.value);
  date.setMonth(date.getMonth() - 1);
  currentDate.value = date;
}

function nextMonth() {
  const date = new Date(currentDate.value);
  date.setMonth(date.getMonth() + 1);
  currentDate.value = date;
}

function showEventDetails(event) {
  selectedEvent.value = event;
  isEventModalOpen.value = true;
}

function closeEventModal() {
  isEventModalOpen.value = false;
}

function editEvent() {
  // Implement edit functionality
  closeEventModal();
}

// Simulate API call
onMounted(() => {
  setTimeout(() => {
    loading.value = false;
  }, 1000);
});
</script>
