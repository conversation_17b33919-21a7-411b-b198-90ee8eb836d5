<template>
  <div>
    <div class="mb-6">
      <h1 class="text-2xl font-semibold">Settings</h1>
      <p class="text-gray-500 dark:text-gray-400">Manage your account and application settings</p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <!-- Left sidebar with settings categories -->
      <div class="col-span-1">
        <div class="bg-white dark:bg-gray-800 shadow p-4">
          <div class="space-y-1">
            <button 
              v-for="(tab, index) in tabs" 
              :key="index"
              @click="activeTab = tab.id"
              :class="[
                'w-full text-left px-4 py-2 rounded-md transition-colors',
                activeTab === tab.id 
                  ? 'bg-primary-50 text-primary-600 dark:bg-gray-700 dark:text-white' 
                  : 'hover:bg-gray-100 dark:hover:bg-gray-700'
              ]"
            >
              <div class="flex items-center">
                <Icon :icon="tab.icon" class="h-5 w-5 mr-2" />
                <span>{{ tab.name }}</span>
              </div>
            </button>
          </div>
        </div>
      </div>

      <!-- Right content area -->
      <div class="col-span-1 md:col-span-2">
        <div class="bg-white dark:bg-gray-800 shadow p-6">
          <!-- Profile Settings -->
          <div v-if="activeTab === 'profile'" class="space-y-6">
            <h2 class="text-xl font-semibold">Profile Settings</h2>
            
            <div v-if="loading">
              <div class="animate-pulse space-y-4">
                <div class="h-12 bg-gray-200 dark:bg-gray-700 rounded"></div>
                <div class="h-12 bg-gray-200 dark:bg-gray-700 rounded"></div>
                <div class="h-12 bg-gray-200 dark:bg-gray-700 rounded"></div>
              </div>
            </div>
            
            <div v-else class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Profile Picture</label>
                <div class="flex items-center space-x-4">
                  <img src="https://randomuser.me/api/portraits/men/1.jpg" alt="Profile" class="h-16 w-16 rounded-full object-cover" />
                  <button class="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-sm">Change</button>
                </div>
              </div>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">First Name</label>
                  <input type="text" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700" value="John" />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Last Name</label>
                  <input type="text" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700" value="Doe" />
                </div>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Email</label>
                <input type="email" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700" value="<EMAIL>" />
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Phone</label>
                <input type="tel" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700" value="+****************" />
              </div>
              
              <div class="pt-4">
                <button class="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700">Save Changes</button>
              </div>
            </div>
          </div>

          <!-- Account Settings -->
          <div v-if="activeTab === 'account'" class="space-y-6">
            <h2 class="text-xl font-semibold">Account Settings</h2>
            
            <div v-if="loading">
              <div class="animate-pulse space-y-4">
                <div class="h-12 bg-gray-200 dark:bg-gray-700 rounded"></div>
                <div class="h-12 bg-gray-200 dark:bg-gray-700 rounded"></div>
              </div>
            </div>
            
            <div v-else class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Username</label>
                <input type="text" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700" value="johndoe" />
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Change Password</label>
                <div class="space-y-2">
                  <input type="password" placeholder="Current Password" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700" />
                  <input type="password" placeholder="New Password" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700" />
                  <input type="password" placeholder="Confirm New Password" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700" />
                </div>
              </div>
              
              <div class="pt-4">
                <button class="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700">Update Password</button>
              </div>
            </div>
          </div>

          <!-- Notification Settings -->
          <div v-if="activeTab === 'notifications'" class="space-y-6">
            <h2 class="text-xl font-semibold">Notification Settings</h2>
            
            <div v-if="loading">
              <div class="animate-pulse space-y-4">
                <div class="h-12 bg-gray-200 dark:bg-gray-700 rounded"></div>
                <div class="h-12 bg-gray-200 dark:bg-gray-700 rounded"></div>
                <div class="h-12 bg-gray-200 dark:bg-gray-700 rounded"></div>
              </div>
            </div>
            
            <div v-else class="space-y-4">
              <div class="space-y-3">
                <div class="flex items-center justify-between">
                  <div>
                    <h3 class="font-medium">Email Notifications</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">Receive email notifications for important updates</p>
                  </div>
                  <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" checked class="sr-only peer">
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
                  </label>
                </div>
                
                <div class="flex items-center justify-between">
                  <div>
                    <h3 class="font-medium">SMS Notifications</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">Receive text messages for urgent updates</p>
                  </div>
                  <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" class="sr-only peer">
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
                  </label>
                </div>
                
                <div class="flex items-center justify-between">
                  <div>
                    <h3 class="font-medium">Browser Notifications</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">Receive notifications in your browser</p>
                  </div>
                  <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" checked class="sr-only peer">
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
                  </label>
                </div>
              </div>
              
              <div class="pt-4">
                <button class="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700">Save Preferences</button>
              </div>
            </div>
          </div>

          <!-- Security Settings -->
          <div v-if="activeTab === 'security'" class="space-y-6">
            <h2 class="text-xl font-semibold">Security Settings</h2>
            
            <div v-if="loading">
              <div class="animate-pulse space-y-4">
                <div class="h-12 bg-gray-200 dark:bg-gray-700 rounded"></div>
                <div class="h-12 bg-gray-200 dark:bg-gray-700 rounded"></div>
              </div>
            </div>
            
            <div v-else class="space-y-4">
              <div class="space-y-3">
                <div class="flex items-center justify-between">
                  <div>
                    <h3 class="font-medium">Two-Factor Authentication</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">Add an extra layer of security to your account</p>
                  </div>
                  <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" class="sr-only peer">
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
                  </label>
                </div>
                
                <div class="flex items-center justify-between">
                  <div>
                    <h3 class="font-medium">Login Notifications</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">Get notified when someone logs into your account</p>
                  </div>
                  <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" checked class="sr-only peer">
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
                  </label>
                </div>
              </div>
              
              <div>
                <h3 class="font-medium mb-2">Recent Login Activity</h3>
                <div class="border border-gray-200 dark:border-gray-700 rounded-md overflow-hidden">
                  <div class="divide-y divide-gray-200 dark:divide-gray-700">
                    <div class="p-3">
                      <div class="flex justify-between">
                        <div>
                          <p class="font-medium">Chrome on Windows</p>
                          <p class="text-sm text-gray-500 dark:text-gray-400">IP: ***********</p>
                        </div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Today, 10:30 AM</p>
                      </div>
                    </div>
                    <div class="p-3">
                      <div class="flex justify-between">
                        <div>
                          <p class="font-medium">Safari on iPhone</p>
                          <p class="text-sm text-gray-500 dark:text-gray-400">IP: ***********</p>
                        </div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Yesterday, 8:15 PM</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';

definePageMeta({
  layout: "dashboard",
});

const loading = ref(true);
const activeTab = ref('profile');

const tabs = [
  { id: 'profile', name: 'Profile', icon: 'heroicons:user' },
  { id: 'account', name: 'Account', icon: 'heroicons:user-circle' },
  { id: 'notifications', name: 'Notifications', icon: 'heroicons:bell' },
  { id: 'security', name: 'Security', icon: 'heroicons:lock-closed' },
];

onMounted(() => {
  // Simulate API call
  setTimeout(() => {
    loading.value = false;
  }, 1000);
});
</script>
