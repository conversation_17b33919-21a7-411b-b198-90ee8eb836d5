<template>
  <div class="p-6 bg-zinc-100 dark:bg-zinc-900 min-h-screen">
    <div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Venue Bookings</h1>
        <p class="text-gray-500 dark:text-zinc-400">Manage all venue bookings and reservations</p>
      </div>
      <div class="mt-4 md:mt-0 flex space-x-3">
        <button @click="exportPDF" class="px-4 py-2 bg-white dark:bg-zinc-800 border border-zinc-300 dark:border-zinc-700 shadow-sm hover:bg-zinc-50 dark:hover:bg-zinc-700 flex items-center text-sm text-gray-700 dark:text-zinc-300">
          <Icon icon="heroicons:document-arrow-down" class="h-5 w-5 mr-2" />
          Export PDF
        </button>
        <button @click="exportExcel" class="px-4 py-2 bg-white dark:bg-zinc-800 border border-zinc-300 dark:border-zinc-700 shadow-sm hover:bg-zinc-50 dark:hover:bg-zinc-700 flex items-center text-sm text-gray-700 dark:text-zinc-300">
          <Icon icon="heroicons:table-cells" class="h-5 w-5 mr-2" />
          Export Excel
        </button>
      </div>
    </div>

    <!-- Search and Filters -->
    <div class="bg-white dark:bg-zinc-800 p-4 shadow mb-6">
      <div class="mb-4">
        <div class="relative">
          <Icon icon="heroicons:magnifying-glass" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-zinc-400 w-5 h-5" />
          <input
            v-model="searchQuery"
            @input="debouncedSearch"
            type="text"
            placeholder="Search bookings by ID, customer name, or venue..."
            class="w-full pl-10 pr-4 py-2 border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-gray-900 dark:text-white focus:ring-red-500 focus:border-red-500"
          />
        </div>
      </div>
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-zinc-300 mb-1">Venue</label>
          <Listbox v-model="filters.venue">
            <div class="relative mt-1">
              <ListboxButton class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-gray-900 dark:text-zinc-100 text-left focus:ring-red-500 focus:border-red-500">
                <span class="block truncate">{{ filters.venue ? filters.venue.name : 'All Venues' }}</span>
                <span class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                  <Icon icon="heroicons:chevron-up-down" class="h-5 w-5 text-zinc-400" />
                </span>
              </ListboxButton>
              <transition leave-active-class="transition ease-in duration-100" leave-from-class="opacity-100" leave-to-class="opacity-0">
                <ListboxOptions class="absolute z-10 mt-1 max-h-60 w-full overflow-auto bg-white dark:bg-zinc-700 py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
                  <ListboxOption v-slot="{ active, selected }" :value="null" as="template">
                    <li :class="[active ? 'bg-red-600 text-white' : 'text-gray-900 dark:text-zinc-100', 'relative cursor-default select-none py-2 pl-3 pr-9']">
                      <span :class="[selected ? 'font-semibold' : 'font-normal', 'block truncate']">All Venues</span>
                    </li>
                  </ListboxOption>
                  <ListboxOption v-for="venue in venues" :key="venue.id" :value="venue" v-slot="{ active, selected }" as="template">
                    <li :class="[active ? 'bg-red-600 text-white' : 'text-gray-900 dark:text-zinc-100', 'relative cursor-default select-none py-2 pl-3 pr-9']">
                      <span :class="[selected ? 'font-semibold' : 'font-normal', 'block truncate']">{{ venue.name }}</span>
                    </li>
                  </ListboxOption>
                </ListboxOptions>
              </transition>
            </div>
          </Listbox>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-zinc-300 mb-1">Status</label>
          <Listbox v-model="filters.status">
            <div class="relative mt-1">
              <ListboxButton class="w-full px-3 py-2 border border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-gray-900 dark:text-zinc-100 text-left focus:ring-red-500 focus:border-red-500">
                <span class="block truncate">{{ filters.status || 'All Statuses' }}</span>
                <span class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                  <Icon icon="heroicons:chevron-up-down" class="h-5 w-5 text-zinc-400" />
                </span>
              </ListboxButton>
              <transition leave-active-class="transition ease-in duration-100" leave-from-class="opacity-100" leave-to-class="opacity-0">
                <ListboxOptions class="absolute z-10 mt-1 max-h-60 w-full overflow-auto bg-white dark:bg-zinc-700 py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
                  <ListboxOption v-slot="{ active, selected }" :value="null" as="template">
                    <li :class="[active ? 'bg-red-600 text-white' : 'text-gray-900 dark:text-zinc-100', 'relative cursor-default select-none py-2 pl-3 pr-9']">
                      <span :class="[selected ? 'font-semibold' : 'font-normal', 'block truncate']">All Statuses</span>
                    </li>
                  </ListboxOption>
                  <ListboxOption v-for="status in ['pending', 'confirmed', 'rejected']" :key="status" :value="status" v-slot="{ active, selected }" as="template">
                    <li :class="[active ? 'bg-red-600 text-white' : 'text-gray-900 dark:text-zinc-100', 'relative cursor-default select-none py-2 pl-3 pr-9']">
                      <span :class="[selected ? 'font-semibold' : 'font-normal', 'block truncate capitalize']">{{ status }}</span>
                    </li>
                  </ListboxOption>
                </ListboxOptions>
              </transition>
            </div>
          </Listbox>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-zinc-300 mb-1">Date Range</label>
          <VueDatePicker
            v-model="filters.dateRange"
            range
            :enable-time-picker="false"
            :format="DATE_FORMAT.FULL"
            placeholder="Select date range"
            class="w-full"
            :text-input="true"
            auto-apply
          />
        </div>
        <div class="flex items-end">
          <button @click="applyFilters" class="w-full px-4 py-2 bg-red-600 text-white shadow-sm hover:bg-red-700 flex items-center justify-center">
            <Icon icon="heroicons:funnel" class="w-4 h-4 mr-2" />
            Apply Filters
          </button>
        </div>
      </div>
    </div>

    <!-- Bookings Table -->
    <div class="bg-white dark:bg-zinc-800 shadow overflow-hidden">
      <div v-if="loading" class="flex justify-center items-center py-20">
        <CoreLoader />
      </div>

      <div v-else class="overflow-x-auto">
        <table class="min-w-full divide-y divide-zinc-200 dark:divide-zinc-700">
          <thead class="bg-zinc-50 dark:bg-zinc-700">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-zinc-300 uppercase tracking-wider">
                Booking ID
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-zinc-300 uppercase tracking-wider">
                Venue
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-zinc-300 uppercase tracking-wider">
                Customer
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-zinc-300 uppercase tracking-wider">
                Date & Time
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-zinc-300 uppercase tracking-wider">
                Status
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-zinc-300 uppercase tracking-wider">
                Amount
              </th>
              <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-zinc-300 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-zinc-800 divide-y divide-zinc-200 dark:divide-zinc-700">
            <tr v-for="booking in bookings" :key="booking.id" class="hover:bg-zinc-50 dark:hover:bg-zinc-700">
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                {{ booking.id }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-zinc-400">
                {{ booking.venue?.name }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="h-8 w-8 rounded-full overflow-hidden bg-zinc-100 dark:bg-zinc-700 flex-shrink-0">
                    <img :src="booking.user?.avatar || '/images/default-avatar.png'" alt="" class="h-full w-full object-cover" />
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900 dark:text-white">
                      {{ booking.user?.name }}
                    </div>
                    <div class="text-sm text-gray-500 dark:text-zinc-400">
                      {{ booking.user?.email }}
                    </div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900 dark:text-white">{{ formatDate(booking.booking_from) }}</div>
                <div class="text-sm text-gray-500 dark:text-zinc-400">{{ formatDate(booking.booking_to) }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="[
                  'px-2 inline-flex text-xs leading-5 font-semibold rounded-full',
                  booking.status === 'confirmed' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                  booking.status === 'pending' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                  'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                ]">
                  {{ booking.status }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-zinc-400">
                {{ formatCurrency(booking.venue_price?.amount || 0, booking.venue_price?.currency?.code) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div class="flex justify-end space-x-2">
                  <button @click="viewBooking(booking)" class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300" title="View">
                    <Icon icon="heroicons:eye" class="h-5 w-5" />
                  </button>
                  <button v-if="booking.status === 'pending'" @click="editBooking(booking)" class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300" title="Edit">
                    <Icon icon="heroicons:pencil-square" class="h-5 w-5" />
                  </button>
                  <button v-if="booking.status === 'pending'" @click="confirmDelete(booking)" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300" title="Delete">
                    <Icon icon="heroicons:trash" class="h-5 w-5" />
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div class="bg-white dark:bg-zinc-800 px-4 py-3 flex items-center justify-between border-t border-zinc-200 dark:border-zinc-700 sm:px-6">
        <div class="flex-1 flex justify-between sm:hidden">
          <button @click="prevPage" :disabled="currentPage === 1" class="relative inline-flex items-center px-4 py-2 border border-zinc-300 dark:border-zinc-600 text-sm font-medium text-gray-700 dark:text-zinc-300 bg-white dark:bg-zinc-700 hover:bg-zinc-50 dark:hover:bg-zinc-600 disabled:opacity-50">
            Previous
          </button>
          <button @click="nextPage" :disabled="currentPage === totalPages" class="ml-3 relative inline-flex items-center px-4 py-2 border border-zinc-300 dark:border-zinc-600 text-sm font-medium text-gray-700 dark:text-zinc-300 bg-white dark:bg-zinc-700 hover:bg-zinc-50 dark:hover:bg-zinc-600 disabled:opacity-50">
            Next
          </button>
        </div>
        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
          <div>
            <p class="text-sm text-gray-700 dark:text-zinc-300">
              Showing <span class="font-medium">{{ paginationStart }}</span> to <span class="font-medium">{{ paginationEnd }}</span> of <span class="font-medium">{{ totalItems }}</span> results
            </p>
          </div>
          <div>
            <nav class="relative z-0 inline-flex shadow-sm -space-x-px" aria-label="Pagination">
              <button @click="prevPage" :disabled="currentPage === 1" class="relative inline-flex items-center px-2 py-2 border border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-sm font-medium text-gray-500 dark:text-zinc-400 hover:bg-zinc-50 dark:hover:bg-zinc-600 disabled:opacity-50">
                <span class="sr-only">Previous</span>
                <Icon icon="heroicons:chevron-left" class="h-5 w-5" />
              </button>
              <button v-for="page in displayedPages" :key="page" @click="goToPage(page)" :class="[
                'relative inline-flex items-center px-4 py-2 border text-sm font-medium',
                page === currentPage
                  ? 'z-10 bg-red-50 dark:bg-red-900 border-red-500 text-red-600 dark:text-red-200'
                  : 'bg-white dark:bg-zinc-700 border-zinc-300 dark:border-zinc-600 text-gray-500 dark:text-zinc-400 hover:bg-zinc-50 dark:hover:bg-zinc-600'
              ]">
                {{ page }}
              </button>
              <button @click="nextPage" :disabled="currentPage === totalPages" class="relative inline-flex items-center px-2 py-2 border border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-sm font-medium text-gray-500 dark:text-zinc-400 hover:bg-zinc-50 dark:hover:bg-zinc-600 disabled:opacity-50">
                <span class="sr-only">Next</span>
                <Icon icon="heroicons:chevron-right" class="h-5 w-5" />
              </button>
            </nav>
          </div>
        </div>
      </div>
    </div>

    <!-- View/Edit Booking Modal -->
    <TransitionRoot appear :show="isModalOpen" as="template">
      <Dialog as="div" @close="closeModal" class="relative z-10">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-25" />
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div class="flex min-h-full items-center justify-center p-4 text-center">
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel class="w-full max-w-md transform overflow-hidden bg-white dark:bg-zinc-800 p-6 text-left align-middle shadow-xl transition-all">
                <DialogTitle as="h3" class="text-lg font-medium leading-6 text-gray-900 dark:text-white">
                  {{ modalMode === 'view' ? 'Booking Details' : 'Edit Booking' }}
                </DialogTitle>
                <div class="mt-4">
                  <!-- Booking details or edit form -->
                </div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>

    <!-- Delete Confirmation Modal -->
    <TransitionRoot appear :show="isDeleteModalOpen" as="template">
      <Dialog as="div" @close="closeDeleteModal" class="relative z-10">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-25" />
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div class="flex min-h-full items-center justify-center p-4 text-center">
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel class="w-full max-w-md transform overflow-hidden bg-white dark:bg-zinc-800 p-6 text-left align-middle shadow-xl transition-all">
                <DialogTitle as="h3" class="text-lg font-medium leading-6 text-gray-900 dark:text-white">
                  Delete Booking
                </DialogTitle>
                <div class="mt-2">
                  <p class="text-sm text-gray-500 dark:text-zinc-400">
                    Are you sure you want to delete this booking? This action cannot be undone.
                  </p>
                </div>

                <div class="mt-4 flex justify-end space-x-3">
                  <button
                    type="button"
                    class="inline-flex justify-center border border-transparent bg-red-600 px-4 py-2 text-sm font-medium text-white hover:bg-red-700 focus:outline-none focus-visible:ring-2 focus-visible:ring-red-500 focus-visible:ring-offset-2"
                    @click="deleteBooking"
                  >
                    Delete
                  </button>
                  <button
                    type="button"
                    class="inline-flex justify-center border border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 px-4 py-2 text-sm font-medium text-gray-700 dark:text-zinc-300 hover:bg-zinc-50 dark:hover:bg-zinc-600 focus:outline-none focus-visible:ring-2 focus-visible:ring-zinc-500 focus-visible:ring-offset-2"
                    @click="closeDeleteModal"
                  >
                    Cancel
                  </button>
                </div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { Dialog, DialogPanel, DialogTitle, TransitionRoot, TransitionChild, Listbox, ListboxButton, ListboxOptions, ListboxOption } from '@headlessui/vue';
import type { Venue, VenueBooking } from '@/types';

definePageMeta({
  layout: "dashboard",
});

const { $toast }: any = useNuxtApp();
const httpClient = useHttpClient();
const loading = ref<boolean>(true);
const bookings = ref<VenueBooking[]>([]);
const venues = ref<Venue[]>([]);
const currentPage = ref<number>(1);
const perPage = ref<number>(10);
const totalItems = ref<number>(0);
const searchQuery = ref<string>('');
const isModalOpen = ref<boolean>(false);
const isDeleteModalOpen = ref<boolean>(false);
const modalMode = ref<'view' | 'edit'>('view');
const selectedBooking = ref<VenueBooking | null>(null);
const filters = ref<{
  venue: Venue | null;
  status: "pending" | "confirmed" | "rejected" | null;
  dateRange: [Date, Date] | null;
}>({
  venue: null,
  status: null,
  dateRange: null
});

const totalPages = computed(() => Math.ceil(totalItems.value / perPage.value));
const paginationStart = computed(() => ((currentPage.value - 1) * perPage.value) + 1);
const paginationEnd = computed(() => Math.min(currentPage.value * perPage.value, totalItems.value));
const displayedPages = computed(() => {
  const pages = [];
  const maxPages = 5;
  let start = Math.max(1, currentPage.value - Math.floor(maxPages / 2));
  let end = Math.min(totalPages.value, start + maxPages - 1);

  if (end - start + 1 < maxPages) {
    start = Math.max(1, end - maxPages + 1);
  }

  for (let i = start; i <= end; i++) {
    pages.push(i);
  }
  return pages;
});

// Fetch venues for the filter dropdown
async function fetchVenues() {
  try {
    const response = await httpClient.get<Venue[]>('/venues/get');
    venues.value = response;
  } catch (error) {
    console.error('Error fetching venues:', error);
    $toast.error('Failed to load venues');
  }
}

// Fetch bookings with filters and pagination
async function fetchBookings() {
  loading.value = true;
  try {
    const params: Record<string, any> = {
      page: currentPage.value,
      per_page: perPage.value,
    };

    if (searchQuery.value.trim()) {
      params.search = searchQuery.value.trim();
    }
    if (filters.value.venue) {
      params.venue_id = filters.value.venue.id;
    }
    if (filters.value.status) {
      params.status = filters.value.status;
    }
    if (filters.value.dateRange) {
      params.from_date = filters.value.dateRange[0].toISOString().split('T')[0];
      params.to_date = filters.value.dateRange[1].toISOString().split('T')[0];
    }

    const response = await httpClient.get<{ bookings: VenueBooking[]; total: number }>('/venue-bookings', { params });
    bookings.value = response.bookings;
    totalItems.value = response.total;
  } catch (error) {
    console.error('Error fetching bookings:', error);
    $toast.error('Failed to load bookings');
  } finally {
    loading.value = false;
  }
}

// Debounced search function
let searchTimeout: NodeJS.Timeout;
function debouncedSearch() {
  clearTimeout(searchTimeout);
  searchTimeout = setTimeout(() => {
    currentPage.value = 1;
    fetchBookings();
  }, 500);
}

// Pagination methods
function prevPage() {
  if (currentPage.value > 1) {
    currentPage.value--;
    fetchBookings();
  }
}

function nextPage() {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
    fetchBookings();
  }
}

function goToPage(page: number) {
  currentPage.value = page;
  fetchBookings();
}

// Filter methods
function applyFilters() {
  currentPage.value = 1;
  fetchBookings();
}

// Modal methods
function viewBooking(booking: VenueBooking) {
  selectedBooking.value = booking;
  modalMode.value = 'view';
  isModalOpen.value = true;
}

function editBooking(booking: VenueBooking) {
  selectedBooking.value = booking;
  modalMode.value = 'edit';
  isModalOpen.value = true;
}

function closeModal() {
  isModalOpen.value = false;
  selectedBooking.value = null;
}

// Delete methods
function confirmDelete(booking: VenueBooking) {
  selectedBooking.value = booking;
  isDeleteModalOpen.value = true;
}

async function deleteBooking() {
  if (!selectedBooking.value) return;

  try {
    await httpClient.delete(`/venue-bookings/${selectedBooking.value.id}`);
    $toast.success('Booking deleted successfully');
    fetchBookings();
  } catch (error) {
    console.error('Error deleting booking:', error);
    $toast.error('Failed to delete booking');
  } finally {
    closeDeleteModal();
  }
}

function closeDeleteModal() {
  isDeleteModalOpen.value = false;
  selectedBooking.value = null;
}

// Export methods
async function exportPDF() {
  try {
    const params = {
      venue_id: filters.value.venue?.id,
      status: filters.value.status,
      from_date: filters.value.dateRange?.[0]?.toISOString().split('T')[0],
      to_date: filters.value.dateRange?.[1]?.toISOString().split('T')[0]
    };

    const response = await httpClient.get<ArrayBuffer>('/venue-bookings/export/pdf', {
      params,
      responseType: 'arrayBuffer'
    });

    const blob = new Blob([response], { type: 'application/pdf' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', `venue-bookings-${new Date().toISOString().split('T')[0]}.pdf`);
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Error exporting PDF:', error);
    $toast.error('Failed to export PDF');
  }
}

async function exportExcel() {
  try {
    const params = {
      venue_id: filters.value.venue?.id,
      status: filters.value.status,
      from_date: filters.value.dateRange?.[0]?.toISOString().split('T')[0],
      to_date: filters.value.dateRange?.[1]?.toISOString().split('T')[0]
    };

    const response = await httpClient.get<ArrayBuffer>('/venue-bookings/export/excel', {
      params,
      responseType: 'arrayBuffer'
    });

    const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', `venue-bookings-${new Date().toISOString().split('T')[0]}.xlsx`);
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Error exporting Excel:', error);
    $toast.error('Failed to export Excel');
  }
}

// Utility functions
function formatDate(dateStr: string) {
  const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'short', day: 'numeric' };
  return new Date(dateStr).toLocaleDateString(undefined, options);
}

function formatCurrency(amount: number, currency: string = 'USD') {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency
  }).format(amount);
}

// Initial data fetch
onMounted(() => {
  fetchVenues();
  fetchBookings();
});
</script>
