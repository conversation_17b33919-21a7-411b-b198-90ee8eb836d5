<template>
  <div class="p-6 bg-gray-100">
    <div class="mb-6 flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold">Check-ins</h1>
        <p class="text-gray-500">Manage event check-ins and track attendance</p>
      </div>
      <div class="flex space-x-2">
        <button class="px-4 py-2 bg-white border border-gray-300 rounded shadow-sm hover:bg-gray-50 flex items-center">
          <Icon icon="heroicons:arrow-down-tray" class="w-5 h-5 text-gray-600 mr-1" />
          Export
        </button>
        <button class="px-4 py-2 bg-red-600 text-white rounded shadow-sm hover:bg-red-700 flex items-center">
          <Icon icon="heroicons:qr-code" class="w-5 h-5 mr-1" />
          Scan QR Code
        </button>
      </div>
    </div>

    <!-- Event Selector -->
    <div class="bg-white p-4 shadow mb-6">
      <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
        <div class="flex-1 max-w-md">
          <label for="event-selector" class="block text-sm font-medium text-gray-700 mb-1">Select Event</label>
          <select 
            id="event-selector" 
            v-model="selectedEventId"
            class="w-full border-gray-300 rounded-md shadow-sm focus:border-red-500 focus:ring-red-500"
          >
            <option value="">Select an event</option>
            <option v-for="event in events" :key="event.id" :value="event.id">{{ event.title }}</option>
          </select>
        </div>
        <div class="flex items-center space-x-4">
          <div class="flex items-center">
            <div class="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
            <span class="text-sm text-gray-600">Checked In: {{ checkedInCount }}</span>
          </div>
          <div class="flex items-center">
            <div class="w-3 h-3 rounded-full bg-yellow-500 mr-2"></div>
            <span class="text-sm text-gray-600">Expected: {{ expectedCount }}</span>
          </div>
          <div class="flex items-center">
            <div class="w-3 h-3 rounded-full bg-red-500 mr-2"></div>
            <span class="text-sm text-gray-600">No Shows: {{ noShowCount }}</span>
          </div>
        </div>
      </div>
    </div>

    <div v-if="!selectedEventId" class="bg-white p-8 shadow text-center">
      <Icon icon="heroicons:calendar" class="w-16 h-16 text-gray-400 mx-auto mb-4" />
      <h2 class="text-xl font-medium text-gray-700 mb-2">No Event Selected</h2>
      <p class="text-gray-500 mb-4">Please select an event to view check-in data</p>
      <button @click="openEventSelector" class="px-4 py-2 bg-red-600 text-white rounded shadow-sm hover:bg-red-700">
        Select Event
      </button>
    </div>

    <div v-else>
      <!-- Check-in Stats -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div class="bg-white p-4 shadow">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-green-100 mr-4">
              <Icon icon="heroicons:check-circle" class="w-6 h-6 text-green-600" />
            </div>
            <div>
              <p class="text-sm text-gray-500">Checked In</p>
              <p class="text-2xl font-bold">{{ checkedInCount }}</p>
              <p class="text-sm text-gray-500">{{ Math.round((checkedInCount / totalAttendees) * 100) }}% of total</p>
            </div>
          </div>
        </div>

        <div class="bg-white p-4 shadow">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-yellow-100 mr-4">
              <Icon icon="heroicons:clock" class="w-6 h-6 text-yellow-600" />
            </div>
            <div>
              <p class="text-sm text-gray-500">Expected</p>
              <p class="text-2xl font-bold">{{ expectedCount }}</p>
              <p class="text-sm text-gray-500">{{ Math.round((expectedCount / totalAttendees) * 100) }}% of total</p>
            </div>
          </div>
        </div>

        <div class="bg-white p-4 shadow">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-red-100 mr-4">
              <Icon icon="heroicons:x-circle" class="w-6 h-6 text-red-600" />
            </div>
            <div>
              <p class="text-sm text-gray-500">No Shows</p>
              <p class="text-2xl font-bold">{{ noShowCount }}</p>
              <p class="text-sm text-gray-500">{{ Math.round((noShowCount / totalAttendees) * 100) }}% of total</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Search and Filters -->
      <div class="bg-white p-4 shadow mb-6">
        <div class="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-4">
          <div class="flex-1">
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Icon icon="heroicons:magnifying-glass" class="h-5 w-5 text-gray-400" />
              </div>
              <input
                v-model="searchQuery"
                type="text"
                placeholder="Search by name, email or ticket ID"
                class="block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:border-red-500 focus:ring-red-500"
              />
            </div>
          </div>
          <div>
            <select 
              v-model="statusFilter"
              class="border-gray-300 rounded-md shadow-sm focus:border-red-500 focus:ring-red-500"
            >
              <option value="all">All Statuses</option>
              <option value="checked-in">Checked In</option>
              <option value="expected">Expected</option>
              <option value="no-show">No Show</option>
            </select>
          </div>
          <div>
            <select 
              v-model="ticketTypeFilter"
              class="border-gray-300 rounded-md shadow-sm focus:border-red-500 focus:ring-red-500"
            >
              <option value="all">All Ticket Types</option>
              <option value="general">General Admission</option>
              <option value="vip">VIP</option>
              <option value="early-bird">Early Bird</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Attendees Table -->
      <div class="bg-white shadow">
        <div v-if="loading" class="flex justify-center items-center py-20">
          <CoreLoader />
        </div>
        <div v-else>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead>
                <tr>
                  <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Attendee</th>
                  <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ticket</th>
                  <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Check-in Time</th>
                  <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="attendee in filteredAttendees" :key="attendee.id" class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="flex-shrink-0 h-10 w-10">
                        <img class="h-10 w-10 rounded-full" :src="attendee.avatar || 'https://via.placeholder.com/40'" alt="" />
                      </div>
                      <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900">{{ attendee.name }}</div>
                        <div class="text-sm text-gray-500">{{ attendee.email }}</div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">{{ attendee.ticket.type }}</div>
                    <div class="text-sm text-gray-500">ID: {{ attendee.ticket.id }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {{ attendee.checkInTime ? formatDateTime(attendee.checkInTime) : 'Not checked in' }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full" 
                      :class="{
                        'bg-green-100 text-green-800': attendee.status === 'checked-in',
                        'bg-yellow-100 text-yellow-800': attendee.status === 'expected',
                        'bg-red-100 text-red-800': attendee.status === 'no-show'
                      }">
                      {{ formatStatus(attendee.status) }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div class="flex space-x-2">
                      <button 
                        v-if="attendee.status !== 'checked-in'"
                        @click="checkInAttendee(attendee)" 
                        class="text-green-600 hover:text-green-900"
                      >
                        <Icon icon="heroicons:check-circle" class="w-5 h-5" />
                      </button>
                      <button 
                        v-if="attendee.status === 'checked-in'"
                        @click="undoCheckIn(attendee)" 
                        class="text-yellow-600 hover:text-yellow-900"
                      >
                        <Icon icon="heroicons:arrow-uturn-left" class="w-5 h-5" />
                      </button>
                      <button 
                        v-if="attendee.status !== 'no-show'"
                        @click="markNoShow(attendee)" 
                        class="text-red-600 hover:text-red-900"
                      >
                        <Icon icon="heroicons:x-circle" class="w-5 h-5" />
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Pagination -->
          <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div class="flex-1 flex justify-between sm:hidden">
              <button @click="prevPage" :disabled="currentPage === 1" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                Previous
              </button>
              <button @click="nextPage" :disabled="currentPage === totalPages" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                Next
              </button>
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p class="text-sm text-gray-700">
                  Showing <span class="font-medium">{{ paginationStart }}</span> to <span class="font-medium">{{ paginationEnd }}</span> of <span class="font-medium">{{ totalItems }}</span> attendees
                </p>
              </div>
              <div>
                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                  <button @click="prevPage" :disabled="currentPage === 1" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                    <span class="sr-only">Previous</span>
                    <Icon icon="heroicons:chevron-left" class="h-5 w-5" />
                  </button>
                  <button v-for="page in displayedPages" :key="page" @click="goToPage(page)" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium" :class="page === currentPage ? 'z-10 bg-red-50 border-red-500 text-red-600' : 'text-gray-500 hover:bg-gray-50'">
                    {{ page }}
                  </button>
                  <button @click="nextPage" :disabled="currentPage === totalPages" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                    <span class="sr-only">Next</span>
                    <Icon icon="heroicons:chevron-right" class="h-5 w-5" />
                  </button>
                </nav>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { ENDPOINTS } from '@/utils/api';

definePageMeta({
  layout: "dashboard",
});

const loading = ref(true);
const searchQuery = ref('');
const selectedEventId = ref('');
const statusFilter = ref('all');
const ticketTypeFilter = ref('all');
const currentPage = ref(1);
const itemsPerPage = 10;
const totalItems = ref(0);

// Mock data - would be replaced with API calls
const events = ref([
  { id: 1, title: 'Summer Music Festival (Jun 15, 2024)' },
  { id: 2, title: 'Tech Conference 2024 (May 10, 2024)' },
  { id: 3, title: 'Food & Wine Expo (Apr 22, 2024)' },
]);

const attendees = ref([
  {
    id: 1,
    name: 'John Doe',
    email: '<EMAIL>',
    avatar: 'https://randomuser.me/api/portraits/men/1.jpg',
    ticket: { id: 'TKT-001', type: 'VIP' },
    checkInTime: '2024-06-15T10:15:00',
    status: 'checked-in'
  },
  {
    id: 2,
    name: 'Jane Smith',
    email: '<EMAIL>',
    avatar: 'https://randomuser.me/api/portraits/women/2.jpg',
    ticket: { id: 'TKT-002', type: 'General Admission' },
    checkInTime: '2024-06-15T10:30:00',
    status: 'checked-in'
  },
  {
    id: 3,
    name: 'Robert Johnson',
    email: '<EMAIL>',
    avatar: 'https://randomuser.me/api/portraits/men/3.jpg',
    ticket: { id: 'TKT-003', type: 'Early Bird' },
    checkInTime: null,
    status: 'expected'
  },
  {
    id: 4,
    name: 'Emily Davis',
    email: '<EMAIL>',
    avatar: 'https://randomuser.me/api/portraits/women/4.jpg',
    ticket: { id: 'TKT-004', type: 'General Admission' },
    checkInTime: null,
    status: 'no-show'
  },
  {
    id: 5,
    name: 'Michael Wilson',
    email: '<EMAIL>',
    avatar: 'https://randomuser.me/api/portraits/men/5.jpg',
    ticket: { id: 'TKT-005', type: 'VIP' },
    checkInTime: null,
    status: 'expected'
  }
]);

const filteredAttendees = computed(() => {
  let result = [...attendees.value];
  
  // Apply search
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(a => 
      a.name.toLowerCase().includes(query) || 
      a.email.toLowerCase().includes(query) ||
      a.ticket.id.toLowerCase().includes(query)
    );
  }
  
  // Apply status filter
  if (statusFilter.value !== 'all') {
    result = result.filter(a => a.status === statusFilter.value);
  }
  
  // Apply ticket type filter
  if (ticketTypeFilter.value !== 'all') {
    result = result.filter(a => a.ticket.type.toLowerCase() === ticketTypeFilter.value);
  }
  
  totalItems.value = result.length;
  
  // Apply pagination
  const start = (currentPage.value - 1) * itemsPerPage;
  const end = start + itemsPerPage;
  return result.slice(start, end);
});

const totalAttendees = computed(() => attendees.value.length);

const checkedInCount = computed(() => 
  attendees.value.filter(a => a.status === 'checked-in').length
);

const expectedCount = computed(() => 
  attendees.value.filter(a => a.status === 'expected').length
);

const noShowCount = computed(() => 
  attendees.value.filter(a => a.status === 'no-show').length
);

const totalPages = computed(() => Math.ceil(totalItems.value / itemsPerPage));

const paginationStart = computed(() => {
  if (totalItems.value === 0) return 0;
  return (currentPage.value - 1) * itemsPerPage + 1;
});

const paginationEnd = computed(() => {
  return Math.min(currentPage.value * itemsPerPage, totalItems.value);
});

const displayedPages = computed(() => {
  const pages = [];
  const maxPagesToShow = 5;
  
  if (totalPages.value <= maxPagesToShow) {
    for (let i = 1; i <= totalPages.value; i++) {
      pages.push(i);
    }
  } else {
    const leftSide = Math.floor(maxPagesToShow / 2);
    const rightSide = maxPagesToShow - leftSide - 1;
    
    if (currentPage.value > leftSide && currentPage.value < totalPages.value - rightSide) {
      // Middle case
      for (let i = currentPage.value - leftSide; i <= currentPage.value + rightSide; i++) {
        pages.push(i);
      }
    } else if (currentPage.value <= leftSide) {
      // Near the start
      for (let i = 1; i <= maxPagesToShow; i++) {
        pages.push(i);
      }
    } else {
      // Near the end
      for (let i = totalPages.value - maxPagesToShow + 1; i <= totalPages.value; i++) {
        pages.push(i);
      }
    }
  }
  
  return pages;
});

function formatDateTime(dateTimeString) {
  return new Date(dateTimeString).toLocaleString('en-US', {
    month: 'short',
    day: 'numeric',
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  });
}

function formatStatus(status) {
  switch (status) {
    case 'checked-in': return 'Checked In';
    case 'expected': return 'Expected';
    case 'no-show': return 'No Show';
    default: return status;
  }
}

function openEventSelector() {
  // Focus on the event selector dropdown
  document.getElementById('event-selector')?.focus();
}

function prevPage() {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
}

function nextPage() {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
}

function goToPage(page) {
  currentPage.value = page;
}

function checkInAttendee(attendee) {
  // In a real app, this would call an API
  attendee.status = 'checked-in';
  attendee.checkInTime = new Date().toISOString();
}

function undoCheckIn(attendee) {
  // In a real app, this would call an API
  attendee.status = 'expected';
  attendee.checkInTime = null;
}

function markNoShow(attendee) {
  // In a real app, this would call an API
  attendee.status = 'no-show';
}

watch([searchQuery, statusFilter, ticketTypeFilter], () => {
  currentPage.value = 1; // Reset to first page when filters change
});

watch(selectedEventId, () => {
  if (selectedEventId.value) {
    loading.value = true;
    // In a real app, this would fetch attendees for the selected event
    setTimeout(() => {
      loading.value = false;
    }, 1000);
  }
});

// Simulate API call
onMounted(() => {
  setTimeout(() => {
    loading.value = false;
  }, 1000);
});
</script>
