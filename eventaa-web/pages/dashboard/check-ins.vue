<template>
  <div class="p-6 bg-zinc-100 dark:bg-zinc-900 min-h-screen">
    <div class="mb-6 flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Check-ins</h1>
        <p class="text-gray-500 dark:text-gray-400">Manage event check-ins and track attendance</p>
      </div>
      <div class="flex space-x-2">
        <button @click="exportCheckIns" :disabled="loading" class="px-4 py-2 bg-white dark:bg-zinc-800 border border-zinc-300 dark:border-zinc-600 shadow-sm hover:bg-zinc-50 dark:hover:bg-zinc-700 flex items-center text-gray-700 dark:text-gray-300 disabled:opacity-50">
          <Icon icon="mdi:microsoft-excel" class="w-5 h-5 text-green-600 mr-1" />
          Export
        </button>
        <button @click="openQRScanner" class="px-4 py-2 bg-red-600 text-white shadow-sm hover:bg-red-700 flex items-center">
          <Icon icon="heroicons:qr-code" class="w-5 h-5 mr-1" />
          Scan QR Code
        </button>
      </div>
    </div>

    <div class="bg-white dark:bg-zinc-800 p-4 shadow mb-6">
      <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
        <div class="flex-1 max-w-md">
          <label for="event-selector" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Select Event</label>
          <div class="relative">
            <select
              id="event-selector"
              v-model="filters.eventId"
              class="w-full border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-gray-900 dark:text-white shadow-sm focus:border-red-500 focus:ring-red-500 appearance-none pr-10"
            >
              <option value="">Select an event</option>
              <option v-for="event in events" :key="event.id" :value="event.id">{{ event.title }}</option>
            </select>
            <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
              <Icon icon="heroicons:chevron-up-down" class="h-5 w-5 text-gray-400 dark:text-gray-500" />
            </div>
          </div>
        </div>
        <div class="flex items-center space-x-4">
          <div class="flex items-center">
            <div class="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
            <span class="text-sm text-gray-600 dark:text-gray-400">Checked In: {{ stats.checked_in }}</span>
          </div>
          <div class="flex items-center">
            <div class="w-3 h-3 rounded-full bg-yellow-500 mr-2"></div>
            <span class="text-sm text-gray-600 dark:text-gray-400">Expected: {{ stats.expected }}</span>
          </div>
          <div class="flex items-center">
            <div class="w-3 h-3 rounded-full bg-red-500 mr-2"></div>
            <span class="text-sm text-gray-600 dark:text-gray-400">No Shows: {{ stats.no_show }}</span>
          </div>
        </div>
      </div>
    </div>

    <div v-if="!filters.eventId" class="bg-white dark:bg-zinc-800 p-8 shadow text-center">
      <Icon icon="heroicons:calendar" class="w-16 h-16 text-gray-400 dark:text-gray-600 mx-auto mb-4" />
      <h2 class="text-xl font-medium text-gray-700 dark:text-gray-300 mb-2">No Event Selected</h2>
      <p class="text-gray-500 dark:text-gray-400 mb-4">Please select an event to view check-in data</p>
      <button @click="openEventSelector" class="px-4 py-2 bg-red-600 text-white shadow-sm hover:bg-red-700">
        Select Event
      </button>
    </div>

    <div v-else>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div class="bg-white dark:bg-zinc-800 p-4 shadow">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-green-100 dark:bg-green-900 mr-4">
              <Icon icon="heroicons:check-circle" class="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
            <div>
              <p class="text-sm text-gray-500 dark:text-gray-400">Checked In</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ stats.checked_in }}</p>
              <p class="text-sm text-gray-500 dark:text-gray-400">{{ stats.check_in_rate }}% of total</p>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-zinc-800 p-4 shadow">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-yellow-100 dark:bg-yellow-900 mr-4">
              <Icon icon="heroicons:clock" class="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
            </div>
            <div>
              <p class="text-sm text-gray-500 dark:text-gray-400">Expected</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ stats.expected }}</p>
              <p class="text-sm text-gray-500 dark:text-gray-400">{{ Math.round((stats.expected / stats.total_attendees) * 100) }}% of total</p>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-zinc-800 p-4 shadow">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-red-100 dark:bg-red-900 mr-4">
              <Icon icon="heroicons:x-circle" class="w-6 h-6 text-red-600 dark:text-red-400" />
            </div>
            <div>
              <p class="text-sm text-gray-500 dark:text-gray-400">No Shows</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ stats.no_show }}</p>
              <p class="text-sm text-gray-500 dark:text-gray-400">{{ stats.total_attendees > 0 ? Math.round((stats.no_show / stats.total_attendees) * 100) : 0 }}% of total</p>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-zinc-800 p-4 shadow mb-6">
        <div class="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-4">
          <div class="flex-1">
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Icon icon="heroicons:magnifying-glass" class="h-5 w-5 text-gray-400 dark:text-gray-500" />
              </div>
              <input
                v-model="searchQuery"
                type="text"
                placeholder="Search by name, email or ticket ID"
                class="block w-full pl-10 border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-gray-900 dark:text-white shadow-sm focus:border-red-500 focus:ring-red-500"
              />
            </div>
          </div>
          <div class="relative">
            <select
              v-model="filters.status"
              class="border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-gray-900 dark:text-white shadow-sm focus:border-red-500 focus:ring-red-500 appearance-none pr-10"
            >
              <option value="">All Statuses</option>
              <option value="checked-in">Checked In</option>
              <option value="confirmed">Expected</option>
              <option value="no-show">No Show</option>
            </select>
            <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
              <Icon icon="heroicons:chevron-up-down" class="h-5 w-5 text-gray-400 dark:text-gray-500" />
            </div>
          </div>
          <div class="relative">
            <select
              v-model="filters.ticketType"
              class="border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-gray-900 dark:text-white shadow-sm focus:border-red-500 focus:ring-red-500 appearance-none pr-10"
            >
              <option value="">All Ticket Types</option>
              <option v-for="ticketType in availableTicketTypes" :key="ticketType" :value="ticketType">{{ ticketType }}</option>
            </select>
            <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
              <Icon icon="heroicons:chevron-up-down" class="h-5 w-5 text-gray-400 dark:text-gray-500" />
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-zinc-800 shadow">
        <div v-if="loading" class="flex justify-center items-center py-20">
          <CoreLoader />
        </div>
        <div v-else-if="attendees.length === 0" class="flex flex-col justify-center items-center py-20">
          <Icon icon="heroicons:users" class="w-16 h-16 text-gray-400 dark:text-gray-600 mb-4" />
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No attendees found</h3>
          <p class="text-gray-500 dark:text-gray-400 text-center max-w-md">
            No attendees found for the selected event and filters.
          </p>
        </div>
        <div v-else>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead>
                <tr>
                  <th class="px-6 py-3 bg-zinc-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Attendee</th>
                  <th class="px-6 py-3 bg-zinc-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Ticket</th>
                  <th class="px-6 py-3 bg-zinc-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Check-in Time</th>
                  <th class="px-6 py-3 bg-zinc-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                  <th class="px-6 py-3 bg-zinc-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody class="bg-white dark:bg-zinc-800 divide-y divide-gray-200 dark:divide-gray-700">
                <tr v-for="attendee in attendees" :key="attendee.id" class="hover:bg-zinc-50 dark:hover:bg-zinc-700">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="flex-shrink-0 h-10 w-10">
                        <img class="h-10 w-10 rounded-full" :src="`${runtimeConfig.public.baseUrl}storage/avatars/${attendee.avatar}`" alt="" />
                      </div>
                      <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900 dark:text-white">{{ attendee.name }}</div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">{{ attendee.email }}</div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900 dark:text-white">{{ attendee.ticket.type }}</div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">ID: {{ attendee.ticket.id }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {{ attendee.checkInTime ? formatDateTime(attendee.checkInTime) : 'Not checked in' }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                      :class="{
                        'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200': attendee.status === 'checked-in',
                        'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200': attendee.status === 'confirmed',
                        'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200': attendee.status === 'no-show'
                      }">
                      {{ formatStatus(attendee.status) }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div class="flex space-x-2">
                      <button
                        v-if="attendee.status !== 'checked-in'"
                        @click="checkInAttendee(attendee)"
                        class="text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-200"
                        title="Check In"
                      >
                        <Icon icon="heroicons:check-circle" class="w-5 h-5" />
                      </button>
                      <button
                        v-if="attendee.status === 'checked-in'"
                        @click="undoCheckIn(attendee)"
                        class="text-yellow-600 dark:text-yellow-400 hover:text-yellow-900 dark:hover:text-yellow-200"
                        title="Undo Check-in"
                      >
                        <Icon icon="heroicons:arrow-uturn-left" class="w-5 h-5" />
                      </button>
                      <button
                        v-if="attendee.status !== 'no-show'"
                        @click="markNoShow(attendee)"
                        class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-200"
                        title="Mark as No Show"
                      >
                        <Icon icon="heroicons:x-circle" class="w-5 h-5" />
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <div class="bg-white dark:bg-zinc-800 px-4 py-3 flex items-center justify-between border-t border-zinc-200 dark:border-zinc-700 sm:px-6">
            <div class="flex-1 flex justify-between sm:hidden">
              <button @click="prevPage" :disabled="currentPage === 1" class="relative inline-flex items-center px-4 py-2 border border-zinc-300 dark:border-zinc-600 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-zinc-700 hover:bg-zinc-50 dark:hover:bg-zinc-600 disabled:opacity-50">
                Previous
              </button>
              <button @click="nextPage" :disabled="currentPage === totalPages" class="ml-3 relative inline-flex items-center px-4 py-2 border border-zinc-300 dark:border-zinc-600 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-zinc-700 hover:bg-zinc-50 dark:hover:bg-zinc-600 disabled:opacity-50">
                Next
              </button>
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p class="text-sm text-gray-700 dark:text-gray-300">
                  Showing <span class="font-medium">{{ paginationStart }}</span> to <span class="font-medium">{{ paginationEnd }}</span> of <span class="font-medium">{{ totalItems }}</span> attendees
                </p>
              </div>
              <div>
                <nav class="relative z-0 inline-flex shadow-sm -space-x-px" aria-label="Pagination">
                  <button @click="prevPage" :disabled="currentPage === 1" class="relative inline-flex items-center px-2 py-2 border border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-zinc-50 dark:hover:bg-zinc-600 disabled:opacity-50">
                    <span class="sr-only">Previous</span>
                    <Icon icon="heroicons:chevron-left" class="h-5 w-5" />
                  </button>
                  <button v-for="page in displayedPages" :key="page" @click="goToPage(page)" class="relative inline-flex items-center px-4 py-2 border border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-sm font-medium" :class="page === currentPage ? 'z-10 bg-red-500 dark:bg-red-900 border-red-500 text-red-600 dark:text-red-200' : 'text-gray-500 dark:text-gray-400 hover:bg-zinc-50 dark:hover:bg-zinc-600'">
                    {{ page }}
                  </button>
                  <button @click="nextPage" :disabled="currentPage === totalPages" class="relative inline-flex items-center px-2 py-2 border border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-zinc-50 dark:hover:bg-zinc-600 disabled:opacity-50">
                    <span class="sr-only">Next</span>
                    <Icon icon="heroicons:chevron-right" class="h-5 w-5" />
                  </button>
                </nav>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- QR Scanner Modal -->
    <TransitionRoot appear :show="isQRScannerOpen" as="template">
      <Dialog as="div" @close="closeQRScanner" class="relative z-10">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-25 dark:bg-opacity-75" />
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div class="flex min-h-full items-center justify-center p-4 text-center">
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel class="w-full max-w-md transform overflow-hidden bg-white dark:bg-zinc-800 p-6 text-left align-middle shadow-xl transition-all">
                <DialogTitle as="h3" class="text-lg font-medium leading-6 text-gray-900 dark:text-white mb-4">
                  Scan QR Code
                </DialogTitle>

                <div class="space-y-4">
                  <div class="bg-zinc-100 dark:bg-zinc-700 p-8 text-center">
                    <Icon icon="heroicons:qr-code" class="w-16 h-16 text-gray-400 dark:text-gray-500 mx-auto mb-4" />
                    <p class="text-sm text-gray-600 dark:text-gray-400">QR Scanner would be implemented here</p>
                    <p class="text-xs text-gray-500 dark:text-gray-500 mt-2">Use device camera to scan attendee QR codes</p>
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Or enter code manually:</label>
                    <input
                      v-model="scanResult"
                      type="text"
                      placeholder="Enter QR code data"
                      class="block w-full border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-gray-900 dark:text-white shadow-sm focus:border-red-500 focus:ring-red-500"
                    />
                  </div>
                </div>

                <div class="mt-6 flex justify-end space-x-2">
                  <button
                    type="button"
                    class="inline-flex justify-center border border-transparent bg-zinc-100 dark:bg-zinc-700 px-4 py-2 text-sm font-medium text-gray-900 dark:text-gray-300 hover:bg-zinc-200 dark:hover:bg-zinc-600 focus:outline-none"
                    @click="closeQRScanner"
                  >
                    Cancel
                  </button>
                  <button
                    v-if="scanResult"
                    type="button"
                    class="inline-flex justify-center border border-transparent bg-red-600 px-4 py-2 text-sm font-medium text-white hover:bg-red-700 focus:outline-none"
                    @click="handleQRScan(scanResult)"
                  >
                    Process Code
                  </button>
                </div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue';
import { ENDPOINTS } from '@/utils/api';
import type { EventItem } from '~/types';
import type { CheckInAttendee, CheckInFilters, CheckInResponse, CheckInStats } from '~/types/checkin';
import type { EventsResponse } from '~/types/attendee';

definePageMeta({
  layout: "dashboard",
});

const runtimeConfig = useRuntimeConfig();
const loading = ref(true);
const searchQuery = ref('');
const currentPage = ref(1);
const itemsPerPage = 10;
const totalItems = ref(0);
const totalPages = computed(() => Math.ceil(totalItems.value / itemsPerPage));
const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

const filters = ref<CheckInFilters>({
  eventId: '',
  status: '',
  ticketType: '',
});

const events = ref<EventItem[]>([]);
const attendees = ref<CheckInAttendee[]>([]);
const ticketTypes = ref<string[]>([]);
const stats = ref<CheckInStats>({
  total_attendees: 0,
  checked_in: 0,
  expected: 0,
  no_show: 0,
  check_in_rate: 0,
});

const isQRScannerOpen = ref(false);
const scanResult = ref<string>('');

const availableTicketTypes = computed(() => {
  return ticketTypes.value;
});

const paginationStart = computed(() => {
  if (totalItems.value === 0) return 0;
  return (currentPage.value - 1) * itemsPerPage + 1;
});

const paginationEnd = computed(() => {
  return Math.min(currentPage.value * itemsPerPage, totalItems.value);
});

const displayedPages = computed(() => {
  const pages = [];
  const maxPagesToShow = 5;

  if (totalPages.value <= maxPagesToShow) {
    for (let i = 1; i <= totalPages.value; i++) {
      pages.push(i);
    }
  } else {
    const leftSide = Math.floor(maxPagesToShow / 2);
    const rightSide = maxPagesToShow - leftSide - 1;

    if (currentPage.value > leftSide && currentPage.value < totalPages.value - rightSide) {
      for (let i = currentPage.value - leftSide; i <= currentPage.value + rightSide; i++) {
        pages.push(i);
      }
    } else if (currentPage.value <= leftSide) {
      for (let i = 1; i <= maxPagesToShow; i++) {
        pages.push(i);
      }
    } else {
      for (let i = totalPages.value - maxPagesToShow + 1; i <= totalPages.value; i++) {
        pages.push(i);
      }
    }
  }

  return pages;
});

const fetchEvents = async () => {
  try {
    const response = await httpClient.get<EventsResponse>(ENDPOINTS.EVENTS.USER);
    events.value = response.events.data;
  } catch (error) {
    console.error('Error fetching events:', error);
    $toast.error('Failed to load events');
  }
};

const fetchTicketTypes = async () => {
  try {
    const params = new URLSearchParams();
    if (filters.value.eventId) {
      params.append('event_id', filters.value.eventId);
    }

    const response = await httpClient.get<{ ticket_types: string[] }>(
      `${ENDPOINTS.ATTENDEES.TICKET_TYPES}?${params.toString()}`
    );
    ticketTypes.value = response.ticket_types || [];
  } catch (error) {
    console.error('Error fetching ticket types:', error);
    ticketTypes.value = [];
  }
};

const fetchAttendees = async () => {
  if (!filters.value.eventId) {
    attendees.value = [];
    totalItems.value = 0;
    return;
  }

  try {
    loading.value = true;
    const params = new URLSearchParams();

    params.append('page', currentPage.value.toString());
    params.append('limit', itemsPerPage.toString());
    params.append('event_id', filters.value.eventId);

    if (searchQuery.value.trim()) {
      params.append('search', searchQuery.value.trim());
    }
    if (filters.value.status) {
      params.append('status', filters.value.status);
    }
    if (filters.value.ticketType) {
      params.append('ticket_type', filters.value.ticketType);
    }

    const response = await httpClient.get<CheckInResponse>(
      `${ENDPOINTS.ATTENDEES.GET}?${params.toString()}`
    );

    attendees.value = response.data || [];
    totalItems.value = response.total || 0;
    currentPage.value = response.current_page || 1;
  } catch (error) {
    console.error('Error fetching attendees:', error);
    $toast.error('Failed to load attendees');
    attendees.value = [];
    totalItems.value = 0;
  } finally {
    loading.value = false;
  }
};

const fetchStats = async () => {
  if (!filters.value.eventId) {
    stats.value = {
      total_attendees: 0,
      checked_in: 0,
      expected: 0,
      no_show: 0,
      check_in_rate: 0,
    };
    return;
  }

  try {
    const params = new URLSearchParams();
    params.append('event_id', filters.value.eventId);

    const response = await httpClient.get<CheckInStats>(
      `${ENDPOINTS.ATTENDEES.STATS}?${params.toString()}`
    );

    stats.value = response;
  } catch (error) {
    console.error('Error fetching stats:', error);
  }
};

const formatDateTime = (dateTimeString: string): string => {
  return new Date(dateTimeString).toLocaleString('en-US', {
    month: 'short',
    day: 'numeric',
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  });
};

const formatStatus = (status: string): string => {
  switch (status) {
    case 'checked-in': return 'Checked In';
    case 'confirmed': return 'Expected';
    case 'no-show': return 'No Show';
    default: return status;
  }
};

const openEventSelector = (): void => {
  document.getElementById('event-selector')?.focus();
};

const prevPage = (): void => {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
};

const nextPage = (): void => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
};

const goToPage = (page: number): void => {
  currentPage.value = page;
};

const checkInAttendee = async (attendee: CheckInAttendee): Promise<void> => {
  try {
    await httpClient.post(`${ENDPOINTS.ATTENDEES.CHECKIN}/${attendee.id}`);
    $toast.success(`${attendee.name} has been checked in`);

    const index = attendees.value.findIndex(a => a.id === attendee.id);
    if (index !== -1) {
      attendees.value[index].status = 'checked-in';
      attendees.value[index].checkInTime = new Date().toISOString();
    }

    await fetchStats();
  } catch (error) {
    console.error('Error checking in attendee:', error);
    $toast.error('Failed to check in attendee');
  }
};

const undoCheckIn = async (attendee: CheckInAttendee): Promise<void> => {
  try {
    await httpClient.post(`${ENDPOINTS.ATTENDEES.CHECKIN}/${attendee.id}/undo`);
    $toast.success(`Check-in undone for ${attendee.name}`);

    const index = attendees.value.findIndex(a => a.id === attendee.id);
    if (index !== -1) {
      attendees.value[index].status = 'confirmed';
      attendees.value[index].checkInTime = undefined;
    }

    await fetchStats();
  } catch (error) {
    console.error('Error undoing check-in:', error);
    $toast.error('Failed to undo check-in');
  }
};

const markNoShow = async (attendee: CheckInAttendee): Promise<void> => {
  try {
    await httpClient.post(`${ENDPOINTS.ATTENDEES.BASE}/no-show/${attendee.id}`);
    $toast.success(`${attendee.name} marked as no-show`);

    const index = attendees.value.findIndex(a => a.id === attendee.id);
    if (index !== -1) {
      attendees.value[index].status = 'no-show';
    }

    await fetchStats();
  } catch (error) {
    console.error('Error marking no-show:', error);
    $toast.error('Failed to mark as no-show');
  }
};

const exportCheckIns = async (): Promise<void> => {
  try {
    const params = new URLSearchParams();

    if (filters.value.eventId) {
      params.append('event_id', filters.value.eventId);
    }
    if (searchQuery.value.trim()) {
      params.append('search', searchQuery.value.trim());
    }
    if (filters.value.status) {
      params.append('status', filters.value.status);
    }
    if (filters.value.ticketType) {
      params.append('ticket_type', filters.value.ticketType);
    }

    const response = await httpClient.get(`${ENDPOINTS.ATTENDEES.EXPORT}?${params.toString()}`, {
      responseType: 'blob'
    }) as Blob;

    const url = window.URL.createObjectURL(response);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', `check-ins-${new Date().toISOString().split('T')[0]}.xlsx`);
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);

    $toast.success('Check-ins exported successfully');
  } catch (error) {
    console.error('Error exporting check-ins:', error);
    $toast.error('Failed to export check-ins');
  }
};

const openQRScanner = (): void => {
  isQRScannerOpen.value = true;
};

const closeQRScanner = (): void => {
  isQRScannerOpen.value = false;
  scanResult.value = '';
};

const handleQRScan = async (result: string): Promise<void> => {
  try {
    scanResult.value = result;

    const response = await httpClient.post<any>(`${ENDPOINTS.ATTENDEES.CHECKIN}/scan`, {
      qr_code: result,
      event_id: filters.value.eventId
    });

    if (response.valid) {
      $toast.success(`${response.attendee?.name} checked in successfully!`);
      await Promise.all([fetchAttendees(), fetchStats()]);
    } else {
      $toast.error(response.message || 'Invalid QR code');
    }

    closeQRScanner();
  } catch (error) {
    console.error('Error scanning QR code:', error);
    $toast.error('Failed to process QR code');
  }
};

let searchTimeout: NodeJS.Timeout;
watch(searchQuery, () => {
  clearTimeout(searchTimeout);
  searchTimeout = setTimeout(() => {
    currentPage.value = 1;
    fetchAttendees();
  }, 300);
});

watch(filters, () => {
  currentPage.value = 1;
  fetchAttendees();
  fetchStats();
}, { deep: true });

watch(() => filters.value.eventId, () => {
  filters.value.status = '';
  filters.value.ticketType = '';
  fetchTicketTypes();
});

onMounted(async () => {
  await Promise.all([
    fetchEvents(),
    fetchTicketTypes()
  ]);
});
</script>
