<template>
  <div class="min-h-screen bg-gray-50">
    <div class="mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="flex justify-between items-center mb-8">
        <h1 class="text-2xl font-semibold text-gray-900">Sponsors</h1>
        <SponsorAddDialog @sponsor-created="fetchSponsors" />
      </div>

      <div class="mb-6 flex items-center space-x-4">
        <CoreSearch
          v-model="searchQuery"
          @search="handleSearch"
          @clear="clearSearch"
        />
        <div class="relative w-48">
          <Listbox v-model="filterField">
            <div class="relative">
              <ListboxButton
                class="relative w-full cursor-default rounded-none bg-white py-2 pl-3 pr-10 text-left border focus:outline-none focus:ring-0"
              >
                <span class="block truncate">{{
                  filterOptions.find((option) => option.value === filterField)
                    ?.label
                }}</span>
                <span
                  class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
                >
                  <ChevronUpDownIcon
                    class="h-5 w-5 text-gray-400"
                    aria-hidden="true"
                  />
                </span>
              </ListboxButton>

              <transition
                leave-active-class="transition duration-100 ease-in"
                leave-from-class="opacity-100"
                leave-to-class="opacity-0"
              >
                <ListboxOptions
                  class="absolute z-10 mt-1 max-h-60 w-full overflow-auto bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"
                >
                  <ListboxOption
                    v-for="option in filterOptions"
                    :key="option.value"
                    :value="option.value"
                    v-slot="{ active, selected }"
                    as="template"
                  >
                    <li
                      :class="[
                        active ? 'bg-red-100 text-red-600' : 'text-gray-900',
                        'relative cursor-default select-none py-2 pl-10 pr-4',
                      ]"
                    >
                      <span
                        :class="[
                          selected ? 'font-medium' : 'font-normal',
                          'block truncate',
                        ]"
                      >
                        {{ option.label }}
                      </span>
                      <span
                        v-if="selected"
                        class="absolute inset-y-0 left-0 flex items-center pl-3 text-red-600"
                      >
                        <CheckIcon class="h-5 w-5" aria-hidden="true" />
                      </span>
                    </li>
                  </ListboxOption>
                </ListboxOptions>
              </transition>
            </div>
          </Listbox>
        </div>
      </div>

      <div>
        <div v-if="selectedSponsors.length" class="mt-4 flex flex-wrap gap-2 mb-3">
          <div
            v-for="sponsor in selectedSponsors"
            :key="sponsor.id"
            class="flex items-center bg-red-100 text-red-700 px-3 py-1 rounded-full"
          >
            <span class="mr-2">{{ sponsor.name }}</span>
            <button
              @click="removeSponsor(Number(sponsor.id))"
              class="text-red-600 hover:text-red-800"
            >
              <Icon icon="weui:close-outlined" class="w-4 h-4" />
            </button>
          </div>
        </div>

        <Datatable
          v-model:items-selected="selectedSponsors"
          :server-options="serverOptions"
          :loading="loading"
          :headers="headers"
          :items="filteredSponsors"
          :rows-per-page="10"
          :rows-items="[10, 25, 50, 100]"
          table-class-name="customize-table"
          theme-color="rgb(220 38 38 / 1)"
        >
          <template #item-logo="{ logo }">
            <img
              :src="`${runtimeConfig.public.baseUrl}storage/sponsors/${logo}`"
              :alt="logo"
              class="w-10 h-10 object-contain my-1"
            />
          </template>

          <template #loading>
            <CoreLoader />
          </template>

          <template #item-created_at="{ created_at }">
            {{ formatDate(created_at) }}
          </template>

          <template #item-actions="item">
            <div class="flex space-x-2 items-center">
              <SponsorEditDialog
                :sponsor="item"
                @sponsor-updated="fetchSponsors"
              />
              <SponsorDeleteDialog
                :sponsor-id="item.id"
                @sponsor-deleted="fetchSponsors"
              />
            </div>
          </template>

          <template #empty-message>
            <div class="text-center py-8">
              {{ loading ? "Loading sponsors..." : "No sponsors found." }}
            </div>
          </template>
        </Datatable>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive, watch } from "vue";
import {
  Listbox,
  ListboxButton,
  ListboxOptions,
  ListboxOption,
} from "@headlessui/vue";
import { CheckIcon, ChevronUpDownIcon } from "@heroicons/vue/20/solid";
import type { Sponsor } from "@/types";

definePageMeta({
  layout: "dashboard",
});

const httpClient = useHttpClient();
const runtimeConfig = useRuntimeConfig();
const loading = ref(false);
const sponsors = ref<Sponsor[]>([]);
const searchQuery = ref("");
const filterField = ref("all");
const selectedSponsors = ref<Sponsor[]>([]);
const searchTimeout = ref<NodeJS.Timeout | null>(null);

const filterOptions = [
  { value: "all", label: "All Fields" },
  { value: "name", label: "Name" },
  { value: "city", label: "City" },
  { value: "country", label: "Country" },
  { value: "address", label: "Address" },
];

const headers = [
  { text: "", value: "logo" },
  { text: "NAME", value: "name" },
  { text: "ADDRESS", value: "address" },
  { text: "CITY", value: "city" },
  { text: "COUNTRY", value: "country" },
  { text: "COORDINATES", value: "coordinates", sortable: false },
  { text: "DATE CREATED", value: "created_at" },
  { text: "ACTIONS", value: "actions", sortable: false },
];

const serverOptions = reactive({
  page: 1,
  rowsPerPage: 10,
  sortBy: "name",
  sortType: "asc",
});

const filteredSponsors = computed(() => {
  if (!searchQuery.value.trim()) {
    return sponsors.value;
  }

  const query = searchQuery.value.toLowerCase().trim();

  return sponsors.value.filter((sponsor) => {
    if (filterField.value === "all") {
      return (
        sponsor.name.toLowerCase().includes(query) ||
        sponsor.city.toLowerCase().includes(query) ||
        sponsor.country.toLowerCase().includes(query) ||
        sponsor.address.toLowerCase().includes(query)
      );
    } else {
      return sponsor[filterField.value as keyof Sponsor]
        ?.toString()
        .toLowerCase()
        .includes(query);
    }
  });
});

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString();
};

const removeSponsor = (id: number) => {
  selectedSponsors.value = selectedSponsors.value.filter(
    (sponsor: Sponsor) => Number(sponsor.id) !== id
  );
};

const handleSearch = () => {
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value);
  }

  searchTimeout.value = setTimeout(() => {
    serverOptions.page = 1;
  }, 300);
};

const clearSearch = () => {
  searchQuery.value = "";
  serverOptions.page = 1;
};

const fetchSponsors = async () => {
  loading.value = true;
  try {
    const response = await httpClient.get<{ sponsors: Sponsor[] }>(
      ENDPOINTS.SPONSORS.READ
    );

    sponsors.value = response.sponsors.map((sponsor) => ({
      ...sponsor,
      coordinates: `${sponsor.latitude}, ${sponsor.longitude}`,
    }));
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

watch(filterField, () => {
  serverOptions.page = 1;
});

onMounted(() => {
  fetchSponsors();
});
</script>

<style>
.customize-table {
  --easy-table-header-font-size: 14px;
  --easy-table-header-height: 50px;
  --easy-table-header-background-color: #f9fafb;
  --easy-table-header-font-color: #374151;
  --easy-table-body-row-height: 50px;
  --easy-table-body-row-font-size: 14px;
  --easy-table-body-row-font-color: #374151;
  --easy-table-body-row-hover-background-color: #f3f4f6;
}
</style>
