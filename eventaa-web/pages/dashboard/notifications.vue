<template>
  <div class="p-6 bg-gray-100">
    <div class="mb-6 flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold">Notifications</h1>
        <p class="text-gray-500">Manage your system notifications</p>
      </div>
      <div class="flex space-x-2">
        <button @click="markAllAsRead" class="px-4 py-2 bg-white border border-gray-300 rounded shadow-sm hover:bg-gray-50 flex items-center">
          <Icon icon="heroicons:check" class="w-5 h-5 text-gray-600 mr-1" />
          Mark All as Read
        </button>
        <button @click="openNotificationSettings" class="px-4 py-2 bg-white border border-gray-300 rounded shadow-sm hover:bg-gray-50 flex items-center">
          <Icon icon="heroicons:cog-6-tooth" class="w-5 h-5 text-gray-600 mr-1" />
          Settings
        </button>
      </div>
    </div>

    <!-- Filters -->
    <div class="bg-white p-4 shadow mb-6">
      <div class="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-4">
        <div class="flex-1">
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Icon icon="heroicons:magnifying-glass" class="h-5 w-5 text-gray-400" />
            </div>
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search notifications"
              class="block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:border-red-500 focus:ring-red-500"
            />
          </div>
        </div>
        <div>
          <select 
            v-model="typeFilter"
            class="border-gray-300 rounded-md shadow-sm focus:border-red-500 focus:ring-red-500"
          >
            <option value="all">All Types</option>
            <option value="system">System</option>
            <option value="event">Event</option>
            <option value="booking">Booking</option>
            <option value="payment">Payment</option>
            <option value="user">User</option>
          </select>
        </div>
        <div>
          <select 
            v-model="statusFilter"
            class="border-gray-300 rounded-md shadow-sm focus:border-red-500 focus:ring-red-500"
          >
            <option value="all">All Status</option>
            <option value="read">Read</option>
            <option value="unread">Unread</option>
          </select>
        </div>
        <div>
          <select 
            v-model="dateFilter"
            class="border-gray-300 rounded-md shadow-sm focus:border-red-500 focus:ring-red-500"
          >
            <option value="all">All Time</option>
            <option value="today">Today</option>
            <option value="week">This Week</option>
            <option value="month">This Month</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Notifications List -->
    <div class="bg-white shadow rounded">
      <div v-if="loading" class="flex justify-center items-center py-20">
        <CoreLoader />
      </div>
      <div v-else-if="filteredNotifications.length === 0" class="flex flex-col items-center justify-center py-20">
        <Icon icon="heroicons:bell-slash" class="h-16 w-16 text-gray-300 mb-4" />
        <h3 class="text-lg font-medium text-gray-700">No notifications found</h3>
        <p class="text-gray-500 mt-1">There are no notifications matching your filters</p>
      </div>
      <div v-else>
        <div class="divide-y divide-gray-200">
          <div 
            v-for="notification in filteredNotifications" 
            :key="notification.id"
            class="p-4 hover:bg-gray-50 transition-colors duration-150"
            :class="{ 'bg-red-50': !notification.read }"
          >
            <div class="flex items-start">
              <div class="flex-shrink-0 pt-0.5">
                <div 
                  class="h-10 w-10 rounded-full flex items-center justify-center"
                  :class="getNotificationTypeClass(notification.type)"
                >
                  <Icon :icon="getNotificationTypeIcon(notification.type)" class="h-5 w-5 text-white" />
                </div>
              </div>
              <div class="ml-3 flex-1">
                <div class="flex items-center justify-between">
                  <p class="text-sm font-medium text-gray-900">
                    {{ notification.title }}
                  </p>
                  <div class="flex items-center">
                    <p class="text-xs text-gray-500">{{ formatDate(notification.timestamp) }}</p>
                    <div class="ml-2 flex">
                      <button 
                        v-if="!notification.read"
                        @click="markAsRead(notification)"
                        class="text-gray-400 hover:text-gray-500"
                      >
                        <Icon icon="heroicons:check-circle" class="h-5 w-5" />
                      </button>
                      <button 
                        @click="deleteNotification(notification)"
                        class="ml-1 text-gray-400 hover:text-red-500"
                      >
                        <Icon icon="heroicons:trash" class="h-5 w-5" />
                      </button>
                    </div>
                  </div>
                </div>
                <p class="text-sm text-gray-500 mt-1">{{ notification.message }}</p>
                <div v-if="notification.actionUrl" class="mt-2">
                  <a 
                    :href="notification.actionUrl"
                    class="inline-flex items-center text-sm font-medium text-red-600 hover:text-red-500"
                  >
                    {{ notification.actionText }}
                    <Icon icon="heroicons:arrow-right" class="ml-1 h-4 w-4" />
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Pagination -->
        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
          <div class="flex-1 flex justify-between sm:hidden">
            <button @click="prevPage" :disabled="currentPage === 1" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
              Previous
            </button>
            <button @click="nextPage" :disabled="currentPage === totalPages" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
              Next
            </button>
          </div>
          <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p class="text-sm text-gray-700">
                Showing <span class="font-medium">{{ paginationStart }}</span> to <span class="font-medium">{{ paginationEnd }}</span> of <span class="font-medium">{{ totalItems }}</span> notifications
              </p>
            </div>
            <div>
              <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button @click="prevPage" :disabled="currentPage === 1" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                  <span class="sr-only">Previous</span>
                  <Icon icon="heroicons:chevron-left" class="h-5 w-5" />
                </button>
                <button v-for="page in displayedPages" :key="page" @click="goToPage(page)" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium" :class="page === currentPage ? 'z-10 bg-red-50 border-red-500 text-red-600' : 'text-gray-500 hover:bg-gray-50'">
                  {{ page }}
                </button>
                <button @click="nextPage" :disabled="currentPage === totalPages" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                  <span class="sr-only">Next</span>
                  <Icon icon="heroicons:chevron-right" class="h-5 w-5" />
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { ENDPOINTS } from '@/utils/api';

definePageMeta({
  layout: "dashboard",
});

const loading = ref(true);
const searchQuery = ref('');
const typeFilter = ref('all');
const statusFilter = ref('all');
const dateFilter = ref('all');
const currentPage = ref(1);
const itemsPerPage = 10;
const totalItems = ref(0);
const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

// Mock data - would be replaced with API calls
const notifications = ref([
  {
    id: 1,
    title: 'New Booking Request',
    message: 'You have received a new booking request for Summer Music Festival.',
    type: 'booking',
    timestamp: new Date(2024, 4, 25, 14, 30),
    read: false,
    actionUrl: '/dashboard/bookings',
    actionText: 'View Booking'
  },
  {
    id: 2,
    title: 'Payment Received',
    message: 'Payment of $150.00 has been received for order #12345.',
    type: 'payment',
    timestamp: new Date(2024, 4, 25, 10, 15),
    read: true,
    actionUrl: '/dashboard/payments',
    actionText: 'View Payment'
  },
  {
    id: 3,
    title: 'New User Registration',
    message: 'A new user has registered on your platform.',
    type: 'user',
    timestamp: new Date(2024, 4, 24, 16, 45),
    read: false,
    actionUrl: '/dashboard/users',
    actionText: 'View User'
  },
  {
    id: 4,
    title: 'Event Reminder',
    message: 'Tech Conference 2024 is scheduled for tomorrow.',
    type: 'event',
    timestamp: new Date(2024, 4, 24, 9, 20),
    read: true,
    actionUrl: '/dashboard/events',
    actionText: 'View Event'
  },
  {
    id: 5,
    title: 'System Update',
    message: 'The system will undergo maintenance on May 30, 2024, from 2:00 AM to 4:00 AM UTC.',
    type: 'system',
    timestamp: new Date(2024, 4, 23, 11, 10),
    read: true,
    actionUrl: null,
    actionText: null
  },
  {
    id: 6,
    title: 'New Review',
    message: 'Your event "Food & Wine Expo" has received a new 5-star review.',
    type: 'event',
    timestamp: new Date(2024, 4, 22, 15, 25),
    read: false,
    actionUrl: '/dashboard/reviews',
    actionText: 'View Review'
  },
  {
    id: 7,
    title: 'Subscription Renewal',
    message: 'Your subscription will renew in 7 days. Please ensure your payment method is up to date.',
    type: 'payment',
    timestamp: new Date(2024, 4, 21, 8, 45),
    read: true,
    actionUrl: '/dashboard/subscription',
    actionText: 'Manage Subscription'
  },
  {
    id: 8,
    title: 'New Message',
    message: 'You have a new message from John Doe regarding the Summer Music Festival.',
    type: 'user',
    timestamp: new Date(2024, 4, 20, 14, 15),
    read: false,
    actionUrl: '/dashboard/messages',
    actionText: 'View Message'
  },
  {
    id: 9,
    title: 'Booking Confirmed',
    message: 'Booking #54321 has been confirmed for Tech Conference 2024.',
    type: 'booking',
    timestamp: new Date(2024, 4, 19, 10, 30),
    read: true,
    actionUrl: '/dashboard/bookings',
    actionText: 'View Booking'
  },
  {
    id: 10,
    title: 'Low Ticket Alert',
    message: 'Only 10 tickets remaining for Summer Music Festival. Consider adding more capacity.',
    type: 'event',
    timestamp: new Date(2024, 4, 18, 16, 20),
    read: false,
    actionUrl: '/dashboard/events',
    actionText: 'Manage Event'
  }
]);

const filteredNotifications = computed(() => {
  let result = [...notifications.value];
  
  // Apply search
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(n => 
      n.title.toLowerCase().includes(query) || 
      n.message.toLowerCase().includes(query)
    );
  }
  
  // Apply type filter
  if (typeFilter.value !== 'all') {
    result = result.filter(n => n.type === typeFilter.value);
  }
  
  // Apply status filter
  if (statusFilter.value !== 'all') {
    result = result.filter(n => 
      (statusFilter.value === 'read' && n.read) || 
      (statusFilter.value === 'unread' && !n.read)
    );
  }
  
  // Apply date filter
  if (dateFilter.value !== 'all') {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    
    switch (dateFilter.value) {
      case 'today':
        result = result.filter(n => {
          const notifDate = new Date(n.timestamp);
          return notifDate.getDate() === today.getDate() &&
                 notifDate.getMonth() === today.getMonth() &&
                 notifDate.getFullYear() === today.getFullYear();
        });
        break;
      case 'week':
        const weekStart = new Date(today);
        weekStart.setDate(today.getDate() - today.getDay());
        result = result.filter(n => new Date(n.timestamp) >= weekStart);
        break;
      case 'month':
        const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
        result = result.filter(n => new Date(n.timestamp) >= monthStart);
        break;
    }
  }
  
  // Sort by timestamp (newest first)
  result.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
  
  totalItems.value = result.length;
  
  // Apply pagination
  const start = (currentPage.value - 1) * itemsPerPage;
  const end = start + itemsPerPage;
  return result.slice(start, end);
});

const totalPages = computed(() => Math.ceil(totalItems.value / itemsPerPage));

const paginationStart = computed(() => {
  if (totalItems.value === 0) return 0;
  return (currentPage.value - 1) * itemsPerPage + 1;
});

const paginationEnd = computed(() => {
  return Math.min(currentPage.value * itemsPerPage, totalItems.value);
});

const displayedPages = computed(() => {
  const pages = [];
  const maxPagesToShow = 5;
  
  if (totalPages.value <= maxPagesToShow) {
    for (let i = 1; i <= totalPages.value; i++) {
      pages.push(i);
    }
  } else {
    const leftSide = Math.floor(maxPagesToShow / 2);
    const rightSide = maxPagesToShow - leftSide - 1;
    
    if (currentPage.value > leftSide && currentPage.value < totalPages.value - rightSide) {
      // Middle case
      for (let i = currentPage.value - leftSide; i <= currentPage.value + rightSide; i++) {
        pages.push(i);
      }
    } else if (currentPage.value <= leftSide) {
      // Near the start
      for (let i = 1; i <= maxPagesToShow; i++) {
        pages.push(i);
      }
    } else {
      // Near the end
      for (let i = totalPages.value - maxPagesToShow + 1; i <= totalPages.value; i++) {
        pages.push(i);
      }
    }
  }
  
  return pages;
});

function formatDate(date) {
  if (!date) return '';
  
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);
  
  const notifDate = new Date(date);
  const notifDay = new Date(notifDate.getFullYear(), notifDate.getMonth(), notifDate.getDate());
  
  if (notifDay.getTime() === today.getTime()) {
    return `Today at ${notifDate.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true })}`;
  } else if (notifDay.getTime() === yesterday.getTime()) {
    return `Yesterday at ${notifDate.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true })}`;
  } else {
    return notifDate.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  }
}

function getNotificationTypeClass(type) {
  switch (type) {
    case 'system':
      return 'bg-gray-600';
    case 'event':
      return 'bg-blue-600';
    case 'booking':
      return 'bg-green-600';
    case 'payment':
      return 'bg-purple-600';
    case 'user':
      return 'bg-yellow-600';
    default:
      return 'bg-red-600';
  }
}

function getNotificationTypeIcon(type) {
  switch (type) {
    case 'system':
      return 'heroicons:cog-6-tooth';
    case 'event':
      return 'heroicons:calendar';
    case 'booking':
      return 'heroicons:ticket';
    case 'payment':
      return 'heroicons:currency-dollar';
    case 'user':
      return 'heroicons:user';
    default:
      return 'heroicons:bell';
  }
}

function markAsRead(notification) {
  // In a real app, this would call an API
  notification.read = true;
  $toast.success('Notification marked as read');
}

function markAllAsRead() {
  // In a real app, this would call an API
  notifications.value.forEach(notification => {
    notification.read = true;
  });
  $toast.success('All notifications marked as read');
}

function deleteNotification(notification) {
  // In a real app, this would call an API
  const index = notifications.value.findIndex(n => n.id === notification.id);
  if (index !== -1) {
    notifications.value.splice(index, 1);
    totalItems.value = notifications.value.length;
    $toast.success('Notification deleted');
  }
}

function openNotificationSettings() {
  // Navigate to notification settings page
  $toast.info('Navigating to notification settings');
}

function prevPage() {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
}

function nextPage() {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
}

function goToPage(page) {
  currentPage.value = page;
}

watch([searchQuery, typeFilter, statusFilter, dateFilter], () => {
  currentPage.value = 1; // Reset to first page when filters change
});

// Simulate API call
onMounted(() => {
  setTimeout(() => {
    loading.value = false;
  }, 1000);
});
</script>
