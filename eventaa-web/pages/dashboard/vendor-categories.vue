<template>
  <div class="p-6 bg-gray-100">
    <div class="mb-6 flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold">Vendor Categories</h1>
        <p class="text-gray-500">Manage categories for vendor services</p>
      </div>
      <button 
        @click="openAddCategoryModal" 
        class="px-4 py-2 bg-red-600 text-white rounded shadow-sm hover:bg-red-700 flex items-center"
      >
        <Icon icon="heroicons:plus" class="w-5 h-5 mr-1" />
        Add Category
      </button>
    </div>

    <!-- Categories Table -->
    <div class="bg-white shadow">
      <div v-if="loading" class="flex justify-center items-center py-20">
        <CoreLoader />
      </div>
      <div v-else>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead>
              <tr>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Vendors</th>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="category in categories" :key="category.id" class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10 bg-gray-200 rounded-full flex items-center justify-center">
                      <Icon :icon="category.icon || 'heroicons:tag'" class="h-5 w-5 text-gray-600" />
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium text-gray-900">{{ category.name }}</div>
                      <div class="text-sm text-gray-500">{{ category.slug }}</div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4">
                  <div class="text-sm text-gray-900">{{ truncateText(category.description, 100) }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ category.vendorCount }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ formatDate(category.createdAt) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full" 
                    :class="category.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'">
                    {{ category.isActive ? 'Active' : 'Inactive' }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div class="flex space-x-2">
                    <button @click="editCategory(category)" class="text-blue-600 hover:text-blue-900">
                      <Icon icon="heroicons:pencil-square" class="w-5 h-5" />
                    </button>
                    <button @click="confirmDeleteCategory(category)" class="text-red-600 hover:text-red-900">
                      <Icon icon="heroicons:trash" class="w-5 h-5" />
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
          <div class="flex-1 flex justify-between sm:hidden">
            <button @click="prevPage" :disabled="currentPage === 1" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
              Previous
            </button>
            <button @click="nextPage" :disabled="currentPage === totalPages" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
              Next
            </button>
          </div>
          <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p class="text-sm text-gray-700">
                Showing <span class="font-medium">{{ paginationStart }}</span> to <span class="font-medium">{{ paginationEnd }}</span> of <span class="font-medium">{{ totalItems }}</span> categories
              </p>
            </div>
            <div>
              <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button @click="prevPage" :disabled="currentPage === 1" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                  <span class="sr-only">Previous</span>
                  <Icon icon="heroicons:chevron-left" class="h-5 w-5" />
                </button>
                <button v-for="page in displayedPages" :key="page" @click="goToPage(page)" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium" :class="page === currentPage ? 'z-10 bg-red-50 border-red-500 text-red-600' : 'text-gray-500 hover:bg-gray-50'">
                  {{ page }}
                </button>
                <button @click="nextPage" :disabled="currentPage === totalPages" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                  <span class="sr-only">Next</span>
                  <Icon icon="heroicons:chevron-right" class="h-5 w-5" />
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Add/Edit Category Modal -->
    <TransitionRoot appear :show="isCategoryModalOpen" as="template">
      <Dialog as="div" @close="closeCategoryModal" class="relative z-10">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-25" />
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div class="flex min-h-full items-center justify-center p-4 text-center">
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel class="w-full max-w-md transform overflow-hidden bg-white p-6 text-left align-middle shadow-xl transition-all">
                <DialogTitle as="h3" class="text-lg font-medium leading-6 text-gray-900">
                  {{ isEditing ? 'Edit Category' : 'Add New Category' }}
                </DialogTitle>
                <div class="mt-4">
                  <form @submit.prevent="saveCategory">
                    <div class="mb-4">
                      <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Name</label>
                      <input 
                        id="name" 
                        v-model="categoryForm.name" 
                        type="text" 
                        class="w-full border-gray-300 rounded-md shadow-sm focus:border-red-500 focus:ring-red-500"
                        required
                      />
                    </div>
                    <div class="mb-4">
                      <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                      <textarea 
                        id="description" 
                        v-model="categoryForm.description" 
                        rows="3" 
                        class="w-full border-gray-300 rounded-md shadow-sm focus:border-red-500 focus:ring-red-500"
                      ></textarea>
                    </div>
                    <div class="mb-4">
                      <label for="icon" class="block text-sm font-medium text-gray-700 mb-1">Icon</label>
                      <input 
                        id="icon" 
                        v-model="categoryForm.icon" 
                        type="text" 
                        class="w-full border-gray-300 rounded-md shadow-sm focus:border-red-500 focus:ring-red-500"
                        placeholder="heroicons:tag"
                      />
                      <p class="mt-1 text-xs text-gray-500">Enter an icon name from Heroicons or other icon libraries</p>
                    </div>
                    <div class="mb-4 flex items-center">
                      <input 
                        id="isActive" 
                        v-model="categoryForm.isActive" 
                        type="checkbox" 
                        class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
                      />
                      <label for="isActive" class="ml-2 block text-sm text-gray-700">Active</label>
                    </div>
                    <div class="mt-6 flex justify-end space-x-3">
                      <button
                        type="button"
                        class="inline-flex justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none"
                        @click="closeCategoryModal"
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                        class="inline-flex justify-center rounded-md border border-transparent bg-red-600 px-4 py-2 text-sm font-medium text-white hover:bg-red-700 focus:outline-none"
                      >
                        {{ isEditing ? 'Update' : 'Create' }}
                      </button>
                    </div>
                  </form>
                </div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>

    <!-- Delete Confirmation Modal -->
    <TransitionRoot appear :show="isDeleteModalOpen" as="template">
      <Dialog as="div" @close="closeDeleteModal" class="relative z-10">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-25" />
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div class="flex min-h-full items-center justify-center p-4 text-center">
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel class="w-full max-w-md transform overflow-hidden bg-white p-6 text-left align-middle shadow-xl transition-all">
                <DialogTitle as="h3" class="text-lg font-medium leading-6 text-gray-900">
                  Delete Category
                </DialogTitle>
                <div class="mt-2">
                  <p class="text-sm text-gray-500">
                    Are you sure you want to delete the category "{{ categoryToDelete?.name }}"? This action cannot be undone.
                  </p>
                  <p class="text-sm text-red-500 mt-2" v-if="categoryToDelete?.vendorCount > 0">
                    Warning: This category is used by {{ categoryToDelete.vendorCount }} vendors. Deleting it may affect their listings.
                  </p>
                </div>

                <div class="mt-4 flex justify-end space-x-2">
                  <button
                    type="button"
                    class="inline-flex justify-center rounded-md border border-transparent bg-gray-100 px-4 py-2 text-sm font-medium text-gray-900 hover:bg-gray-200 focus:outline-none"
                    @click="closeDeleteModal"
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    class="inline-flex justify-center rounded-md border border-transparent bg-red-600 px-4 py-2 text-sm font-medium text-white hover:bg-red-700 focus:outline-none"
                    @click="deleteCategory"
                  >
                    Delete
                  </button>
                </div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue';
import { ENDPOINTS } from '@/utils/api';

definePageMeta({
  layout: "dashboard",
});

const loading = ref(true);
const currentPage = ref(1);
const itemsPerPage = 10;
const totalItems = ref(0);
const isCategoryModalOpen = ref(false);
const isDeleteModalOpen = ref(false);
const isEditing = ref(false);
const categoryToDelete = ref(null);
const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

const categoryForm = ref({
  id: null,
  name: '',
  description: '',
  icon: 'heroicons:tag',
  isActive: true
});

// Mock data - would be replaced with API calls
const categories = ref([
  {
    id: 1,
    name: 'Photography',
    slug: 'photography',
    description: 'Professional photography services for events, weddings, and more.',
    icon: 'heroicons:camera',
    vendorCount: 12,
    createdAt: '2024-01-15',
    isActive: true
  },
  {
    id: 2,
    name: 'Catering',
    slug: 'catering',
    description: 'Food and beverage services for events of all sizes.',
    icon: 'heroicons:cake',
    vendorCount: 8,
    createdAt: '2024-01-20',
    isActive: true
  },
  {
    id: 3,
    name: 'Music & Entertainment',
    slug: 'music-entertainment',
    description: 'DJs, live bands, and performers for your events.',
    icon: 'heroicons:musical-note',
    vendorCount: 15,
    createdAt: '2024-02-05',
    isActive: true
  },
  {
    id: 4,
    name: 'Venue',
    slug: 'venue',
    description: 'Event spaces and venues for all occasions.',
    icon: 'heroicons:building-office-2',
    vendorCount: 6,
    createdAt: '2024-02-10',
    isActive: true
  },
  {
    id: 5,
    name: 'Decoration',
    slug: 'decoration',
    description: 'Event decoration and styling services.',
    icon: 'heroicons:sparkles',
    vendorCount: 9,
    createdAt: '2024-02-15',
    isActive: true
  }
]);

totalItems.value = categories.value.length;

const totalPages = computed(() => Math.ceil(totalItems.value / itemsPerPage));

const paginationStart = computed(() => {
  if (totalItems.value === 0) return 0;
  return (currentPage.value - 1) * itemsPerPage + 1;
});

const paginationEnd = computed(() => {
  return Math.min(currentPage.value * itemsPerPage, totalItems.value);
});

const displayedPages = computed(() => {
  const pages = [];
  const maxPagesToShow = 5;
  
  if (totalPages.value <= maxPagesToShow) {
    for (let i = 1; i <= totalPages.value; i++) {
      pages.push(i);
    }
  } else {
    const leftSide = Math.floor(maxPagesToShow / 2);
    const rightSide = maxPagesToShow - leftSide - 1;
    
    if (currentPage.value > leftSide && currentPage.value < totalPages.value - rightSide) {
      // Middle case
      for (let i = currentPage.value - leftSide; i <= currentPage.value + rightSide; i++) {
        pages.push(i);
      }
    } else if (currentPage.value <= leftSide) {
      // Near the start
      for (let i = 1; i <= maxPagesToShow; i++) {
        pages.push(i);
      }
    } else {
      // Near the end
      for (let i = totalPages.value - maxPagesToShow + 1; i <= totalPages.value; i++) {
        pages.push(i);
      }
    }
  }
  
  return pages;
});

function formatDate(dateString) {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
}

function truncateText(text, maxLength) {
  if (!text) return '';
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
}

function prevPage() {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
}

function nextPage() {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
}

function goToPage(page) {
  currentPage.value = page;
}

function openAddCategoryModal() {
  isEditing.value = false;
  categoryForm.value = {
    id: null,
    name: '',
    description: '',
    icon: 'heroicons:tag',
    isActive: true
  };
  isCategoryModalOpen.value = true;
}

function editCategory(category) {
  isEditing.value = true;
  categoryForm.value = {
    id: category.id,
    name: category.name,
    description: category.description,
    icon: category.icon,
    isActive: category.isActive
  };
  isCategoryModalOpen.value = true;
}

function closeCategoryModal() {
  isCategoryModalOpen.value = false;
}

async function saveCategory() {
  try {
    if (isEditing.value) {
      // Update existing category
      // In a real app, this would call an API
      // await httpClient.put(`${ENDPOINTS.CATEGORIES.BASE}/${categoryForm.value.id}`, categoryForm.value);
      
      const index = categories.value.findIndex(c => c.id === categoryForm.value.id);
      if (index !== -1) {
        categories.value[index] = {
          ...categories.value[index],
          name: categoryForm.value.name,
          description: categoryForm.value.description,
          icon: categoryForm.value.icon,
          isActive: categoryForm.value.isActive
        };
      }
      
      $toast.success('Category updated successfully');
    } else {
      // Create new category
      // In a real app, this would call an API
      // const response = await httpClient.post(ENDPOINTS.CATEGORIES.BASE, categoryForm.value);
      
      const newCategory = {
        id: categories.value.length + 1,
        name: categoryForm.value.name,
        slug: categoryForm.value.name.toLowerCase().replace(/\s+/g, '-'),
        description: categoryForm.value.description,
        icon: categoryForm.value.icon,
        vendorCount: 0,
        createdAt: new Date().toISOString().split('T')[0],
        isActive: categoryForm.value.isActive
      };
      
      categories.value.push(newCategory);
      totalItems.value = categories.value.length;
      
      $toast.success('Category created successfully');
    }
    
    closeCategoryModal();
  } catch (error) {
    console.error('Error saving category:', error);
    $toast.error('Failed to save category. Please try again.');
  }
}

function confirmDeleteCategory(category) {
  categoryToDelete.value = category;
  isDeleteModalOpen.value = true;
}

function closeDeleteModal() {
  isDeleteModalOpen.value = false;
  categoryToDelete.value = null;
}

async function deleteCategory() {
  try {
    // In a real app, this would call an API
    // await httpClient.delete(`${ENDPOINTS.CATEGORIES.BASE}/${categoryToDelete.value.id}`);
    
    categories.value = categories.value.filter(c => c.id !== categoryToDelete.value.id);
    totalItems.value = categories.value.length;
    
    $toast.success(`Category "${categoryToDelete.value.name}" deleted successfully`);
    closeDeleteModal();
  } catch (error) {
    console.error('Error deleting category:', error);
    $toast.error('Failed to delete category. Please try again.');
  }
}

// Simulate API call
onMounted(() => {
  setTimeout(() => {
    loading.value = false;
  }, 1000);
});
</script>
