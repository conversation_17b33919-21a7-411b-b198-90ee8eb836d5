<template>
  <div class="p-6 bg-gray-100">
    <div class="mb-6 flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold">Payments</h1>
        <p class="text-gray-500">Manage event ticket payments and transactions</p>
      </div>
      <div class="flex space-x-2">
        <button @click="exportTransactions" class="px-4 py-2 bg-white border border-gray-300 rounded shadow-sm hover:bg-gray-50 flex items-center">
          <Icon icon="heroicons:arrow-down-tray" class="w-5 h-5 text-gray-600 mr-1" />
          Export
        </button>
      </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <div class="bg-white p-4 shadow rounded">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-green-100 mr-4">
            <Icon icon="heroicons:currency-dollar" class="w-6 h-6 text-green-600" />
          </div>
          <div>
            <p class="text-sm text-gray-500">Total Revenue</p>
            <p class="text-2xl font-bold">{{ formatCurrency(stats.totalRevenue) }}</p>
            <div class="flex items-center text-sm">
              <span :class="stats.revenueGrowth >= 0 ? 'text-green-600' : 'text-red-600'">
                <Icon :icon="stats.revenueGrowth >= 0 ? 'heroicons:arrow-up' : 'heroicons:arrow-down'" class="w-4 h-4 inline" />
                {{ Math.abs(stats.revenueGrowth) }}%
              </span>
              <span class="text-gray-500 ml-1">vs last month</span>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white p-4 shadow rounded">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-blue-100 mr-4">
            <Icon icon="heroicons:shopping-cart" class="w-6 h-6 text-blue-600" />
          </div>
          <div>
            <p class="text-sm text-gray-500">Transactions</p>
            <p class="text-2xl font-bold">{{ stats.totalTransactions }}</p>
            <div class="flex items-center text-sm">
              <span :class="stats.transactionsGrowth >= 0 ? 'text-green-600' : 'text-red-600'">
                <Icon :icon="stats.transactionsGrowth >= 0 ? 'heroicons:arrow-up' : 'heroicons:arrow-down'" class="w-4 h-4 inline" />
                {{ Math.abs(stats.transactionsGrowth) }}%
              </span>
              <span class="text-gray-500 ml-1">vs last month</span>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white p-4 shadow rounded">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-yellow-100 mr-4">
            <Icon icon="heroicons:ticket" class="w-6 h-6 text-yellow-600" />
          </div>
          <div>
            <p class="text-sm text-gray-500">Tickets Sold</p>
            <p class="text-2xl font-bold">{{ stats.ticketsSold }}</p>
            <div class="flex items-center text-sm">
              <span :class="stats.ticketsGrowth >= 0 ? 'text-green-600' : 'text-red-600'">
                <Icon :icon="stats.ticketsGrowth >= 0 ? 'heroicons:arrow-up' : 'heroicons:arrow-down'" class="w-4 h-4 inline" />
                {{ Math.abs(stats.ticketsGrowth) }}%
              </span>
              <span class="text-gray-500 ml-1">vs last month</span>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white p-4 shadow rounded">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-red-100 mr-4">
            <Icon icon="heroicons:arrow-path" class="w-6 h-6 text-red-600" />
          </div>
          <div>
            <p class="text-sm text-gray-500">Refunds</p>
            <p class="text-2xl font-bold">{{ formatCurrency(stats.totalRefunds) }}</p>
            <div class="flex items-center text-sm">
              <span :class="stats.refundsGrowth <= 0 ? 'text-green-600' : 'text-red-600'">
                <Icon :icon="stats.refundsGrowth <= 0 ? 'heroicons:arrow-down' : 'heroicons:arrow-up'" class="w-4 h-4 inline" />
                {{ Math.abs(stats.refundsGrowth) }}%
              </span>
              <span class="text-gray-500 ml-1">vs last month</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="bg-white p-4 shadow mb-6">
      <div class="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-4">
        <div class="flex-1">
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Icon icon="heroicons:magnifying-glass" class="h-5 w-5 text-gray-400" />
            </div>
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search by transaction ID, customer, or event"
              class="block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:border-red-500 focus:ring-red-500"
            />
          </div>
        </div>
        <div>
          <select
            v-model="statusFilter"
            class="border-gray-300 rounded-md shadow-sm focus:border-red-500 focus:ring-red-500"
          >
            <option value="all">All Statuses</option>
            <option value="completed">Completed</option>
            <option value="pending">Pending</option>
            <option value="failed">Failed</option>
            <option value="refunded">Refunded</option>
          </select>
        </div>
        <div>
          <select
            v-model="eventFilter"
            class="border-gray-300 rounded-md shadow-sm focus:border-red-500 focus:ring-red-500"
          >
            <option value="all">All Events</option>
            <option v-for="event in events" :key="event.id" :value="event.id">{{ event.title }}</option>
          </select>
        </div>
        <div>
          <select
            v-model="dateFilter"
            class="border-gray-300 rounded-md shadow-sm focus:border-red-500 focus:ring-red-500"
          >
            <option value="all">All Time</option>
            <option value="today">Today</option>
            <option value="week">This Week</option>
            <option value="month">This Month</option>
            <option value="year">This Year</option>
          </select>
        </div>
      </div>
    </div>

    <div class="bg-white shadow">
      <div v-if="loading" class="flex justify-center items-center py-20">
        <CoreLoader />
      </div>
      <div v-else>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead>
              <tr>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Transaction ID</th>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Event</th>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payment Method</th>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="transaction in filteredTransactions" :key="transaction.id" class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {{ transaction.transaction_id || transaction.transactionId }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10">
                      <img class="h-10 w-10 rounded-full" :src="transaction.customer?.avatar || 'https://via.placeholder.com/40'" alt="" />
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium text-gray-900">{{ transaction.customer?.name || transaction.customer_name }}</div>
                      <div class="text-sm text-gray-500">{{ transaction.customer?.email || transaction.customer_email }}</div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ transaction.event?.title || transaction.event_name }}</div>
                  <div class="text-sm text-gray-500">{{ formatDate(transaction.event?.date || transaction.created_at || '') }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ formatCurrency(transaction.amount) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ formatDate(transaction.date || transaction.created_at || '') }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <div class="flex items-center">
                    <Icon :icon="getPaymentMethodIcon(transaction.paymentMethod || transaction.payment_method || '')" class="w-5 h-5 mr-1" />
                    {{ transaction.paymentMethod || transaction.payment_method }}
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                    :class="{
                      'bg-green-100 text-green-800': transaction.status === 'completed',
                      'bg-yellow-100 text-yellow-800': transaction.status === 'pending',
                      'bg-red-100 text-red-800': transaction.status === 'failed',
                      'bg-gray-100 text-gray-800': transaction.status === 'refunded'
                    }">
                    {{ formatStatus(transaction.status) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div class="flex space-x-2">
                    <button @click="viewTransactionDetails(transaction)" class="text-gray-600 hover:text-gray-900">
                      <Icon icon="heroicons:eye" class="w-5 h-5" />
                    </button>
                    <button
                      v-if="transaction.status === 'completed'"
                      @click="issueRefund(transaction)"
                      class="text-red-600 hover:text-red-900"
                    >
                      <Icon icon="heroicons:arrow-path" class="w-5 h-5" />
                    </button>
                    <button @click="downloadReceipt(transaction)" class="text-blue-600 hover:text-blue-900">
                      <Icon icon="heroicons:document-arrow-down" class="w-5 h-5" />
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
          <div class="flex-1 flex justify-between sm:hidden">
            <button @click="prevPage" :disabled="currentPage === 1" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
              Previous
            </button>
            <button @click="nextPage" :disabled="currentPage === totalPages" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
              Next
            </button>
          </div>
          <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p class="text-sm text-gray-700">
                Showing <span class="font-medium">{{ paginationStart }}</span> to <span class="font-medium">{{ paginationEnd }}</span> of <span class="font-medium">{{ totalItems }}</span> transactions
              </p>
            </div>
            <div>
              <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button @click="prevPage" :disabled="currentPage === 1" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                  <span class="sr-only">Previous</span>
                  <Icon icon="heroicons:chevron-left" class="h-5 w-5" />
                </button>
                <button v-for="page in displayedPages" :key="page" @click="goToPage(page)" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium" :class="page === currentPage ? 'z-10 bg-red-50 border-red-500 text-red-600' : 'text-gray-500 hover:bg-gray-50'">
                  {{ page }}
                </button>
                <button @click="nextPage" :disabled="currentPage === totalPages" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                  <span class="sr-only">Next</span>
                  <Icon icon="heroicons:chevron-right" class="h-5 w-5" />
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { ENDPOINTS } from '@/utils/api';
import type { EventItem } from '@/types';

definePageMeta({
  layout: "dashboard",
});

const loading = ref(true);
const searchQuery = ref('');
const statusFilter = ref('all');
const eventFilter = ref('all');
const dateFilter = ref('all');
const currentPage = ref(1);
const itemsPerPage = 10;
const totalItems = ref(0);
const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

interface PaymentStats {
  totalRevenue: number;
  revenueGrowth: number;
  totalTransactions: number;
  transactionsGrowth: number;
  ticketsSold: number;
  ticketsGrowth: number;
  totalRefunds: number;
  refundsGrowth: number;
}

interface Transaction {
  id: number;
  transaction_id?: string;
  transactionId: string;
  amount: number;
  status: 'completed' | 'pending' | 'failed' | 'refunded';
  payment_method?: string;
  paymentMethod: string;
  event_name?: string;
  customer_name?: string;
  customer_email?: string;
  created_at?: string;
  date: string;
  customer: {
    name: string;
    email: string;
    avatar?: string;
  };
  event: {
    id: number;
    title: string;
    date: string;
  };
}

const stats = ref<PaymentStats>({
  totalRevenue: 0,
  revenueGrowth: 0,
  totalTransactions: 0,
  transactionsGrowth: 0,
  ticketsSold: 0,
  ticketsGrowth: 0,
  totalRefunds: 0,
  refundsGrowth: 0
});

const transactions = ref<Transaction[]>([]);
const events = ref<EventItem[]>([]);

const fetchPaymentStats = async () => {
  try {
    const response = await httpClient.get<PaymentStats>(ENDPOINTS.PAYMENTS.STATS);
    stats.value = response;
  } catch (error) {
    console.error('Error fetching payment stats:', error);
    $toast.error('Failed to load payment statistics');
  }
};

const fetchTransactions = async () => {
  try {
    loading.value = true;
    const params = new URLSearchParams({
      page: currentPage.value.toString(),
      limit: itemsPerPage.toString(),
      search: searchQuery.value,
      status: statusFilter.value !== 'all' ? statusFilter.value : '',
      event: eventFilter.value !== 'all' ? eventFilter.value : '',
      date_filter: dateFilter.value !== 'all' ? dateFilter.value : ''
    });

    const response = await httpClient.get<{ data: Transaction[], total: number }>(
      `${ENDPOINTS.PAYMENTS.USER_TRANSACTIONS}?${params.toString()}`
    );

    transactions.value = response.data;
    totalItems.value = response.total;
  } catch (error) {
    console.error('Error fetching transactions:', error);
    $toast.error('Failed to load transactions');
  } finally {
    loading.value = false;
  }
};

const viewTransactionDetails = (transaction: Transaction) => {
  // Implement view transaction details functionality
  console.log('View transaction details:', transaction);
  $toast.info(`Viewing details for transaction: ${transaction.transactionId}`);
};

const exportTransactions = async () => {
  try {
    const response = await httpClient.get(`${ENDPOINTS.PAYMENTS.USER_TRANSACTIONS}/export`, {
      params: {
        search: searchQuery.value,
        status: statusFilter.value !== 'all' ? statusFilter.value : '',
        event: eventFilter.value !== 'all' ? eventFilter.value : '',
        date_filter: dateFilter.value !== 'all' ? dateFilter.value : ''
      },
      responseType: 'blob'
    }) as Blob;

    // Create download link
    const url = window.URL.createObjectURL(response);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', `transactions-${new Date().toISOString().split('T')[0]}.csv`);
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);

    $toast.success('Transactions exported successfully');
  } catch (error) {
    console.error('Error exporting transactions:', error);
    $toast.error('Failed to export transactions');
  }
};

const filteredTransactions = computed(() => {
  let result = [...transactions.value];

  // Apply search
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(t =>
      (t.transactionId || t.transaction_id || '').toLowerCase().includes(query) ||
      (t.customer?.name || t.customer_name || '').toLowerCase().includes(query) ||
      (t.customer?.email || t.customer_email || '').toLowerCase().includes(query) ||
      (t.event?.title || t.event_name || '').toLowerCase().includes(query)
    );
  }

  // Apply status filter
  if (statusFilter.value !== 'all') {
    result = result.filter(t => t.status === statusFilter.value);
  }

  // Apply event filter
  if (eventFilter.value !== 'all') {
    result = result.filter(t =>
      (t.event?.id || 0) === parseInt(eventFilter.value) ||
      t.event_name === events.value.find(e => e.id === parseInt(eventFilter.value))?.title
    );
  }

  // Apply date filter
  if (dateFilter.value !== 'all') {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    switch (dateFilter.value) {
      case 'today':
        result = result.filter(t => {
          const date = new Date(t.date || t.created_at || '');
          return date >= today;
        });
        break;
      case 'week':
        const weekStart = new Date(today);
        weekStart.setDate(today.getDate() - today.getDay());
        result = result.filter(t => {
          const date = new Date(t.date || t.created_at || '');
          return date >= weekStart;
        });
        break;
      case 'month':
        const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
        result = result.filter(t => {
          const date = new Date(t.date || t.created_at || '');
          return date >= monthStart;
        });
        break;
      case 'year':
        const yearStart = new Date(today.getFullYear(), 0, 1);
        result = result.filter(t => {
          const date = new Date(t.date || t.created_at || '');
          return date >= yearStart;
        });
        break;
    }
  }

  totalItems.value = result.length;

  // Apply pagination
  const start = (currentPage.value - 1) * itemsPerPage;
  const end = start + itemsPerPage;
  return result.slice(start, end);
});

const totalPages = computed(() => Math.ceil(totalItems.value / itemsPerPage));

const paginationStart = computed(() => {
  if (totalItems.value === 0) return 0;
  return (currentPage.value - 1) * itemsPerPage + 1;
});

const paginationEnd = computed(() => {
  return Math.min(currentPage.value * itemsPerPage, totalItems.value);
});

const displayedPages = computed(() => {
  const pages = [];
  const maxPagesToShow = 5;

  if (totalPages.value <= maxPagesToShow) {
    for (let i = 1; i <= totalPages.value; i++) {
      pages.push(i);
    }
  } else {
    const leftSide = Math.floor(maxPagesToShow / 2);
    const rightSide = maxPagesToShow - leftSide - 1;

    if (currentPage.value > leftSide && currentPage.value < totalPages.value - rightSide) {
      for (let i = currentPage.value - leftSide; i <= currentPage.value + rightSide; i++) {
        pages.push(i);
      }
    } else if (currentPage.value <= leftSide) {
      for (let i = 1; i <= maxPagesToShow; i++) {
        pages.push(i);
      }
    } else {
      for (let i = totalPages.value - maxPagesToShow + 1; i <= totalPages.value; i++) {
        pages.push(i);
      }
    }
  }

  return pages;
});

function formatCurrency(amount: number, currency = 'USD') {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency
  }).format(amount);
}

function formatDate(dateString: string | number | Date) {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
}

function formatStatus(status: string) {
  return status.charAt(0).toUpperCase() + status.slice(1);
}

function getPaymentMethodIcon(method: string) {
  switch (method.toLowerCase()) {
    case 'credit card':
      return 'heroicons:credit-card';
    case 'paypal':
      return 'mdi:paypal';
    case 'bank transfer':
      return 'heroicons:building-library';
    case 'mobile money':
      return 'heroicons:device-phone-mobile';
    default:
      return 'heroicons:banknotes';
  }
}

function prevPage() {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
}

function nextPage() {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
}

function goToPage(page: number) {
  currentPage.value = page;
}


const issueRefund = async (transaction: Transaction) => {
  try {
    await httpClient.post(`${ENDPOINTS.PAYMENTS.REFUND}/${transaction.id}`);
    $toast.success(`Refund initiated for transaction: ${transaction.transactionId}`);

    // Update transaction status locally
    const index = transactions.value.findIndex(t => t.id === transaction.id);
    if (index !== -1) {
      transactions.value[index].status = 'refunded';
    }

    // Refresh stats
    await fetchPaymentStats();
  } catch (error) {
    console.error('Error processing refund:', error);
    $toast.error('Failed to process refund');
  }
};

const downloadReceipt = async (transaction: Transaction) => {
  try {
    const response = await httpClient.get(`${ENDPOINTS.PAYMENTS.RECEIPT}/${transaction.id}`, {
      responseType: 'blob'
    }) as Blob;

    // Create download link
    const url = window.URL.createObjectURL(response);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', `receipt-${transaction.transactionId}.pdf`);
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);

    $toast.success(`Receipt for transaction ${transaction.transactionId} downloaded successfully`);
  } catch (error) {
    console.error('Error downloading receipt:', error);
    $toast.error('Failed to download receipt');
  }
};

watch([searchQuery, statusFilter, eventFilter, dateFilter], () => {
  currentPage.value = 1;
  fetchTransactions();
});

onMounted(async () => {
  await Promise.all([
    fetchPaymentStats(),
    fetchTransactions()
  ]);
});
</script>
