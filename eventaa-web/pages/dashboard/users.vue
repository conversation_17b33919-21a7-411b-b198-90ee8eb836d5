<template>
  <div class="p-6 bg-gray-100">
    <div class="mb-6 flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold">Users</h1>
        <p class="text-gray-500">Manage system users and their accounts</p>
      </div>
      <div class="flex space-x-2">
        <button @click="exportPDF" class="px-4 py-2 bg-white border border-gray-300 shadow-sm hover:bg-gray-50 flex items-center">
          <Icon icon="vscode-icons:file-type-pdf2" class="w-5 h-5 text-gray-600 mr-1" />
          Export PDF
        </button>
        <excel
          :data="exportData"
          :fields="exportFields"
          :name="`users-${dayjs().format('YYYY-MM-DD')}.xlsx`"
          :before-generate="beforeExport"
          :before-finish="onExportDone"
        >
          <button class="px-4 py-2 bg-green-600 text-green-100 border border-gray-300 shadow-sm flex items-center">
            <Icon icon="vscode-icons:file-type-excel" class="w-5 h-5 text-gray-600 mr-1" />
          Export Excel
          </button>
        </excel>
      </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <div class="bg-white p-4 shadow rounded">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-blue-100 mr-4">
            <Icon icon="heroicons:users" class="w-6 h-6 text-blue-600" />
          </div>
          <div>
            <p class="text-sm text-gray-500">Total Users</p>
            <p class="text-2xl font-bold">{{ stats.totalUsers }}</p>
            <div class="flex items-center text-sm">
              <span :class="stats.usersGrowth >= 0 ? 'text-green-600' : 'text-red-600'">
                <Icon :icon="stats.usersGrowth >= 0 ? 'heroicons:arrow-up' : 'heroicons:arrow-down'" class="w-4 h-4 inline" />
                {{ Math.abs(stats.usersGrowth) }}%
              </span>
              <span class="text-gray-500 ml-1">vs last month</span>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white p-4 shadow rounded">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-green-100 mr-4">
            <Icon icon="heroicons:user-plus" class="w-6 h-6 text-green-600" />
          </div>
          <div>
            <p class="text-sm text-gray-500">New Users</p>
            <p class="text-2xl font-bold">{{ stats.newUsers }}</p>
            <div class="flex items-center text-sm">
              <span :class="stats.newUsersGrowth >= 0 ? 'text-green-600' : 'text-red-600'">
                <Icon :icon="stats.newUsersGrowth >= 0 ? 'heroicons:arrow-up' : 'heroicons:arrow-down'" class="w-4 h-4 inline" />
                {{ Math.abs(stats.newUsersGrowth) }}%
              </span>
              <span class="text-gray-500 ml-1">vs last month</span>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white p-4 shadow rounded">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-purple-100 mr-4">
            <Icon icon="heroicons:building-storefront" class="w-6 h-6 text-purple-600" />
          </div>
          <div>
            <p class="text-sm text-gray-500">Vendors</p>
            <p class="text-2xl font-bold">{{ stats.vendors }}</p>
            <div class="flex items-center text-sm">
              <span :class="stats.vendorsGrowth >= 0 ? 'text-green-600' : 'text-red-600'">
                <Icon :icon="stats.vendorsGrowth >= 0 ? 'heroicons:arrow-up' : 'heroicons:arrow-down'" class="w-4 h-4 inline" />
                {{ Math.abs(stats.vendorsGrowth) }}%
              </span>
              <span class="text-gray-500 ml-1">vs last month</span>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white p-4 shadow rounded">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-red-100 mr-4">
            <Icon icon="heroicons:user-minus" class="w-6 h-6 text-red-600" />
          </div>
          <div>
            <p class="text-sm text-gray-500">Inactive Users</p>
            <p class="text-2xl font-bold">{{ stats.inactiveUsers }}</p>
            <div class="flex items-center text-sm">
              <span :class="stats.inactiveGrowth >= 0 ? 'text-green-600' : 'text-red-600'">
                <Icon :icon="stats.inactiveGrowth >= 0 ? 'heroicons:arrow-up' : 'heroicons:arrow-down'" class="w-4 h-4 inline" />
                {{ Math.abs(stats.inactiveGrowth) }}%
              </span>
              <span class="text-gray-500 ml-1">vs last month</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="bg-white p-4 shadow mb-6">
      <div class="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-4">
        <div class="flex-1">
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Icon icon="heroicons:magnifying-glass" class="h-5 w-5 text-gray-400" />
            </div>
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search users by name, email, or phone"
              class="block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:border-red-500 focus:ring-red-500"
            />
          </div>
        </div>

        <!-- Role Filter -->
        <Listbox v-model="roleFilter" as="div" class="relative">
          <ListboxButton class="relative w-48 cursor-pointer  bg-white py-2 pl-3 pr-10 text-left border border-gray-300 focus:outline-none focus:ring-2 focus:ring-red-500 sm:text-sm">
            <span class="block truncate">{{ formatRole(roleFilter) }}</span>
            <span class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
              <Icon icon="heroicons:chevron-up-down" class="h-5 w-5 text-gray-400" aria-hidden="true" />
            </span>
          </ListboxButton>
          <transition
            leave-active-class="transition duration-100 ease-in"
            leave-from-class="opacity-100"
            leave-to-class="opacity-0"
          >
            <ListboxOptions class="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
              <ListboxOption v-for="role in roles" :key="role.value" :value="role.value" v-slot="{ active, selected }">
                <div :class="[
                  active ? 'bg-red-600 text-white' : 'text-gray-900',
                  'relative cursor-pointer select-none py-2 pl-3 pr-9'
                ]">
                  <span :class="[selected ? 'font-semibold' : 'font-normal', 'block truncate']">
                    {{ role.label }}
                  </span>
                  <span v-if="selected" :class="[
                    active ? 'text-white' : 'text-red-600',
                    'absolute inset-y-0 right-0 flex items-center pr-4'
                  ]">
                    <Icon icon="heroicons:check" class="h-5 w-5" aria-hidden="true" />
                  </span>
                </div>
              </ListboxOption>
            </ListboxOptions>
          </transition>
        </Listbox>

        <!-- Status Filter -->
        <Listbox v-model="statusFilter" as="div" class="relative">
          <ListboxButton class="relative w-48 cursor-pointer  bg-white py-2 pl-3 pr-10 text-left border border-gray-300 focus:outline-none focus:ring-2 focus:ring-red-500 sm:text-sm">
            <span class="block truncate">{{ formatStatus(statusFilter) }}</span>
            <span class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
              <Icon icon="heroicons:chevron-up-down" class="h-5 w-5 text-gray-400" aria-hidden="true" />
            </span>
          </ListboxButton>
          <transition
            leave-active-class="transition duration-100 ease-in"
            leave-from-class="opacity-100"
            leave-to-class="opacity-0"
          >
            <ListboxOptions class="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
              <ListboxOption v-for="status in statuses" :key="status.value" :value="status.value" v-slot="{ active, selected }">
                <div :class="[
                  active ? 'bg-red-600 text-white' : 'text-gray-900',
                  'relative cursor-pointer select-none py-2 pl-3 pr-9'
                ]">
                  <span :class="[selected ? 'font-semibold' : 'font-normal', 'block truncate']">
                    {{ status.label }}
                  </span>
                  <span v-if="selected" :class="[
                    active ? 'text-white' : 'text-red-600',
                    'absolute inset-y-0 right-0 flex items-center pr-4'
                  ]">
                    <Icon icon="heroicons:check" class="h-5 w-5" aria-hidden="true" />
                  </span>
                </div>
              </ListboxOption>
            </ListboxOptions>
          </transition>
        </Listbox>

        <!-- Date Filter -->
        <Listbox v-model="dateFilter" as="div" class="relative">
          <ListboxButton class="relative w-48 cursor-pointer  bg-white py-2 pl-3 pr-10 text-left border border-gray-300 focus:outline-none focus:ring-2 focus:ring-red-500 sm:text-sm">
            <span class="block truncate">{{ formatDateFilter(dateFilter) }}</span>
            <span class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
              <Icon icon="heroicons:chevron-up-down" class="h-5 w-5 text-gray-400" aria-hidden="true" />
            </span>
          </ListboxButton>
          <transition
            leave-active-class="transition duration-100 ease-in"
            leave-from-class="opacity-100"
            leave-to-class="opacity-0"
          >
            <ListboxOptions class="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
              <ListboxOption v-for="date in dates" :key="date.value" :value="date.value" v-slot="{ active, selected }">
                <div :class="[
                  active ? 'bg-red-600 text-white' : 'text-gray-900',
                  'relative cursor-pointer select-none py-2 pl-3 pr-9'
                ]">
                  <span :class="[selected ? 'font-semibold' : 'font-normal', 'block truncate']">
                    {{ date.label }}
                  </span>
                  <span v-if="selected" :class="[
                    active ? 'text-white' : 'text-red-600',
                    'absolute inset-y-0 right-0 flex items-center pr-4'
                  ]">
                    <Icon icon="heroicons:check" class="h-5 w-5" aria-hidden="true" />
                  </span>
                </div>
              </ListboxOption>
            </ListboxOptions>
          </transition>
        </Listbox>
      </div>
    </div>

    <div class="bg-white shadow">
      <div v-if="loading" class="flex justify-center items-center py-20">
        <CoreLoader />
      </div>
      <div v-else>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead>
              <tr>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Joined</th>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Login</th>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="user in filteredUsers" :key="user.id" class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10">
                      <img class="h-10 w-10 rounded-full" :src="`${runtimeConfig.public.baseUrl}storage/avatars/${user.avatar}`" alt="" />
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium text-gray-900">{{ user.name }}</div>
                      <div class="text-sm text-gray-500">ID: {{ user.id }}</div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ user.email }}</div>
                  <div class="text-sm text-gray-500">{{ user.phone || 'No phone' }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div
                      class="w-2 h-2 rounded-full mr-2"
                      :class="{
                        'bg-red-500': user.role === 'admin',
                        'bg-purple-500': user.role === 'vendor',
                        'bg-blue-500': user.role === 'customer',
                        'bg-green-500': user.role === 'staff'
                      }"
                    ></div>
                    <span class="text-sm text-gray-900">{{ formatRole(user.role) }}</span>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ formatDate(user.joinedDate) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ user.lastLogin ? formatDate(user.lastLogin) : 'Never' }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                    :class="{
                      'bg-green-100 text-green-800': user.status === 'active',
                      'bg-gray-100 text-gray-800': user.status === 'inactive',
                      'bg-red-100 text-red-800': user.status === 'suspended',
                      'bg-yellow-100 text-yellow-800': user.status === 'pending'
                    }">
                    {{ formatStatus(user.status) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div class="flex space-x-2">
                    <button @click="openViewModal(user)" class="text-gray-600 hover:text-gray-900">
                      <Icon icon="heroicons:eye" class="w-5 h-5" />
                    </button>
                    <button
                      v-if="user.status === 'active'"
                      @click="openSuspendModal(user)"
                      class="text-yellow-600 hover:text-yellow-900"
                    >
                      <Icon icon="heroicons:pause" class="w-5 h-5" />
                    </button>
                    <button
                      v-if="user.status === 'suspended' || user.status === 'inactive'"
                      @click="openActivateModal(user)"
                      class="text-green-600 hover:text-green-900"
                    >
                      <Icon icon="heroicons:play" class="w-5 h-5" />
                    </button>
                    <button @click="openDeleteModal(user)" class="text-red-600 hover:text-red-900">
                      <Icon icon="heroicons:trash" class="w-5 h-5" />
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
          <div class="flex-1 flex justify-between sm:hidden">
            <button @click="prevPage" :disabled="currentPage === 1" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
              Previous
            </button>
            <button @click="nextPage" :disabled="currentPage === totalPages" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
              Next
            </button>
          </div>
          <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p class="text-sm text-gray-700">
                Showing <span class="font-medium">{{ paginationStart }}</span> to <span class="font-medium">{{ paginationEnd }}</span> of <span class="font-medium">{{ totalItems }}</span> users
              </p>
            </div>
            <div>
              <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button @click="prevPage" :disabled="currentPage === 1" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                  <span class="sr-only">Previous</span>
                  <Icon icon="heroicons:chevron-left" class="h-5 w-5" />
                </button>
                <button v-for="page in displayedPages" :key="page" @click="goToPage(page)" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium" :class="page === currentPage ? 'z-10 bg-red-50 border-red-500 text-red-600' : 'text-gray-500 hover:bg-gray-50'">
                  {{ page }}
                </button>
                <button @click="nextPage" :disabled="currentPage === totalPages" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                  <span class="sr-only">Next</span>
                  <Icon icon="heroicons:chevron-right" class="h-5 w-5" />
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- View User Modal -->
    <TransitionRoot appear :show="isViewModalOpen" as="template">
      <Dialog as="div" @close="closeViewModal" class="relative z-10">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-25" />
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div class="flex min-h-full items-center justify-center p-4 text-center">
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel class="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                <DialogTitle as="h3" class="text-lg font-medium leading-6 text-gray-900">
                  User Details
                </DialogTitle>
                <div class="mt-4">
                  <div v-if="selectedUser" class="space-y-4">
                    <div class="flex items-center">
                      <img :src="`${runtimeConfig.public.baseUrl}storage/avatars/${selectedUser.avatar}`" class="w-16 h-16 rounded-full" :alt="selectedUser.name">
                      <div class="ml-4">
                        <h4 class="text-lg font-medium">{{ selectedUser.name }}</h4>
                        <p class="text-gray-500">{{ selectedUser.email }}</p>
                      </div>
                    </div>
                    <div class="grid grid-cols-2 gap-4">
                      <div>
                        <p class="text-sm text-gray-500">Role</p>
                        <p class="font-medium">{{ formatRole(selectedUser.role) }}</p>
                      </div>
                      <div>
                        <p class="text-sm text-gray-500">Status</p>
                        <p class="font-medium">{{ formatStatus(selectedUser.status) }}</p>
                      </div>
                      <div>
                        <p class="text-sm text-gray-500">Joined Date</p>
                        <p class="font-medium">{{ formatDate(selectedUser.joinedDate) }}</p>
                      </div>
                      <div>
                        <p class="text-sm text-gray-500">Last Login</p>
                        <p class="font-medium">{{ selectedUser.lastLogin ? formatDate(selectedUser.lastLogin) : 'Never' }}</p>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="mt-6">
                  <button
                    type="button"
                    class="inline-flex justify-center rounded-md border border-transparent bg-red-100 px-4 py-2 text-sm font-medium text-red-900 hover:bg-red-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-red-500 focus-visible:ring-offset-2"
                    @click="closeViewModal"
                  >
                    Close
                  </button>
                </div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>

    <!-- Delete Confirmation Modal -->
    <TransitionRoot appear :show="isDeleteModalOpen" as="template">
      <Dialog as="div" @close="closeDeleteModal" class="relative z-10">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-25" />
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div class="flex min-h-full items-center justify-center p-4 text-center">
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel class="w-full max-w-md transform overflow-hidden bg-white p-6 text-left align-middle shadow-xl transition-all">
                <DialogTitle as="h3" class="text-lg font-medium leading-6 text-gray-900">
                  Delete User
                </DialogTitle>
                <div class="mt-2">
                  <p class="text-sm text-gray-500">
                    Are you sure you want to delete this user? This action cannot be undone.
                  </p>
                </div>
                <div class="mt-6 flex space-x-3">
                  <CoreSubmitButton
                    type="button"
                    :loading="isDeleting"
                    @click="confirmDelete"
                  >
                    Delete
                  </CoreSubmitButton>
                  <button
                    type="button"
                    class="inline-flex justify-center border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus-visible:ring-2 focus-visible:ring-red-500 focus-visible:ring-offset-2"
                    @click="closeDeleteModal"
                  >
                    Cancel
                  </button>
                </div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>

    <!-- Status Change Modal -->
    <TransitionRoot appear :show="isStatusModalOpen" as="template">
      <Dialog as="div" @close="closeStatusModal" class="relative z-10">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-25" />
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div class="flex min-h-full items-center justify-center p-4 text-center">
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel class="w-full max-w-md transform overflow-hidden bg-white p-6 text-left align-middle shadow-xl transition-all">
                <DialogTitle as="h3" class="text-lg font-medium leading-6 text-gray-900">
                  {{ statusChangeAction === 'suspend' ? 'Suspend User' : 'Activate User' }}
                </DialogTitle>
                <div class="mt-2">
                  <p class="text-sm text-gray-500">
                    Are you sure you want to {{ statusChangeAction === 'suspend' ? 'suspend' : 'activate' }} this user?
                  </p>
                </div>
                <div class="mt-6 flex space-x-3">
                  <CoreSubmitButton
                    type="button"
                    :loading="isStatusChanging"
                    @click="confirmStatusChange"
                  >
                    {{ statusChangeAction === 'suspend' ? 'Suspend' : 'Activate' }}
                  </CoreSubmitButton>
                  <button
                    type="button"
                    class="inline-flex justify-center border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus-visible:ring-2 focus-visible:ring-red-500 focus-visible:ring-offset-2"
                    @click="closeStatusModal"
                  >
                    Cancel
                  </button>
                </div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import {
  Dialog,
  DialogPanel,
  DialogTitle,
  TransitionChild,
  TransitionRoot,
  Listbox,
  ListboxButton,
  ListboxOptions,
  ListboxOption,
} from '@headlessui/vue';
import dayjs from 'dayjs';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';

definePageMeta({
  layout: "dashboard",
});

const loading = ref(true);
const searchQuery = ref('');
const roleFilter = ref('all');
const statusFilter = ref('all');
const dateFilter = ref('all');
const currentPage = ref(1);
const itemsPerPage = 10;
const totalItems = ref(0);
const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

interface UserStats {
  totalUsers: number;
  usersGrowth: number;
  newUsers: number;
  newUsersGrowth: number;
  vendors: number;
  vendorsGrowth: number;
  inactiveUsers: number;
  inactiveGrowth: number;
}

interface User {
  id: string;
  name: string;
  email: string;
  phone?: string;
  avatar?: string;
  role: 'admin' | 'vendor' | 'customer' | 'staff';
  joinedDate: string;
  lastLogin: string | null;
  status: 'active' | 'inactive' | 'suspended' | 'pending';
}

interface ApiResponse<T> {
  data: T;
  message?: string;
  status: number;
}

interface UsersResponse {
  users: User[];
  total: number;
}

const stats = ref<UserStats>({
  totalUsers: 0,
  usersGrowth: 0,
  newUsers: 0,
  newUsersGrowth: 0,
  vendors: 0,
  vendorsGrowth: 0,
  inactiveUsers: 0,
  inactiveGrowth: 0
});

const users = ref<User[]>([]);
const runtimeConfig = useRuntimeConfig();

async function fetchStats() {
  try {
    const response = await httpClient.get<ApiResponse<UserStats>>(ENDPOINTS.USERS.STATS);
    stats.value = response.data;
  } catch (error) {
    console.error('Error fetching user stats:', error);
    $toast.error('Failed to load user statistics');
  }
}

async function fetchUsers() {
  try {
    loading.value = true;
    const params = new URLSearchParams({
      page: currentPage.value.toString(),
      limit: itemsPerPage.toString(),
      search: searchQuery.value,
      role: roleFilter.value !== 'all' ? roleFilter.value : '',
      status: statusFilter.value !== 'all' ? statusFilter.value : '',
      dateFilter: dateFilter.value !== 'all' ? dateFilter.value : ''
    });

    const response = await httpClient.get<ApiResponse<UsersResponse>>(`${ENDPOINTS.USERS.GET}?${params.toString()}`);
    users.value = response.data.users;
    totalItems.value = response.data.total;
  } catch (error) {
    console.error('Error fetching users:', error);
    $toast.error('Failed to load users');
  } finally {
    loading.value = false;
  }
}

async function updateUserStatus(userId: string, status: string) {
  try {
    isStatusChanging.value = true;
    await httpClient.put(`${ENDPOINTS.USERS}/${userId}/status`, { status });
    await fetchUsers();
    $toast.success(`User status updated successfully`);
  } catch (error) {
    console.error('Error updating user status:', error);
    $toast.error('Failed to update user status');
  } finally {
    isStatusChanging.value = false;
  }
}

async function deleteUser(user: User) {
  try {
    await httpClient.delete(`${ENDPOINTS.USERS.DELETE}/${user.id}`);
    await fetchUsers();
    $toast.success(`User ${user.name} has been deleted`);
  } catch (error) {
    console.error('Error deleting user:', error);
    $toast.error('Failed to delete user');
  }
}

function suspendUser(user: User): void {
  updateUserStatus(user.id, 'suspended');
}

function activateUser(user: User): void {
  updateUserStatus(user.id, 'active');
}

watch([searchQuery, roleFilter, statusFilter, dateFilter], () => {
  currentPage.value = 1;
  fetchUsers();
});

onMounted(async () => {
  await Promise.all([fetchStats(), fetchUsers()]);
});

const filteredUsers = computed<User[]>(() => {
  let result = [...users.value];

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(u =>
      u.name.toLowerCase().includes(query) ||
      u.email.toLowerCase().includes(query) ||
      (u.phone && u.phone.toLowerCase().includes(query)) ||
      u.id.toLowerCase().includes(query)
    );
  }

  if (roleFilter.value !== 'all') {
    result = result.filter(u => u.role === roleFilter.value);
  }

  if (statusFilter.value !== 'all') {
    result = result.filter(u => u.status === statusFilter.value);
  }

  if (dateFilter.value !== 'all') {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    switch (dateFilter.value) {
      case 'today':
        result = result.filter(u => {
          const joinedDate = new Date(u.joinedDate);
          return joinedDate.getDate() === today.getDate() &&
                 joinedDate.getMonth() === today.getMonth() &&
                 joinedDate.getFullYear() === today.getFullYear();
        });
        break;
      case 'week':
        const weekStart = new Date(today);
        weekStart.setDate(today.getDate() - today.getDay());
        result = result.filter(u => new Date(u.joinedDate) >= weekStart);
        break;
      case 'month':
        const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
        result = result.filter(u => new Date(u.joinedDate) >= monthStart);
        break;
      case 'year':
        const yearStart = new Date(today.getFullYear(), 0, 1);
        result = result.filter(u => new Date(u.joinedDate) >= yearStart);
        break;
    }
  }

  result.sort((a, b) => new Date(b.joinedDate).getTime() - new Date(a.joinedDate).getTime());

  totalItems.value = result.length;

  const start = (currentPage.value - 1) * itemsPerPage;
  const end = start + itemsPerPage;
  return result.slice(start, end);
});

const totalPages = computed(() => Math.ceil(totalItems.value / itemsPerPage));

const paginationStart = computed(() => {
  if (totalItems.value === 0) return 0;
  return (currentPage.value - 1) * itemsPerPage + 1;
});

const paginationEnd = computed(() => {
  return Math.min(currentPage.value * itemsPerPage, totalItems.value);
});

const displayedPages = computed(() => {
  const pages = [];
  const maxPagesToShow = 5;

  if (totalPages.value <= maxPagesToShow) {
    for (let i = 1; i <= totalPages.value; i++) {
      pages.push(i);
    }
  } else {
    const leftSide = Math.floor(maxPagesToShow / 2);
    const rightSide = maxPagesToShow - leftSide - 1;

    if (currentPage.value > leftSide && currentPage.value < totalPages.value - rightSide) {
      for (let i = currentPage.value - leftSide; i <= currentPage.value + rightSide; i++) {
        pages.push(i);
      }
    } else if (currentPage.value <= leftSide) {
      for (let i = 1; i <= maxPagesToShow; i++) {
        pages.push(i);
      }
    } else {
      for (let i = totalPages.value - maxPagesToShow + 1; i <= totalPages.value; i++) {
        pages.push(i);
      }
    }
  }

  return pages;
});

function formatDate(dateString: string): string {
  if (!dateString) return '';
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
}

function formatRole(role: string): string {
  return role.charAt(0).toUpperCase() + role.slice(1);
}

function formatStatus(status: string): string {
  return status.charAt(0).toUpperCase() + status.slice(1);
}

function prevPage() {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
}

function nextPage() {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
}

function goToPage(page: number): void {
  currentPage.value = page;
}

// Modal states
const isViewModalOpen = ref(false);
const isDeleteModalOpen = ref(false);
const isStatusModalOpen = ref(false);
const selectedUser = ref<User | null>(null);
const statusChangeAction = ref<'suspend' | 'activate'>('suspend');
const isDeleting = ref(false);
const isStatusChanging = ref(false);

const roles = [
  { value: 'all', label: 'All Roles' },
  { value: 'admin', label: 'Admin' },
  { value: 'vendor', label: 'Vendor' },
  { value: 'user', label: 'User' },
];

const statuses = [
  { value: 'all', label: 'All Status' },
  { value: 'active', label: 'Active' },
  { value: 'inactive', label: 'Inactive' },
  { value: 'suspended', label: 'Suspended' },
  { value: 'pending', label: 'Pending' }
];

const dates = [
  { value: 'all', label: 'All Time' },
  { value: 'today', label: 'Today' },
  { value: 'week', label: 'This Week' },
  { value: 'month', label: 'This Month' },
  { value: 'year', label: 'This Year' }
];

// Modal functions
function openViewModal(user: User) {
  selectedUser.value = user;
  isViewModalOpen.value = true;
}

function closeViewModal() {
  isViewModalOpen.value = false;
  selectedUser.value = null;
}

function openDeleteModal(user: User) {
  selectedUser.value = user;
  isDeleteModalOpen.value = true;
}

function closeDeleteModal() {
  isDeleteModalOpen.value = false;
  selectedUser.value = null;
}

function openSuspendModal(user: User) {
  selectedUser.value = user;
  statusChangeAction.value = 'suspend';
  isStatusModalOpen.value = true;
}

function openActivateModal(user: User) {
  selectedUser.value = user;
  statusChangeAction.value = 'activate';
  isStatusModalOpen.value = true;
}

function closeStatusModal() {
  isStatusModalOpen.value = false;
  selectedUser.value = null;
}

async function confirmDelete() {
  if (selectedUser.value) {
    try {
      isDeleting.value = true;
      await deleteUser(selectedUser.value);
      closeDeleteModal();
    } catch (error) {
      console.error('Error deleting user:', error);
    } finally {
      isDeleting.value = false;
    }
  }
}

async function confirmStatusChange() {
  if (selectedUser.value) {
    try {
      isStatusChanging.value = true;
      if (statusChangeAction.value === 'suspend') {
        await suspendUser(selectedUser.value);
      } else {
        await activateUser(selectedUser.value);
      }
      closeStatusModal();
    } catch (error) {
      console.error('Error changing user status:', error);
    } finally {
      isStatusChanging.value = false;
    }
  }
}

function formatDateFilter(filter: string): string {
  const filterMap: { [key: string]: string } = {
    'all': 'All Time',
    'today': 'Today',
    'week': 'This Week',
    'month': 'This Month',
    'year': 'This Year'
  };
  return filterMap[filter] || filter;
}

const exportFields = computed(() => ({
  'ID': 'id',
  'Name': 'name',
  'Email': 'email',
  'Phone': 'phone',
  'Role': 'role',
  'Status': 'status',
  'Joined Date': 'joinedDate',
  'Last Login': 'lastLogin'
}));

const exportData = computed(() => {
  return users.value.map(user => ({
    id: user.id,
    name: user.name,
    email: user.email,
    phone: user.phone || 'N/A',
    role: formatRole(user.role),
    status: formatStatus(user.status),
    joinedDate: formatDate(user.joinedDate),
    lastLogin: user.lastLogin ? formatDate(user.lastLogin) : 'Never'
  }));
});

const beforeExport = (): void => {
  $toast.info('Preparing Excel export...');
};

const onExportDone = (): void => {
  $toast.success('Excel file exported successfully');
};

function exportPDF(): void {
  try {
    $toast.info('Generating PDF...');

    const doc = new jsPDF();
    const tableColumn = ["ID", "Name", "Email", "Phone", "Role", "Status", "Joined Date", "Last Login"];
    const tableRows: any[] = [];

    users.value.forEach(user => {
      const userData = [
        user.id,
        user.name,
        user.email,
        user.phone || 'N/A',
        formatRole(user.role),
        formatStatus(user.status),
        formatDate(user.joinedDate),
        user.lastLogin ? formatDate(user.lastLogin) : 'Never'
      ];
      tableRows.push(userData);
    });

    doc.setFontSize(22);
    doc.setTextColor(220, 53, 69);
    doc.text("EventaHub", 14, 15);

    doc.setFontSize(15);
    doc.setTextColor(0, 0, 0);
    doc.text("Users Report", 14, 25);

    doc.setFontSize(11);
    doc.setTextColor(100, 100, 100);
    doc.text(`Generated on: ${dayjs().format('MMMM D, YYYY')} at ${dayjs().format('h:mm A')}`, 14, 32);
    doc.text(`Total Users: ${users.value.length}`, 14, 38);

    if (roleFilter.value !== 'all') {
      doc.text(`Role Filter: ${formatRole(roleFilter.value)}`, 14, 44);
    }
    if (statusFilter.value !== 'all') {
      doc.text(`Status Filter: ${formatStatus(statusFilter.value)}`, 14, 50);
    }

    autoTable(doc, {
      head: [tableColumn],
      body: tableRows,
      startY: 55,
      theme: 'grid',
      styles: {
        fontSize: 10,
        cellPadding: 3,
        overflow: 'linebreak',
      },
      headStyles: {
        fillColor: [220, 53, 69],
        textColor: 255,
        fontStyle: 'bold',
      },
      alternateRowStyles: {
        fillColor: [245, 245, 245]
      }
    });

    const pageCount = (doc as any).internal.getNumberOfPages();
    for (let i = 1; i <= pageCount; i++) {
      doc.setPage(i);
      doc.setFontSize(10);
      doc.setTextColor(150, 150, 150);
      const pageSize = (doc as any).internal.pageSize;
      const pageWidth = pageSize.width ? pageSize.width : pageSize.getWidth();
      const pageHeight = pageSize.height ? pageSize.height : pageSize.getHeight();
      doc.text(`Page ${i} of ${pageCount}`, pageWidth / 2, pageHeight - 10, { align: 'center' });
      doc.text('EventaHub User Management', 14, pageHeight - 10);
      doc.text(`${dayjs().format('YYYY-MM-DD')}`, pageWidth - 20, pageHeight - 10);
    }

    doc.save(`users-${dayjs().format('YYYY-MM-DD')}.pdf`);
    $toast.success('PDF exported successfully');
  } catch (error: any) {
    console.error('Error exporting PDF:', error);
    $toast.error('Failed to export PDF');
  }
}
</script>
