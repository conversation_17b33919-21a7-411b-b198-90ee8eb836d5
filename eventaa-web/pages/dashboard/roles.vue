<template>
  <div>
    <div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
      <div>
        <h1 class="text-2xl font-semibold">User Roles</h1>
        <p class="text-gray-500 dark:text-gray-400">Manage user roles and their permissions</p>
      </div>
      <div class="mt-4 md:mt-0">
        <button class="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 flex items-center">
          <Icon icon="heroicons:plus" class="h-5 w-5 mr-2" />
          Create New Role
        </button>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Roles List -->
      <div class="lg:col-span-1">
        <div class="bg-white dark:bg-gray-800 shadow rounded-md p-4">
          <h2 class="text-lg font-semibold mb-4">Roles</h2>

          <div v-if="loading" class="py-8">
            <div class="animate-pulse space-y-4">
              <div class="h-10 bg-gray-200 dark:bg-gray-700 rounded"></div>
              <div class="h-10 bg-gray-200 dark:bg-gray-700 rounded"></div>
              <div class="h-10 bg-gray-200 dark:bg-gray-700 rounded"></div>
              <div class="h-10 bg-gray-200 dark:bg-gray-700 rounded"></div>
            </div>
          </div>

          <div v-else-if="roles.length === 0" class="py-8 text-center text-gray-500 dark:text-gray-400">
            No roles found
          </div>

          <div v-else class="space-y-2">
            <div
              v-for="role in roles"
              :key="role.id"
              class="p-3 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
              :class="{ 'bg-gray-50 dark:bg-gray-700': selectedRole?.id === role.id }"
              @click="selectRole(role)"
            >
              <div class="flex justify-between items-center">
                <div>
                  <h3 class="font-medium text-gray-900 dark:text-white">{{ role.display_name }}</h3>
                  <p class="text-xs text-gray-500 dark:text-gray-400">{{ role.name }}</p>
                </div>
                <div class="flex space-x-2">
                  <button
                    class="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-600"
                    @click.stop="editRole(role)"
                  >
                    <Icon icon="heroicons:pencil-square" class="w-4 h-4 text-gray-500 dark:text-gray-400" />
                  </button>
                  <button
                    class="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-600"
                    @click.stop="deleteRole(role)"
                    :disabled="role.name === 'administrator'"
                  >
                    <Icon
                      icon="heroicons:trash"
                      class="w-4 h-4 text-red-500"
                      :class="{ 'opacity-50 cursor-not-allowed': role.name === 'administrator' }"
                    />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Role Details -->
      <div class="lg:col-span-2">
        <div class="bg-white dark:bg-gray-800 shadow rounded-md p-6">
          <div v-if="!selectedRole" class="py-12 text-center">
            <Icon icon="heroicons:user-group" class="w-16 h-16 mx-auto text-gray-300 dark:text-gray-600" />
            <h3 class="mt-4 text-lg font-medium text-gray-900 dark:text-white">Select a role to view details</h3>
            <p class="text-gray-500 dark:text-gray-400">Or create a new role to get started</p>
          </div>

          <div v-else>
            <div class="flex justify-between items-center mb-6">
              <div>
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white">{{ selectedRole.display_name }}</h2>
                <p class="text-gray-500 dark:text-gray-400">{{ selectedRole.description }}</p>
              </div>
              <div>
                <button
                  class="px-3 py-1 text-sm bg-primary-600 text-white rounded-md hover:bg-primary-700"
                  @click="showPermissionsDialog = true"
                >
                  Manage Permissions
                </button>
              </div>
            </div>

            <div class="mb-6">
              <h3 class="text-md font-medium text-gray-900 dark:text-white mb-2">Permissions</h3>
              <div v-if="selectedRole.permissions && selectedRole.permissions.length > 0" class="grid grid-cols-1 md:grid-cols-2 gap-2">
                <div
                  v-for="permission in selectedRole.permissions"
                  :key="permission.id"
                  class="flex items-center p-2 rounded-md bg-gray-50 dark:bg-gray-700"
                >
                  <Icon icon="heroicons:check-circle" class="w-5 h-5 text-green-500 mr-2" />
                  <div>
                    <p class="text-sm font-medium text-gray-900 dark:text-white">{{ permission.display_name }}</p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">{{ permission.name }}</p>
                  </div>
                </div>
              </div>
              <div v-else class="py-4 text-center text-gray-500 dark:text-gray-400">
                No permissions assigned to this role
              </div>
            </div>

            <div>
              <h3 class="text-md font-medium text-gray-900 dark:text-white mb-2">Users with this role</h3>
              <div v-if="selectedRole.users && selectedRole.users.length > 0" class="grid grid-cols-1 md:grid-cols-2 gap-2">
                <div
                  v-for="user in selectedRole.users"
                  :key="user.id"
                  class="flex items-center p-2 rounded-md bg-gray-50 dark:bg-gray-700"
                >
                  <img
                    :src="user.avatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(user.name)}`"
                    class="w-8 h-8 rounded-full mr-2"
                    :alt="user.name"
                  />
                  <div>
                    <p class="text-sm font-medium text-gray-900 dark:text-white">{{ user.name }}</p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">{{ user.email }}</p>
                  </div>
                </div>
              </div>
              <div v-else class="py-4 text-center text-gray-500 dark:text-gray-400">
                No users assigned to this role
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Permissions Dialog -->
    <div v-if="showPermissionsDialog" class="fixed inset-0 z-50 overflow-y-auto">
      <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
          <div class="absolute inset-0 bg-gray-500 dark:bg-gray-900 opacity-75"></div>
        </div>

        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

        <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-3xl sm:w-full">
          <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="sm:flex sm:items-start">
              <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white" id="modal-title">
                  Permissions for {{ selectedRole?.display_name }}
                </h3>
                <div class="mt-4">
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div v-for="(permissionGroup, groupName) in permissionGroups" :key="groupName" class="border border-gray-200 dark:border-gray-700 rounded-md p-4">
                      <h4 class="font-medium text-gray-900 dark:text-white mb-3">{{ groupName }}</h4>
                      <div class="space-y-2">
                        <div v-for="permission in permissionGroup" :key="permission.id" class="flex items-center">
                          <input
                            type="checkbox"
                            :id="permission.id"
                            :checked="permission.granted"
                            class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 rounded"
                          />
                          <label :for="permission.id" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                            {{ permission.name }}
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="bg-gray-50 dark:bg-gray-900 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button type="button" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm">
              Save Changes
            </button>
            <button type="button" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm" @click="showPermissionsDialog = false">
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { ENDPOINTS } from '~/utils/api';

definePageMeta({
  layout: "dashboard",
});

interface Permission {
  id: number;
  name: string;
  display_name: string;
  description?: string;
}

interface User {
  id: number;
  name: string;
  email: string;
  avatar?: string;
}

interface Role {
  id: number;
  name: string;
  display_name: string;
  description: string;
  permissions: Permission[];
  users: User[];
}

interface PermissionGroup {
  [key: string]: {
    id: string;
    name: string;
    granted: boolean;
  }[];
}

interface ApiResponse<T> {
  data: T;
  message?: string;
  status: number;
}

const loading = ref(true);
const roles = ref<Role[]>([]);
const selectedRole = ref<Role | null>(null);
const showPermissionsDialog = ref(false);
const permissionGroups = ref<PermissionGroup>({});

const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

async function fetchRoles() {
  try {
    loading.value = true;
    const response = await httpClient.get<ApiResponse<Role[]>>(ENDPOINTS.ROLES.GET);
    roles.value = response.data;
  } catch (error) {
    console.error('Error fetching roles:', error);
    $toast.error('Failed to load roles');
  } finally {
    loading.value = false;
  }
}

async function fetchPermissionGroups() {
  try {
    const response = await httpClient.get<ApiResponse<PermissionGroup>>(ENDPOINTS.PERMISSIONS.CATEGORIES);
    permissionGroups.value = response.data;
  } catch (error) {
    console.error('Error fetching permission groups:', error);
    $toast.error('Failed to load permission groups');
  }
}

onMounted(async () => {
  await Promise.all([fetchRoles(), fetchPermissionGroups()]);
});

function selectRole(role: Role) {
  selectedRole.value = role;
}

async function editRole(role: Role) {
  try {
    // Implementation would open an edit modal/form
    $toast.info(`Edit role: ${role.name}`);
  } catch (error) {
    console.error('Error editing role:', error);
    $toast.error('Failed to edit role');
  }
}

async function deleteRole(role: Role) {
  try {
    if (role.name === 'administrator') {
      $toast.error('Cannot delete the Administrator role');
      return;
    }

    if (confirm(`Are you sure you want to delete the role "${role.display_name}"?`)) {
      await httpClient.delete(`${ENDPOINTS.ROLES.DESTROY}/${role.id}`);
      await fetchRoles();
      if (selectedRole.value?.id === role.id) {
        selectedRole.value = null;
      }
      $toast.success(`Role "${role.display_name}" deleted successfully`);
    }
  } catch (error) {
    console.error('Error deleting role:', error);
    $toast.error('Failed to delete role');
  }
}
</script>
