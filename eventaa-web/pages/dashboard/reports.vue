<template>
  <div class="p-6 bg-gray-100">
    <div class="mb-6 flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold">Reports</h1>
        <p class="text-gray-500">Generate and view financial and event reports</p>
      </div>
      <div class="flex space-x-2">
        <button @click="exportReports" class="px-4 py-2 bg-white border border-gray-300 rounded shadow-sm hover:bg-gray-50 flex items-center">
          <Icon icon="heroicons:arrow-down-tray" class="w-5 h-5 text-gray-600 mr-1" />
          Export
        </button>
        <button @click="newReport" class="px-4 py-2 bg-red-600 text-white rounded shadow-sm hover:bg-red-700 flex items-center">
          <Icon icon="heroicons:plus" class="w-5 h-5 mr-1" />
          New Report
        </button>
      </div>
    </div>

    <!-- Report Filters -->
    <div class="bg-white p-4 shadow mb-6">
      <div class="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-4">
        <div>
          <label for="report-type" class="block text-sm font-medium text-gray-700 mb-1">Report Type</label>
          <select
            id="report-type"
            v-model="reportType"
            class="border-gray-300 rounded-md shadow-sm focus:border-red-500 focus:ring-red-500"
          >
            <option value="all">All Reports</option>
            <option value="financial">Financial</option>
            <option value="attendance">Attendance</option>
            <option value="sales">Sales</option>
            <option value="vendor">Vendor</option>
          </select>
        </div>
        <div>
          <label for="date-range" class="block text-sm font-medium text-gray-700 mb-1">Date Range</label>
          <select
            id="date-range"
            v-model="dateRange"
            class="border-gray-300 rounded-md shadow-sm focus:border-red-500 focus:ring-red-500"
          >
            <option value="all">All Time</option>
            <option value="today">Today</option>
            <option value="week">This Week</option>
            <option value="month">This Month</option>
            <option value="quarter">This Quarter</option>
            <option value="year">This Year</option>
            <option value="custom">Custom Range</option>
          </select>
        </div>
        <div v-if="dateRange === 'custom'" class="flex space-x-2">
          <div>
            <label for="start-date" class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
            <input
              id="start-date"
              v-model="startDate"
              type="date"
              class="border-gray-300 rounded-md shadow-sm focus:border-red-500 focus:ring-red-500"
            />
          </div>
          <div>
            <label for="end-date" class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
            <input
              id="end-date"
              v-model="endDate"
              type="date"
              class="border-gray-300 rounded-md shadow-sm focus:border-red-500 focus:ring-red-500"
            />
          </div>
        </div>
        <div class="md:ml-auto">
          <label class="block text-sm font-medium text-gray-700 mb-1">Search</label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Icon icon="heroicons:magnifying-glass" class="h-5 w-5 text-gray-400" />
            </div>
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search reports"
              class="block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:border-red-500 focus:ring-red-500"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Reports Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      <div class="bg-white p-4 shadow rounded">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-blue-100 mr-4">
            <Icon icon="heroicons:currency-dollar" class="w-6 h-6 text-blue-600" />
          </div>
          <div>
            <p class="text-sm text-gray-500">Total Revenue</p>
            <p class="text-2xl font-bold">{{ formatCurrency(stats.totalRevenue) }}</p>
            <div class="flex items-center text-sm">
              <span :class="stats.revenueGrowth >= 0 ? 'text-green-600' : 'text-red-600'">
                <Icon :icon="stats.revenueGrowth >= 0 ? 'heroicons:arrow-up' : 'heroicons:arrow-down'" class="w-4 h-4 inline" />
                {{ Math.abs(stats.revenueGrowth) }}%
              </span>
              <span class="text-gray-500 ml-1">vs previous period</span>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white p-4 shadow rounded">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-green-100 mr-4">
            <Icon icon="heroicons:ticket" class="w-6 h-6 text-green-600" />
          </div>
          <div>
            <p class="text-sm text-gray-500">Tickets Sold</p>
            <p class="text-2xl font-bold">{{ stats.ticketsSold }}</p>
            <div class="flex items-center text-sm">
              <span :class="stats.ticketsGrowth >= 0 ? 'text-green-600' : 'text-red-600'">
                <Icon :icon="stats.ticketsGrowth >= 0 ? 'heroicons:arrow-up' : 'heroicons:arrow-down'" class="w-4 h-4 inline" />
                {{ Math.abs(stats.ticketsGrowth) }}%
              </span>
              <span class="text-gray-500 ml-1">vs previous period</span>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white p-4 shadow rounded">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-purple-100 mr-4">
            <Icon icon="heroicons:users" class="w-6 h-6 text-purple-600" />
          </div>
          <div>
            <p class="text-sm text-gray-500">Total Attendees</p>
            <p class="text-2xl font-bold">{{ stats.totalAttendees }}</p>
            <div class="flex items-center text-sm">
              <span :class="stats.attendeesGrowth >= 0 ? 'text-green-600' : 'text-red-600'">
                <Icon :icon="stats.attendeesGrowth >= 0 ? 'heroicons:arrow-up' : 'heroicons:arrow-down'" class="w-4 h-4 inline" />
                {{ Math.abs(stats.attendeesGrowth) }}%
              </span>
              <span class="text-gray-500 ml-1">vs previous period</span>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white p-4 shadow rounded">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-yellow-100 mr-4">
            <Icon icon="heroicons:building-storefront" class="w-6 h-6 text-yellow-600" />
          </div>
          <div>
            <p class="text-sm text-gray-500">Vendor Revenue</p>
            <p class="text-2xl font-bold">{{ formatCurrency(stats.vendorRevenue) }}</p>
            <div class="flex items-center text-sm">
              <span :class="stats.vendorGrowth >= 0 ? 'text-green-600' : 'text-red-600'">
                <Icon :icon="stats.vendorGrowth >= 0 ? 'heroicons:arrow-up' : 'heroicons:arrow-down'" class="w-4 h-4 inline" />
                {{ Math.abs(stats.vendorGrowth) }}%
              </span>
              <span class="text-gray-500 ml-1">vs previous period</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Reports Table -->
    <div class="bg-white shadow">
      <div v-if="loading" class="flex justify-center items-center py-20">
        <CoreLoader />
      </div>
      <div v-else>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead>
              <tr>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Report Name</th>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Period</th>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Generated</th>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="report in filteredReports" :key="report.id" class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10 bg-gray-100 rounded-full flex items-center justify-center">
                      <Icon :icon="getReportIcon(report.type)" class="h-5 w-5 text-gray-600" />
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium text-gray-900">{{ report.name }}</div>
                      <div class="text-sm text-gray-500">{{ report.description }}</div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ formatReportType(report.type) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ report.period }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ formatDate(report.generatedAt) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                    :class="{
                      'bg-green-100 text-green-800': report.status === 'completed',
                      'bg-yellow-100 text-yellow-800': report.status === 'processing',
                      'bg-red-100 text-red-800': report.status === 'failed',
                      'bg-gray-100 text-gray-800': report.status === 'scheduled'
                    }">
                    {{ formatStatus(report.status) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div class="flex space-x-2">
                    <button
                      v-if="report.status === 'completed'"
                      @click="viewReport(report)"
                      class="text-gray-600 hover:text-gray-900"
                    >
                      <Icon icon="heroicons:eye" class="w-5 h-5" />
                    </button>
                    <button
                      v-if="report.status === 'completed'"
                      @click="downloadReport(report)"
                      class="text-blue-600 hover:text-blue-900"
                    >
                      <Icon icon="heroicons:document-arrow-down" class="w-5 h-5" />
                    </button>
                    <button
                      v-if="report.status === 'scheduled'"
                      @click="cancelReport(report)"
                      class="text-red-600 hover:text-red-900"
                    >
                      <Icon icon="heroicons:x-circle" class="w-5 h-5" />
                    </button>
                    <button
                      v-if="report.status === 'failed'"
                      @click="regenerateReport(report)"
                      class="text-yellow-600 hover:text-yellow-900"
                    >
                      <Icon icon="heroicons:arrow-path" class="w-5 h-5" />
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
          <div class="flex-1 flex justify-between sm:hidden">
            <button @click="prevPage" :disabled="currentPage === 1" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
              Previous
            </button>
            <button @click="nextPage" :disabled="currentPage === totalPages" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
              Next
            </button>
          </div>
          <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p class="text-sm text-gray-700">
                Showing <span class="font-medium">{{ paginationStart }}</span> to <span class="font-medium">{{ paginationEnd }}</span> of <span class="font-medium">{{ totalItems }}</span> reports
              </p>
            </div>
            <div>
              <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button @click="prevPage" :disabled="currentPage === 1" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                  <span class="sr-only">Previous</span>
                  <Icon icon="heroicons:chevron-left" class="h-5 w-5" />
                </button>
                <button v-for="page in displayedPages" :key="page" @click="goToPage(page)" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium" :class="page === currentPage ? 'z-10 bg-red-50 border-red-500 text-red-600' : 'text-gray-500 hover:bg-gray-50'">
                  {{ page }}
                </button>
                <button @click="nextPage" :disabled="currentPage === totalPages" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                  <span class="sr-only">Next</span>
                  <Icon icon="heroicons:chevron-right" class="h-5 w-5" />
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { ENDPOINTS } from '@/utils/api';

definePageMeta({
  layout: "dashboard",
});

const loading = ref(true);
const searchQuery = ref('');
const reportType = ref('all');
const dateRange = ref('all');
const startDate = ref('');
const endDate = ref('');
const currentPage = ref(1);
const itemsPerPage = 10;
const totalItems = ref(0);
const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

interface ReportStats {
  totalRevenue: number;
  revenueGrowth: number;
  ticketsSold: number;
  ticketsGrowth: number;
  totalAttendees: number;
  attendeesGrowth: number;
  vendorRevenue: number;
  vendorGrowth: number;
  totalEvents: number;
  eventsGrowth: number;
}

interface Report {
  id: number;
  name: string;
  description: string;
  type: 'financial' | 'sales' | 'attendance' | 'vendor';
  period: string;
  generatedAt: string;
  status: 'completed' | 'processing' | 'failed' | 'scheduled' | 'cancelled';
}

const stats = ref<ReportStats>({
  totalRevenue: 0,
  revenueGrowth: 0,
  ticketsSold: 0,
  ticketsGrowth: 0,
  totalAttendees: 0,
  attendeesGrowth: 0,
  vendorRevenue: 0,
  vendorGrowth: 0,
  totalEvents: 0,
  eventsGrowth: 0
});

const fetchReportStats = async () => {
  try {
    const params = new URLSearchParams();
    if (dateRange.value !== 'all') {
      params.append('period', dateRange.value);
    }

    const response = await httpClient.get<ReportStats>(`${ENDPOINTS.REPORTS.STATS}?${params.toString()}`);
    stats.value = response;
  } catch (error) {
    console.error('Error fetching report stats:', error);
    $toast.error('Failed to load report statistics');
  }
};

const generateReport = async () => {
  try {
    const payload = {
      type: reportType.value,
      period: dateRange.value,
      start_date: startDate.value,
      end_date: endDate.value,
      format: 'json'
    };

    const response = await httpClient.post(ENDPOINTS.REPORTS.GENERATE, payload);
    $toast.success('Report generated successfully');
    console.log('Generated report:', response);
  } catch (error) {
    console.error('Error generating report:', error);
    $toast.error('Failed to generate report');
  }
};

const exportReport = async (reportId: string) => {
  try {
    const response = await httpClient.get(`${ENDPOINTS.REPORTS.EXPORT}/${reportId}`);
    $toast.success('Report export initiated');
    console.log('Export response:', response);
  } catch (error) {
    console.error('Error exporting report:', error);
    $toast.error('Failed to export report');
  }
};

const reports = ref<Report[]>([]);

const fetchReports = async () => {
  try {
    loading.value = true;
    const params = new URLSearchParams({
      page: currentPage.value.toString(),
      limit: itemsPerPage.toString(),
      search: searchQuery.value,
      type: reportType.value !== 'all' ? reportType.value : '',
      period: dateRange.value !== 'all' ? dateRange.value : '',
      start_date: startDate.value,
      end_date: endDate.value
    });

    const response = await httpClient.get<{ data: Report[], total: number }>(
      `${ENDPOINTS.REPORTS.BASE}?${params.toString()}`
    );

    reports.value = response.data;
    totalItems.value = response.total;
  } catch (error) {
    console.error('Error fetching reports:', error);
    $toast.error('Failed to load reports');
  } finally {
    loading.value = false;
  }
};

const newReport = async () => {
  try {
    const payload = {
      type: reportType.value !== 'all' ? reportType.value : 'financial',
      period: dateRange.value !== 'all' ? dateRange.value : 'month',
      start_date: startDate.value,
      end_date: endDate.value,
      format: 'pdf'
    };

    const response = await httpClient.post(ENDPOINTS.REPORTS.GENERATE, payload);
    $toast.success('Report generation initiated');

    // Refresh reports list
    await fetchReports();
  } catch (error) {
    console.error('Error generating report:', error);
    $toast.error('Failed to generate report');
  }
};

const exportReports = async () => {
  try {
    const params = new URLSearchParams({
      search: searchQuery.value,
      type: reportType.value !== 'all' ? reportType.value : '',
      period: dateRange.value !== 'all' ? dateRange.value : '',
      start_date: startDate.value,
      end_date: endDate.value
    });

    const response = await httpClient.get(`${ENDPOINTS.REPORTS.EXPORT}?${params.toString()}`, {
      responseType: 'blob'
    }) as Blob;

    // Create download link
    const url = window.URL.createObjectURL(response);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', `reports-${new Date().toISOString().split('T')[0]}.csv`);
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);

    $toast.success('Reports exported successfully');
  } catch (error) {
    console.error('Error exporting reports:', error);
    $toast.error('Failed to export reports');
  }
};

const filteredReports = computed(() => {
  // Since filtering and pagination are handled by the backend,
  // we just return the fetched reports directly
  return reports.value;
});

const totalPages = computed(() => Math.ceil(totalItems.value / itemsPerPage));

const paginationStart = computed(() => {
  if (totalItems.value === 0) return 0;
  return (currentPage.value - 1) * itemsPerPage + 1;
});

const paginationEnd = computed(() => {
  return Math.min(currentPage.value * itemsPerPage, totalItems.value);
});

const displayedPages = computed(() => {
  const pages = [];
  const maxPagesToShow = 5;

  if (totalPages.value <= maxPagesToShow) {
    for (let i = 1; i <= totalPages.value; i++) {
      pages.push(i);
    }
  } else {
    const leftSide = Math.floor(maxPagesToShow / 2);
    const rightSide = maxPagesToShow - leftSide - 1;

    if (currentPage.value > leftSide && currentPage.value < totalPages.value - rightSide) {
      // Middle case
      for (let i = currentPage.value - leftSide; i <= currentPage.value + rightSide; i++) {
        pages.push(i);
      }
    } else if (currentPage.value <= leftSide) {
      // Near the start
      for (let i = 1; i <= maxPagesToShow; i++) {
        pages.push(i);
      }
    } else {
      // Near the end
      for (let i = totalPages.value - maxPagesToShow + 1; i <= totalPages.value; i++) {
        pages.push(i);
      }
    }
  }

  return pages;
});

const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount);
};

function formatDate(dateString: string | number | Date): string {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
}

function formatStatus(status: string): string {
  return status.charAt(0).toUpperCase() + status.slice(1);
}

function formatReportType(type: string): string {
  return type.charAt(0).toUpperCase() + type.slice(1);
}

function getReportIcon(type: string): string {
  switch (type) {
    case 'financial':
      return 'heroicons:currency-dollar';
    case 'sales':
      return 'heroicons:shopping-cart';
    case 'attendance':
      return 'heroicons:users';
    case 'vendor':
      return 'heroicons:building-storefront';
    default:
      return 'heroicons:document-text';
  }
}

function prevPage() {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
}

function nextPage() {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
}

function goToPage(page: number): void {
  currentPage.value = page;
}

const viewReport = async (report: Report): Promise<void> => {
  try {
    const response = await httpClient.get(`${ENDPOINTS.REPORTS.BASE}/${report.id}`);
    console.log('Report details:', response);
    $toast.info(`Viewing report: ${report.name}`);
    // Here you could open a modal or navigate to a detailed view
  } catch (error) {
    console.error('Error viewing report:', error);
    $toast.error('Failed to load report details');
  }
};

const downloadReport = async (report: Report): Promise<void> => {
  try {
    const response = await httpClient.get(`${ENDPOINTS.REPORTS.BASE}/${report.id}/download`, {
      responseType: 'blob'
    }) as Blob;

    // Create download link
    const url = window.URL.createObjectURL(response);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', `${report.name.replace(/\s+/g, '-').toLowerCase()}.pdf`);
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);

    $toast.success(`Report "${report.name}" downloaded successfully`);
  } catch (error) {
    console.error('Error downloading report:', error);
    $toast.error('Failed to download report');
  }
};

const cancelReport = async (report: Report): Promise<void> => {
  try {
    await httpClient.delete(`${ENDPOINTS.REPORTS.BASE}/${report.id}`);
    $toast.success(`Report "${report.name}" has been cancelled`);

    // Refresh reports list
    await fetchReports();
  } catch (error) {
    console.error('Error cancelling report:', error);
    $toast.error('Failed to cancel report');
  }
};

const regenerateReport = async (report: Report): Promise<void> => {
  try {
    await httpClient.post(`${ENDPOINTS.REPORTS.BASE}/${report.id}/regenerate`);
    $toast.success(`Report "${report.name}" is being regenerated`);

    // Refresh reports list
    await fetchReports();
  } catch (error) {
    console.error('Error regenerating report:', error);
    $toast.error('Failed to regenerate report');
  }
};

watch([searchQuery, reportType, dateRange, startDate, endDate], () => {
  currentPage.value = 1;
  fetchReports();
});

onMounted(async () => {
  await Promise.all([
    fetchReportStats(),
    fetchReports()
  ]);
});
</script>
