<template>
  <div class="p-6 bg-gray-100 dark:bg-zinc-900 min-h-screen">
    <div class="mb-6 flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Attendees</h1>
        <p class="text-gray-500 dark:text-gray-400">Manage your event attendees and their information</p>
      </div>
      <div class="flex space-x-2">
        <button @click="exportAttendees" :disabled="loading" class="px-4 py-2 bg-white dark:bg-zinc-800 border border-gray-300 dark:border-gray-600 shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center text-gray-700 dark:text-gray-300 disabled:opacity-50">
          <Icon icon="heroicons:arrow-down-tray" class="w-5 h-5 mr-1" />
          Export
        </button>
        <button @click="addAttendee" class="px-4 py-2 bg-red-600 text-white shadow-sm hover:bg-red-700 flex items-center">
          <Icon icon="heroicons:plus" class="w-5 h-5 mr-1" />
          Add Attendee
        </button>
      </div>
    </div>

    <!-- Filters -->
    <div class="bg-white dark:bg-zinc-800 p-4 shadow mb-6">
      <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4">
        <div class="flex-1">
          <label for="event-filter" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Event</label>
          <select id="event-filter" v-model="filters.eventId" class="w-full border-gray-300 dark:border-gray-600 bg-white dark:bg-zinc-700 text-gray-900 dark:text-white shadow-sm focus:border-red-500 focus:ring-red-500">
            <option value="">All Events</option>
            <option v-for="event in events" :key="event.id" :value="event.id">{{ event.title }}</option>
          </select>
        </div>
        <div class="flex-1">
          <label for="status-filter" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Status</label>
          <select id="status-filter" v-model="filters.status" class="w-full border-gray-300 dark:border-gray-600 bg-white dark:bg-zinc-700 text-gray-900 dark:text-white shadow-sm focus:border-red-500 focus:ring-red-500">
            <option value="">All Statuses</option>
            <option value="confirmed">Confirmed</option>
            <option value="checked-in">Checked In</option>
            <option value="no-show">No Show</option>
            <option value="cancelled">Cancelled</option>
          </select>
        </div>
        <div class="flex-1">
          <label for="ticket-filter" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Ticket Type</label>
          <select id="ticket-filter" v-model="filters.ticketType" class="w-full border-gray-300 dark:border-gray-600 bg-white dark:bg-zinc-700 text-gray-900 dark:text-white shadow-sm focus:border-red-500 focus:ring-red-500">
            <option value="">All Ticket Types</option>
            <option value="Basic">Basic</option>
            <option value="Premium">Premium</option>
            <option value="VIP">VIP</option>
            <option value="Early Bird">Early Bird</option>
          </select>
        </div>
        <div class="flex-1">
          <label for="search" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Search</label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Icon icon="heroicons:magnifying-glass" class="h-5 w-5 text-gray-400 dark:text-gray-500" />
            </div>
            <input
              id="search"
              v-model="searchQuery"
              type="text"
              placeholder="Search by name or email"
              class="block w-full pl-10 border-gray-300 dark:border-gray-600 bg-white dark:bg-zinc-700 text-gray-900 dark:text-white shadow-sm focus:border-red-500 focus:ring-red-500"
            />
          </div>
        </div>
      </div>

      <!-- Active Filters Display -->
      <div v-if="hasActiveFilters" class="mt-4 flex flex-wrap gap-2">
        <span class="text-sm text-gray-600 dark:text-gray-400">Active filters:</span>
        <span v-if="filters.eventId" class="inline-flex items-center px-2 py-1 text-xs bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200">
          Event: {{ getEventName(filters.eventId) }}
          <button @click="filters.eventId = ''" class="ml-1 text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200">
            <Icon icon="heroicons:x-mark" class="w-3 h-3" />
          </button>
        </span>
        <span v-if="filters.status" class="inline-flex items-center px-2 py-1 text-xs bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200">
          Status: {{ filters.status }}
          <button @click="filters.status = ''" class="ml-1 text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200">
            <Icon icon="heroicons:x-mark" class="w-3 h-3" />
          </button>
        </span>
        <span v-if="filters.ticketType" class="inline-flex items-center px-2 py-1 text-xs bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200">
          Ticket: {{ filters.ticketType }}
          <button @click="filters.ticketType = ''" class="ml-1 text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200">
            <Icon icon="heroicons:x-mark" class="w-3 h-3" />
          </button>
        </span>
        <span v-if="searchQuery" class="inline-flex items-center px-2 py-1 text-xs bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200">
          Search: "{{ searchQuery }}"
          <button @click="searchQuery = ''" class="ml-1 text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200">
            <Icon icon="heroicons:x-mark" class="w-3 h-3" />
          </button>
        </span>
        <button @click="clearAllFilters" class="text-sm text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 underline">
          Clear all
        </button>
      </div>
    </div>

    <!-- Attendees Table -->
    <div class="bg-white dark:bg-zinc-800 shadow">
      <div v-if="loading" class="flex justify-center items-center py-20">
        <CoreLoader />
      </div>
      <div v-else-if="attendees.length === 0" class="flex flex-col justify-center items-center py-20">
        <Icon icon="heroicons:users" class="w-16 h-16 text-gray-400 dark:text-gray-600 mb-4" />
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No attendees found</h3>
        <p class="text-gray-500 dark:text-gray-400 text-center max-w-md">
          {{ hasActiveFilters ? 'No attendees match your current filters. Try adjusting your search criteria.' : 'No attendees have been registered yet. Start by adding some attendees.' }}
        </p>
        <button v-if="hasActiveFilters" @click="clearAllFilters" class="mt-4 text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 underline">
          Clear all filters
        </button>
      </div>
      <div v-else>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead>
              <tr>
                <th class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  <div class="flex items-center">
                    <input type="checkbox" v-model="selectAll" class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 dark:border-gray-600 bg-white dark:bg-zinc-700" />
                  </div>
                </th>
                <th class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Attendee</th>
                <th class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Event</th>
                <th class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Ticket</th>
                <th class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Purchase Date</th>
                <th class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                <th class="px-6 py-3 bg-gray-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-zinc-800 divide-y divide-gray-200 dark:divide-gray-700">
              <tr v-for="attendee in attendees" :key="attendee.id" class="hover:bg-gray-50 dark:hover:bg-gray-700">
                <td class="px-6 py-4 whitespace-nowrap">
                  <input type="checkbox" v-model="selectedAttendees" :value="attendee.id" class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 dark:border-gray-600 bg-white dark:bg-zinc-700" />
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10">
                      <img class="h-10 w-10 rounded-full" :src="`${runtimeConfig.public.baseUrl}storage/avatars/${attendee.avatar}`" alt="" />
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium text-gray-900 dark:text-white">{{ attendee.name }}</div>
                      <div class="text-sm text-gray-500 dark:text-gray-400">{{ attendee.email }}</div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900 dark:text-white">{{ attendee.event.title }}</div>
                  <div class="text-sm text-gray-500 dark:text-gray-400">{{ formatDate(attendee.event.date) }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900 dark:text-white">{{ attendee.ticket.type }}</div>
                  <div class="text-sm text-gray-500 dark:text-gray-400">${{ Number(attendee.ticket.price).toFixed(2) }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  {{ formatDate(attendee.purchaseDate) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                    :class="{
                      'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200': attendee.status === 'checked-in',
                      'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200': attendee.status === 'confirmed',
                      'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200': attendee.status === 'no-show',
                      'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200': attendee.status === 'cancelled'
                    }">
                    {{ formatStatus(attendee.status) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div class="flex space-x-2">
                    <button v-if="attendee.status === 'confirmed'" @click="checkInAttendee(attendee)" class="text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-200" title="Check In">
                      <Icon icon="heroicons:check-circle" class="w-5 h-5" />
                    </button>
                    <button @click="viewAttendee(attendee)" class="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200" title="View Details">
                      <Icon icon="heroicons:eye" class="w-5 h-5" />
                    </button>
                    <button @click="editAttendee(attendee)" class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-200" title="Edit">
                      <Icon icon="heroicons:pencil-square" class="w-5 h-5" />
                    </button>
                    <button @click="confirmDeleteAttendee(attendee)" class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-200" title="Delete">
                      <Icon icon="heroicons:trash" class="w-5 h-5" />
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div class="bg-white dark:bg-zinc-800 px-4 py-3 flex items-center justify-between border-t border-gray-200 dark:border-gray-700 sm:px-6">
          <div class="flex-1 flex justify-between sm:hidden">
            <button @click="prevPage" :disabled="currentPage === 1" class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-zinc-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50">
              Previous
            </button>
            <button @click="nextPage" :disabled="currentPage === totalPages" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-zinc-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50">
              Next
            </button>
          </div>
          <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p class="text-sm text-gray-700 dark:text-gray-300">
                Showing <span class="font-medium">{{ paginationStart }}</span> to <span class="font-medium">{{ paginationEnd }}</span> of <span class="font-medium">{{ totalItems }}</span> attendees
              </p>
            </div>
            <div>
              <nav class="relative z-0 inline-flex shadow-sm -space-x-px" aria-label="Pagination">
                <button @click="prevPage" :disabled="currentPage === 1" class="relative inline-flex items-center px-2 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-zinc-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50">
                  <span class="sr-only">Previous</span>
                  <Icon icon="heroicons:chevron-left" class="h-5 w-5" />
                </button>
                <button v-for="page in displayedPages" :key="page" @click="goToPage(page)" class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-zinc-700 text-sm font-medium" :class="page === currentPage ? 'z-10 bg-red-50 dark:bg-red-900 border-red-500 text-red-600 dark:text-red-200' : 'text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600'">
                  {{ page }}
                </button>
                <button @click="nextPage" :disabled="currentPage === totalPages" class="relative inline-flex items-center px-2 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-zinc-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50">
                  <span class="sr-only">Next</span>
                  <Icon icon="heroicons:chevron-right" class="h-5 w-5" />
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <TransitionRoot appear :show="isDeleteModalOpen" as="template">
      <Dialog as="div" @close="closeDeleteModal" class="relative z-10">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-25 dark:bg-opacity-75" />
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div class="flex min-h-full items-center justify-center p-4 text-center">
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel class="w-full max-w-md transform overflow-hidden bg-white dark:bg-zinc-800 p-6 text-left align-middle shadow-xl transition-all">
                <DialogTitle as="h3" class="text-lg font-medium leading-6 text-gray-900 dark:text-white">
                  Delete Attendee
                </DialogTitle>
                <div class="mt-2">
                  <p class="text-sm text-gray-500 dark:text-gray-400">
                    Are you sure you want to delete this attendee? This action cannot be undone.
                  </p>
                </div>

                <div class="mt-4 flex justify-end space-x-2">
                  <button
                    type="button"
                    class="inline-flex justify-center border border-transparent bg-gray-100 dark:bg-zinc-700 px-4 py-2 text-sm font-medium text-gray-900 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none"
                    @click="closeDeleteModal"
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    class="inline-flex justify-center border border-transparent bg-red-600 px-4 py-2 text-sm font-medium text-white hover:bg-red-700 focus:outline-none"
                    @click="deleteAttendee"
                  >
                    Delete
                  </button>
                </div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue';
import { ENDPOINTS } from '@/utils/api';
import type { EventItem } from '~/types';

definePageMeta({
  layout: "dashboard",
});

const runtimeConfig = useRuntimeConfig();
const loading = ref(true);
const searchQuery = ref('');
const currentPage = ref(1);
const itemsPerPage = 10;
const totalItems = ref(0);
const totalPages = computed(() => Math.ceil(totalItems.value / itemsPerPage));
const isDeleteModalOpen = ref(false);
const attendeeToDelete = ref<any>(null);
const selectAll = ref(false);
const selectedAttendees = ref<number[]>([]);
const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

interface Attendee {
  id: number;
  name: string;
  email: string;
  avatar?: string;
  event: {
    id: number;
    title: string;
    date: string;
  };
  ticket: {
    type: string;
    price: number;
  };
  purchaseDate: string;
  status: 'confirmed' | 'checked-in' | 'no-show' | 'cancelled';
}

interface Event {
  id: number;
  title: string;
}

const filters = ref({
  eventId: '',
  status: '',
  ticketType: '',
});

const events = ref<EventItem[]>([]);
const attendees = ref<Attendee[]>([]);

const fetchEvents = async () => {
  try {
    const response = await httpClient.get<Event[]>(ENDPOINTS.EVENTS.USER);
    events.value = response;
  } catch (error) {
    console.error('Error fetching events:', error);
    $toast.error('Failed to load events');
  }
};

const fetchAttendees = async () => {
  try {
    loading.value = true;
    const params = new URLSearchParams();

    // Add pagination
    params.append('page', currentPage.value.toString());
    params.append('limit', itemsPerPage.toString());

    // Add filters only if they have values
    if (searchQuery.value.trim()) {
      params.append('search', searchQuery.value.trim());
    }
    if (filters.value.eventId) {
      params.append('event_id', filters.value.eventId);
    }
    if (filters.value.status) {
      params.append('status', filters.value.status);
    }
    if (filters.value.ticketType) {
      params.append('ticket_type', filters.value.ticketType);
    }

    const response = await httpClient.get<{ data: Attendee[], total: number, current_page: number, per_page: number, last_page: number }>(
      `${ENDPOINTS.ATTENDEES.GET}?${params.toString()}`
    );

    attendees.value = response.data || [];
    totalItems.value = response.total || 0;
    currentPage.value = response.current_page || 1;
  } catch (error) {
    console.error('Error fetching attendees:', error);
    $toast.error('Failed to load attendees');
    attendees.value = [];
    totalItems.value = 0;
  } finally {
    loading.value = false;
  }
};

const exportAttendees = async () => {
  try {
    const params = new URLSearchParams();

    // Add filters only if they have values
    if (searchQuery.value.trim()) {
      params.append('search', searchQuery.value.trim());
    }
    if (filters.value.eventId) {
      params.append('event_id', filters.value.eventId);
    }
    if (filters.value.status) {
      params.append('status', filters.value.status);
    }
    if (filters.value.ticketType) {
      params.append('ticket_type', filters.value.ticketType);
    }

    const response = await httpClient.get(`${ENDPOINTS.ATTENDEES.EXPORT}?${params.toString()}`, {
      responseType: 'blob'
    }) as Blob;

    // Create download link
    const url = window.URL.createObjectURL(response);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', `attendees-${new Date().toISOString().split('T')[0]}.csv`);
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);

    $toast.success('Attendees exported successfully');
  } catch (error) {
    console.error('Error exporting attendees:', error);
    $toast.error('Failed to export attendees');
  }
};

// Computed properties
const hasActiveFilters = computed(() => {
  return !!(filters.value.eventId || filters.value.status || filters.value.ticketType || searchQuery.value.trim());
});

const getEventName = (eventId: string): string => {
  const event = events.value.find(e => e.id.toString() === eventId);
  return event ? event.title : 'Unknown Event';
};

const clearAllFilters = (): void => {
  filters.value.eventId = '';
  filters.value.status = '';
  filters.value.ticketType = '';
  searchQuery.value = '';
};

const checkInAttendee = async (attendee: Attendee) => {
  try {
    await httpClient.post(`${ENDPOINTS.ATTENDEES.CHECKIN}/${attendee.id}`);
    $toast.success(`${attendee.name} has been checked in`);

    // Update local state
    const index = attendees.value.findIndex(a => a.id === attendee.id);
    if (index !== -1) {
      attendees.value[index].status = 'checked-in';
    }
  } catch (error) {
    console.error('Error checking in attendee:', error);
    $toast.error('Failed to check in attendee');
  }
};

const deleteSelectedAttendee = async () => {
  if (!attendeeToDelete.value) return;

  try {
    await httpClient.delete(`${ENDPOINTS.ATTENDEES.DELETE}/${attendeeToDelete.value.id}`);
    $toast.success('Attendee deleted successfully');

    // Refresh attendees list
    await fetchAttendees();
    closeDeleteModal();
  } catch (error) {
    console.error('Error deleting attendee:', error);
    $toast.error('Failed to delete attendee');
  }
};

const addAttendee = (): void => {
  // Navigate to add attendee page or open modal
  navigateTo('/dashboard/attendees/create');
};

const paginationStart = computed(() => {
  if (totalItems.value === 0) return 0;
  return (currentPage.value - 1) * itemsPerPage + 1;
});

const paginationEnd = computed(() => {
  return Math.min(currentPage.value * itemsPerPage, totalItems.value);
});

const displayedPages = computed(() => {
  const pages = [];
  const maxPagesToShow = 5;

  if (totalPages.value <= maxPagesToShow) {
    for (let i = 1; i <= totalPages.value; i++) {
      pages.push(i);
    }
  } else {
    const leftSide = Math.floor(maxPagesToShow / 2);
    const rightSide = maxPagesToShow - leftSide - 1;

    if (currentPage.value > leftSide && currentPage.value < totalPages.value - rightSide) {
      // Middle case
      for (let i = currentPage.value - leftSide; i <= currentPage.value + rightSide; i++) {
        pages.push(i);
      }
    } else if (currentPage.value <= leftSide) {
      // Near the start
      for (let i = 1; i <= maxPagesToShow; i++) {
        pages.push(i);
      }
    } else {
      // Near the end
      for (let i = totalPages.value - maxPagesToShow + 1; i <= totalPages.value; i++) {
        pages.push(i);
      }
    }
  }

  return pages;
});

const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

const formatStatus = (status: string): string => {
  switch (status) {
    case 'confirmed': return 'Confirmed';
    case 'checked-in': return 'Checked In';
    case 'no-show': return 'No Show';
    case 'cancelled': return 'Cancelled';
    default: return status;
  }
};

const prevPage = (): void => {
  if (currentPage.value > 1) {
    currentPage.value--;
    fetchAttendees();
  }
};

const nextPage = (): void => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
    fetchAttendees();
  }
};

const goToPage = (page: number): void => {
  currentPage.value = page;
  fetchAttendees();
};

const viewAttendee = (attendee: Attendee): void => {
  // Navigate to attendee details page
  navigateTo(`/dashboard/attendees/${attendee.id}`);
};

const editAttendee = (attendee: Attendee): void => {
  // Navigate to edit attendee page
  navigateTo(`/dashboard/attendees/${attendee.id}/edit`);
};

const confirmDeleteAttendee = (attendee: Attendee): void => {
  attendeeToDelete.value = attendee;
  isDeleteModalOpen.value = true;
};

const closeDeleteModal = (): void => {
  isDeleteModalOpen.value = false;
  attendeeToDelete.value = null;
};

const deleteAttendee = async (): Promise<void> => {
  await deleteSelectedAttendee();
};

watch(selectAll, (newValue: boolean) => {
  if (newValue) {
    selectedAttendees.value = attendees.value.map(a => a.id);
  } else {
    selectedAttendees.value = [];
  }
});

// Debounced search
let searchTimeout: NodeJS.Timeout;
watch(searchQuery, () => {
  clearTimeout(searchTimeout);
  searchTimeout = setTimeout(() => {
    currentPage.value = 1;
    fetchAttendees();
  }, 300);
});

// Watch filters for immediate response
watch(filters, () => {
  currentPage.value = 1;
  fetchAttendees();
}, { deep: true });

onMounted(async () => {
  await Promise.all([
    fetchEvents(),
    fetchAttendees()
  ]);
});
</script>
