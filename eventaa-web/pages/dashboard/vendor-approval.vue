<template>
  <div class="p-6 bg-gray-100 dark:bg-gray-900 min-h-screen">
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Vendor Approval</h1>
      <p class="text-gray-600 dark:text-gray-400 mt-1">Manage vendor requests and approve new vendors</p>
    </div>

    <div class="bg-white dark:bg-gray-800 shadow overflow-hidden">
      <div class="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
        <h2 class="text-lg font-medium text-gray-800 dark:text-white">Pending Vendor Requests</h2>
        <div class="flex items-center space-x-2">
          <div class="relative">
            <input
              type="text"
              v-model="searchQuery"
              placeholder="Search vendors..."
              class="pl-8 pr-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-red-500"
            />
            <div class="absolute left-3 top-1/2 transform -translate-y-1/2">
              <Icon icon="heroicons:magnifying-glass" class="h-4 w-4 text-gray-400 dark:text-gray-500" />
            </div>
          </div>
          <button
            @click="fetchVendorRequests"
            class="p-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
            title="Refresh"
          >
            <Icon icon="heroicons:arrow-path" class="h-4 w-4 text-gray-600 dark:text-gray-400" />
          </button>
        </div>
      </div>

      <div v-if="loading" class="p-8 flex justify-center">
        <div class="flex flex-col items-center">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-red-500"></div>
          <p class="mt-3 text-gray-600 dark:text-gray-400">Loading vendor requests...</p>
        </div>
      </div>

      <div v-else-if="vendorRequests.length === 0" class="p-8 text-center">
        <div class="flex flex-col items-center">
          <Icon icon="heroicons:document-text" class="h-12 w-12 text-gray-400 dark:text-gray-600" />
          <h3 class="mt-2 text-lg font-medium text-gray-900 dark:text-white">No vendor requests</h3>
          <p class="mt-1 text-gray-500 dark:text-gray-400">There are no pending vendor requests at this time.</p>
        </div>
      </div>

      <div v-else class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Business
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Owner
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Contact
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Services
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Submitted
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Status
              </th>
              <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            <tr v-for="vendor in filteredVendorRequests" :key="vendor.id" class="hover:bg-gray-50 dark:hover:bg-gray-700">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-shrink-0 h-10 w-10">
                    <img
                      :src="vendor.logo ? `${runtimeConfig.public.baseUrl}${vendor.logo}` : `https://ui-avatars.com/api/?name=${encodeURIComponent(vendor.name)}&background=random`"
                      class="h-10 w-10 rounded-full object-cover"
                      :alt="vendor.name"
                    />
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900 dark:text-white">{{ vendor.name }}</div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">{{ vendor.location }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900 dark:text-white">{{ vendor.user?.name }}</div>
                <div class="text-sm text-gray-500 dark:text-gray-400">{{ vendor.user?.email }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900 dark:text-white">{{ vendor.phone }}</div>
                <div class="text-sm text-gray-500 dark:text-gray-400">{{ vendor.business_email }}</div>
              </td>
              <td class="px-6 py-4">
                <div class="flex flex-wrap gap-1">
                  <span
                    v-for="service in vendor.services"
                    :key="service.id"
                    class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800"
                  >
                    {{ service.service.name }}
                  </span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ formatDate(vendor.created_at) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                  :class="{
                    'bg-yellow-100 text-yellow-800': vendor.status === 'pending',
                    'bg-green-100 text-green-800': vendor.status === 'approved',
                    'bg-red-100 text-red-800': vendor.status === 'rejected'
                  }"
                >
                  {{ vendor.status || 'Pending' }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div class="flex justify-end space-x-2">
                  <button
                    @click="viewVendorDetails(vendor)"
                    class="text-indigo-600 hover:text-indigo-900 p-1"
                    title="View Details"
                  >
                    <Icon icon="heroicons:eye" class="h-5 w-5" />
                  </button>
                  <button
                    @click="approveVendor(vendor)"
                    class="text-green-600 hover:text-green-900 p-1"
                    title="Approve"
                  >
                    <Icon icon="heroicons:check" class="h-5 w-5" />
                  </button>
                  <button
                    @click="rejectVendor(vendor)"
                    class="text-red-600 hover:text-red-900 p-1"
                    title="Reject"
                  >
                    <Icon icon="heroicons:x-mark" class="h-5 w-5" />
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div v-if="vendorRequests.length > 0" class="px-6 py-4 border-t border-gray-200">
        <div class="flex justify-between items-center">
          <div class="text-sm text-gray-700">
            Showing <span class="font-medium">{{ filteredVendorRequests.length }}</span> of
            <span class="font-medium">{{ vendorRequests.length }}</span> vendor requests
          </div>
          <div class="flex space-x-2">
            <button
              @click="currentPage > 1 ? currentPage-- : null"
              :disabled="currentPage === 1"
              :class="[
                'px-3 py-1 rounded-sm text-sm',
                currentPage === 1
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              ]"
            >
              Previous
            </button>
            <button
              @click="currentPage < totalPages ? currentPage++ : null"
              :disabled="currentPage === totalPages"
              :class="[
                'px-3 py-1 rounded-sm text-sm',
                currentPage === totalPages
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              ]"
            >
              Next
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Vendor Details Modal -->
    <TransitionRoot appear :show="!!selectedVendor" as="template">
      <Dialog as="div" @close="selectedVendor = null" class="relative z-50">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-50" />
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div class="flex min-h-full items-center justify-center p-4">
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel class="w-full max-w-4xl transform overflow-hidden rounded-lg bg-white shadow-xl transition-all">
                <div class="flex justify-between items-center p-6 border-b">
                  <DialogTitle as="h3" class="text-xl font-semibold text-gray-900">Vendor Details</DialogTitle>
                  <button @click="selectedVendor = null" class="text-gray-400 hover:text-gray-500">
                    <Icon icon="heroicons:x-mark" class="h-6 w-6" />
                  </button>
                </div>

                <div v-if="selectedVendor" class="p-6">
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 class="text-lg font-medium text-gray-900 mb-4">Business Information</h4>
                      <div class="space-y-3">
                        <div>
                          <label class="block text-sm font-medium text-gray-700">Business Name</label>
                          <div class="mt-1 text-sm text-gray-900">{{ selectedVendor.name }}</div>
                        </div>
                        <div>
                          <label class="block text-sm font-medium text-gray-700">Location</label>
                          <div class="mt-1 text-sm text-gray-900">{{ selectedVendor.location }}</div>
                        </div>
                        <div>
                          <label class="block text-sm font-medium text-gray-700">Bio</label>
                          <div class="mt-1 text-sm text-gray-900">{{ selectedVendor.bio }}</div>
                        </div>
                        <div>
                          <label class="block text-sm font-medium text-gray-700">Languages</label>
                          <div class="mt-1 text-sm text-gray-900">{{ selectedVendor.languages }}</div>
                        </div>
                      </div>
                    </div>
                    <div>
                      <h4 class="text-lg font-medium text-gray-900 mb-4">Contact Information</h4>
                      <div class="space-y-3">
                        <div>
                          <label class="block text-sm font-medium text-gray-700">Phone</label>
                          <div class="mt-1 text-sm text-gray-900">{{ selectedVendor.phone }}</div>
                        </div>
                        <div>
                          <label class="block text-sm font-medium text-gray-700">Business Email</label>
                          <div class="mt-1 text-sm text-gray-900">{{ selectedVendor.business_email }}</div>
                        </div>
                        <div>
                          <label class="block text-sm font-medium text-gray-700">Website</label>
                          <div class="mt-1 text-sm text-gray-900">
                            <a
                              v-if="selectedVendor.website"
                              :href="selectedVendor.website"
                              target="_blank"
                              class="text-red-600 hover:text-red-800"
                            >
                              {{ selectedVendor.website }}
                            </a>
                            <span v-else>Not provided</span>
                          </div>
                        </div>
                        <div>
                          <label class="block text-sm font-medium text-gray-700">Social Media</label>
                          <div class="mt-1 flex space-x-2">
                            <a
                              v-if="selectedVendor.facebook"
                              :href="selectedVendor.facebook"
                              target="_blank"
                              class="text-blue-600 hover:text-blue-800"
                            >
                              <Icon icon="mdi:facebook" class="h-5 w-5" />
                            </a>
                            <a
                              v-if="selectedVendor.instagram"
                              :href="selectedVendor.instagram"
                              target="_blank"
                              class="text-pink-600 hover:text-pink-800"
                            >
                              <Icon icon="mdi:instagram" class="h-5 w-5" />
                            </a>
                            <a
                              v-if="selectedVendor.twitter"
                              :href="selectedVendor.twitter"
                              target="_blank"
                              class="text-blue-400 hover:text-blue-600"
                            >
                              <Icon icon="mdi:twitter" class="h-5 w-5" />
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="mt-6">
                    <h4 class="text-lg font-medium text-gray-900 mb-4">Services</h4>
                    <div class="flex flex-wrap gap-2">
                      <span
                        v-for="service in selectedVendor.services"
                        :key="service.id"
                        class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800"
                      >
                        {{ service.service.name }}
                      </span>
                    </div>
                  </div>

                  <div class="mt-6">
                    <h4 class="text-lg font-medium text-gray-900 mb-4">Pricing</h4>
                    <div class="overflow-x-auto">
                      <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                          <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Price
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Currency
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Description
                            </th>
                          </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                          <tr v-for="price in selectedVendor.prices" :key="price.id">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ price.price }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ price.currency?.name }}</td>
                            <td class="px-6 py-4 text-sm text-gray-900">{{ price.description || 'No description' }}</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>

                <div class="flex justify-end space-x-3 p-6 border-t">
                  <button
                    @click="selectedVendor = null"
                    class="px-4 py-2 bg-white border border-gray-300 rounded-sm text-sm font-medium text-gray-700 hover:bg-gray-50"
                  >
                    Close
                  </button>
                  <button
                    v-if="selectedVendor"
                    @click="approveVendor(selectedVendor)"
                    class="px-4 py-2 bg-green-600 border border-transparent rounded-sm text-sm font-medium text-white hover:bg-green-700"
                  >
                    Approve
                  </button>
                  <button
                    v-if="selectedVendor"
                    @click="rejectVendor(selectedVendor)"
                    class="px-4 py-2 bg-red-600 border border-transparent rounded-sm text-sm font-medium text-white hover:bg-red-700"
                  >
                    Reject
                  </button>
                </div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
    <!-- Rejection Modal -->
    <TransitionRoot appear :show="showRejectionModal" as="template">
      <Dialog as="div" @close="closeRejectModal" class="relative z-50">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-50" />
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div class="flex min-h-full items-center justify-center p-4">
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel class="w-full max-w-md transform overflow-hidden rounded-lg bg-white shadow-xl transition-all">
                <div class="flex justify-between items-center p-6 border-b">
                  <DialogTitle as="h3" class="text-xl font-semibold text-gray-900">Reject Vendor Request</DialogTitle>
                  <button @click="closeRejectModal" class="text-gray-400 hover:text-gray-500">
                    <Icon icon="heroicons:x-mark" class="h-6 w-6" />
                  </button>
                </div>
                <div class="p-6">
                  <p class="mb-4 text-gray-700">
                    Please provide a reason for rejecting {{ vendorToReject?.name }}'s vendor request. This will be sent to the vendor.
                  </p>
                  <div>
                    <label for="rejection-reason" class="block text-sm font-medium text-gray-700 mb-1">Rejection Reason</label>
                    <textarea
                      id="rejection-reason"
                      v-model="rejectionReason"
                      rows="4"
                      class="w-full px-3 py-2 border border-gray-300 rounded-sm focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-red-500"
                      placeholder="Please explain why this vendor request is being rejected..."
                    ></textarea>
                  </div>
                </div>
                <div class="flex justify-end space-x-3 p-6 border-t">
                  <button
                    @click="closeRejectModal"
                    class="px-4 py-2 bg-white border border-gray-300 rounded-sm text-sm font-medium text-gray-700 hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    @click="confirmReject"
                    class="px-4 py-2 bg-red-600 border border-transparent rounded-sm text-sm font-medium text-white hover:bg-red-700"
                  >
                    Reject Request
                  </button>
                </div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { ENDPOINTS } from '@/utils/api';
import type { ApiVendor } from '@/types/vendor';
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue';

interface ApiResponse {
  message?: string;
  vendor?: ApiVendor;
  [key: string]: any;
}

definePageMeta({
  layout: 'dashboard',
});

const httpClient = useHttpClient();
const runtimeConfig = useRuntimeConfig();
const { $toast }: any = useNuxtApp();

const vendorRequests = ref<ApiVendor[]>([]);
const loading = ref<boolean>(true);
const searchQuery = ref<string>('');
const currentPage = ref<number>(1);
const itemsPerPage = ref<number>(10);
const selectedVendor = ref<ApiVendor | null>(null);

const filteredVendorRequests = computed(() => {
  if (!searchQuery.value) {
    return vendorRequests.value;
  }

  const query = searchQuery.value.toLowerCase();
  return vendorRequests.value.filter(
    (vendor) =>
      vendor.name.toLowerCase().includes(query) ||
      vendor.user?.name.toLowerCase().includes(query) ||
      vendor.business_email.toLowerCase().includes(query) ||
      vendor.services.some((service) => service.service.name.toLowerCase().includes(query))
  );
});

const totalPages = computed(() => {
  return Math.ceil(filteredVendorRequests.value.length / itemsPerPage.value);
});

const fetchVendorRequests = async () => {
  loading.value = true;
  try {
    const response = await httpClient.get<any>(ENDPOINTS.VENDORS.PENDING);
    if (response) {
      if (response.data) {
        vendorRequests.value = response.data;
      } else if (Array.isArray(response)) {
        vendorRequests.value = response;
      } else {
        vendorRequests.value = response.vendors || response.requests || [];
      }
    }
  } catch (error: any) {
    console.error('Error fetching vendor requests:', error);
    $toast.error(error.message || 'Failed to fetch vendor requests');
  } finally {
    loading.value = false;
  }
};

const approveVendor = async (vendor: ApiVendor) => {
  try {
    const response = await httpClient.post<ApiResponse>(`${ENDPOINTS.VENDORS.APPROVE}/${vendor.id}`);
    if (response) {
      const message = response.message || `${vendor.name} has been approved as a vendor`;
      $toast.success(message);

      const index = vendorRequests.value.findIndex((v) => v.id === vendor.id);
      if (index !== -1) {
        vendorRequests.value[index].status = 'approved';
      }

      selectedVendor.value = null;

      await fetchVendorRequests();
    }
  } catch (error: any) {
    console.error('Error approving vendor:', error);
    const errorMessage = error.message || 'Failed to approve vendor';
    $toast.error(errorMessage);
  }
};

const rejectionReason = ref<string>('');
const showRejectionModal = ref<boolean>(false);
const vendorToReject = ref<ApiVendor | null>(null);

const openRejectModal = (vendor: ApiVendor) => {
  vendorToReject.value = vendor;
  rejectionReason.value = '';
  showRejectionModal.value = true;
};

const closeRejectModal = () => {
  showRejectionModal.value = false;
  vendorToReject.value = null;
  rejectionReason.value = '';
};

const rejectVendor = async (vendor: ApiVendor) => {
  openRejectModal(vendor);
};

const confirmReject = async () => {
  if (!vendorToReject.value) return;

  if (!rejectionReason.value.trim()) {
    $toast.error('Rejection reason is required');
    return;
  }

  try {
    const response = await httpClient.post<ApiResponse>(`${ENDPOINTS.VENDORS.REJECT}/${vendorToReject.value.id}`, {
      rejection_reason: rejectionReason.value
    });

    if (response) {
      const message = response.message || `${vendorToReject.value.name}'s vendor request has been rejected`;
      $toast.success(message);

      const index = vendorRequests.value.findIndex((v) => v.id === vendorToReject.value?.id);
      if (index !== -1) {
        vendorRequests.value[index].status = 'rejected';
      }

      closeRejectModal();

      selectedVendor.value = null;

      await fetchVendorRequests();
    }
  } catch (error: any) {
    console.error('Error rejecting vendor:', error);
    const errorMessage = error.message || 'Failed to reject vendor';
    $toast.error(errorMessage);
  }
};

const viewVendorDetails = (vendor: ApiVendor) => {
  selectedVendor.value = vendor;
};

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date);
};

onMounted(() => {
  fetchVendorRequests();
});
</script>
