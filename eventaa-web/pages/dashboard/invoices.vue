<template>
  <div class="p-6 bg-gray-100">
    <div class="mb-6 flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold">Invoices</h1>
        <p class="text-gray-500">Manage and track all invoices</p>
      </div>
      <div class="flex space-x-2">
        <button class="px-4 py-2 bg-white border border-gray-300 rounded shadow-sm hover:bg-gray-50 flex items-center">
          <Icon icon="heroicons:arrow-down-tray" class="w-5 h-5 text-gray-600 mr-1" />
          Export
        </button>
        <button class="px-4 py-2 bg-red-600 text-white rounded shadow-sm hover:bg-red-700 flex items-center">
          <Icon icon="heroicons:plus" class="w-5 h-5 mr-1" />
          Create Invoice
        </button>
      </div>
    </div>

    <!-- Filters -->
    <div class="bg-white p-4 shadow mb-6">
      <div class="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-4">
        <div class="flex-1">
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Icon icon="heroicons:magnifying-glass" class="h-5 w-5 text-gray-400" />
            </div>
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search by invoice number, customer, or description"
              class="block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:border-red-500 focus:ring-red-500"
            />
          </div>
        </div>
        <div>
          <select 
            v-model="statusFilter"
            class="border-gray-300 rounded-md shadow-sm focus:border-red-500 focus:ring-red-500"
          >
            <option value="all">All Statuses</option>
            <option value="paid">Paid</option>
            <option value="pending">Pending</option>
            <option value="overdue">Overdue</option>
            <option value="cancelled">Cancelled</option>
          </select>
        </div>
        <div>
          <select 
            v-model="dateFilter"
            class="border-gray-300 rounded-md shadow-sm focus:border-red-500 focus:ring-red-500"
          >
            <option value="all">All Time</option>
            <option value="today">Today</option>
            <option value="week">This Week</option>
            <option value="month">This Month</option>
            <option value="year">This Year</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Invoices Table -->
    <div class="bg-white shadow">
      <div v-if="loading" class="flex justify-center items-center py-20">
        <CoreLoader />
      </div>
      <div v-else>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead>
              <tr>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice #</th>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Issue Date</th>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due Date</th>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="invoice in filteredInvoices" :key="invoice.id" class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {{ invoice.invoiceNumber }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10">
                      <img class="h-10 w-10 rounded-full" :src="invoice.customer.avatar || 'https://via.placeholder.com/40'" alt="" />
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium text-gray-900">{{ invoice.customer.name }}</div>
                      <div class="text-sm text-gray-500">{{ invoice.customer.email }}</div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4">
                  <div class="text-sm text-gray-900">{{ invoice.description }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ formatCurrency(invoice.amount) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ formatDate(invoice.issueDate) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ formatDate(invoice.dueDate) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full" 
                    :class="{
                      'bg-green-100 text-green-800': invoice.status === 'paid',
                      'bg-yellow-100 text-yellow-800': invoice.status === 'pending',
                      'bg-red-100 text-red-800': invoice.status === 'overdue',
                      'bg-gray-100 text-gray-800': invoice.status === 'cancelled'
                    }">
                    {{ formatStatus(invoice.status) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div class="flex space-x-2">
                    <button @click="viewInvoice(invoice)" class="text-gray-600 hover:text-gray-900">
                      <Icon icon="heroicons:eye" class="w-5 h-5" />
                    </button>
                    <button 
                      v-if="invoice.status === 'pending' || invoice.status === 'overdue'"
                      @click="markAsPaid(invoice)" 
                      class="text-green-600 hover:text-green-900"
                    >
                      <Icon icon="heroicons:check-circle" class="w-5 h-5" />
                    </button>
                    <button 
                      v-if="invoice.status === 'pending'"
                      @click="cancelInvoice(invoice)" 
                      class="text-red-600 hover:text-red-900"
                    >
                      <Icon icon="heroicons:x-circle" class="w-5 h-5" />
                    </button>
                    <button @click="downloadInvoice(invoice)" class="text-blue-600 hover:text-blue-900">
                      <Icon icon="heroicons:document-arrow-down" class="w-5 h-5" />
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
          <div class="flex-1 flex justify-between sm:hidden">
            <button @click="prevPage" :disabled="currentPage === 1" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
              Previous
            </button>
            <button @click="nextPage" :disabled="currentPage === totalPages" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
              Next
            </button>
          </div>
          <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p class="text-sm text-gray-700">
                Showing <span class="font-medium">{{ paginationStart }}</span> to <span class="font-medium">{{ paginationEnd }}</span> of <span class="font-medium">{{ totalItems }}</span> invoices
              </p>
            </div>
            <div>
              <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button @click="prevPage" :disabled="currentPage === 1" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                  <span class="sr-only">Previous</span>
                  <Icon icon="heroicons:chevron-left" class="h-5 w-5" />
                </button>
                <button v-for="page in displayedPages" :key="page" @click="goToPage(page)" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium" :class="page === currentPage ? 'z-10 bg-red-50 border-red-500 text-red-600' : 'text-gray-500 hover:bg-gray-50'">
                  {{ page }}
                </button>
                <button @click="nextPage" :disabled="currentPage === totalPages" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                  <span class="sr-only">Next</span>
                  <Icon icon="heroicons:chevron-right" class="h-5 w-5" />
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { ENDPOINTS } from '@/utils/api';

definePageMeta({
  layout: "dashboard",
});

const loading = ref(true);
const searchQuery = ref('');
const statusFilter = ref('all');
const dateFilter = ref('all');
const currentPage = ref(1);
const itemsPerPage = 10;
const totalItems = ref(0);
const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

// Mock data - would be replaced with API calls
const invoices = ref([
  {
    id: 1,
    invoiceNumber: 'INV-2024-001',
    customer: {
      name: 'John Doe',
      email: '<EMAIL>',
      avatar: 'https://randomuser.me/api/portraits/men/1.jpg'
    },
    description: 'Summer Music Festival Tickets (5x VIP)',
    amount: 750.00,
    issueDate: '2024-05-01',
    dueDate: '2024-05-15',
    status: 'paid'
  },
  {
    id: 2,
    invoiceNumber: 'INV-2024-002',
    customer: {
      name: 'Jane Smith',
      email: '<EMAIL>',
      avatar: 'https://randomuser.me/api/portraits/women/2.jpg'
    },
    description: 'Tech Conference 2024 Sponsorship',
    amount: 2500.00,
    issueDate: '2024-05-02',
    dueDate: '2024-05-16',
    status: 'pending'
  },
  {
    id: 3,
    invoiceNumber: 'INV-2024-003',
    customer: {
      name: 'Robert Johnson',
      email: '<EMAIL>',
      avatar: 'https://randomuser.me/api/portraits/men/3.jpg'
    },
    description: 'Food & Wine Expo Vendor Booth',
    amount: 1200.00,
    issueDate: '2024-04-15',
    dueDate: '2024-04-29',
    status: 'overdue'
  },
  {
    id: 4,
    invoiceNumber: 'INV-2024-004',
    customer: {
      name: 'Emily Davis',
      email: '<EMAIL>',
      avatar: 'https://randomuser.me/api/portraits/women/4.jpg'
    },
    description: 'Annual Subscription - Professional Plan',
    amount: 959.88,
    issueDate: '2024-05-04',
    dueDate: '2024-05-18',
    status: 'paid'
  },
  {
    id: 5,
    invoiceNumber: 'INV-2024-005',
    customer: {
      name: 'Michael Wilson',
      email: '<EMAIL>',
      avatar: 'https://randomuser.me/api/portraits/men/5.jpg'
    },
    description: 'Photography Services - Corporate Event',
    amount: 850.00,
    issueDate: '2024-05-05',
    dueDate: '2024-05-19',
    status: 'cancelled'
  }
]);

const filteredInvoices = computed(() => {
  let result = [...invoices.value];
  
  // Apply search
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(i => 
      i.invoiceNumber.toLowerCase().includes(query) || 
      i.customer.name.toLowerCase().includes(query) || 
      i.customer.email.toLowerCase().includes(query) ||
      i.description.toLowerCase().includes(query)
    );
  }
  
  // Apply status filter
  if (statusFilter.value !== 'all') {
    result = result.filter(i => i.status === statusFilter.value);
  }
  
  // Apply date filter
  if (dateFilter.value !== 'all') {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    
    switch (dateFilter.value) {
      case 'today':
        result = result.filter(i => new Date(i.issueDate) >= today);
        break;
      case 'week':
        const weekStart = new Date(today);
        weekStart.setDate(today.getDate() - today.getDay());
        result = result.filter(i => new Date(i.issueDate) >= weekStart);
        break;
      case 'month':
        const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
        result = result.filter(i => new Date(i.issueDate) >= monthStart);
        break;
      case 'year':
        const yearStart = new Date(today.getFullYear(), 0, 1);
        result = result.filter(i => new Date(i.issueDate) >= yearStart);
        break;
    }
  }
  
  totalItems.value = result.length;
  
  // Apply pagination
  const start = (currentPage.value - 1) * itemsPerPage;
  const end = start + itemsPerPage;
  return result.slice(start, end);
});

const totalPages = computed(() => Math.ceil(totalItems.value / itemsPerPage));

const paginationStart = computed(() => {
  if (totalItems.value === 0) return 0;
  return (currentPage.value - 1) * itemsPerPage + 1;
});

const paginationEnd = computed(() => {
  return Math.min(currentPage.value * itemsPerPage, totalItems.value);
});

const displayedPages = computed(() => {
  const pages = [];
  const maxPagesToShow = 5;
  
  if (totalPages.value <= maxPagesToShow) {
    for (let i = 1; i <= totalPages.value; i++) {
      pages.push(i);
    }
  } else {
    const leftSide = Math.floor(maxPagesToShow / 2);
    const rightSide = maxPagesToShow - leftSide - 1;
    
    if (currentPage.value > leftSide && currentPage.value < totalPages.value - rightSide) {
      // Middle case
      for (let i = currentPage.value - leftSide; i <= currentPage.value + rightSide; i++) {
        pages.push(i);
      }
    } else if (currentPage.value <= leftSide) {
      // Near the start
      for (let i = 1; i <= maxPagesToShow; i++) {
        pages.push(i);
      }
    } else {
      // Near the end
      for (let i = totalPages.value - maxPagesToShow + 1; i <= totalPages.value; i++) {
        pages.push(i);
      }
    }
  }
  
  return pages;
});

function formatCurrency(amount, currency = 'USD') {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency
  }).format(amount);
}

function formatDate(dateString) {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
}

function formatStatus(status) {
  return status.charAt(0).toUpperCase() + status.slice(1);
}

function prevPage() {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
}

function nextPage() {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
}

function goToPage(page) {
  currentPage.value = page;
}

function viewInvoice(invoice) {
  // Implement view invoice functionality
  console.log('View invoice:', invoice);
  $toast.info(`Viewing invoice: ${invoice.invoiceNumber}`);
}

function markAsPaid(invoice) {
  // Implement mark as paid functionality
  console.log('Mark invoice as paid:', invoice);
  invoice.status = 'paid';
  $toast.success(`Invoice ${invoice.invoiceNumber} marked as paid`);
}

function cancelInvoice(invoice) {
  // Implement cancel invoice functionality
  console.log('Cancel invoice:', invoice);
  invoice.status = 'cancelled';
  $toast.success(`Invoice ${invoice.invoiceNumber} has been cancelled`);
}

function downloadInvoice(invoice) {
  // Implement invoice download functionality
  console.log('Download invoice:', invoice);
  $toast.success(`Invoice ${invoice.invoiceNumber} is being downloaded`);
}

watch([searchQuery, statusFilter, dateFilter], () => {
  currentPage.value = 1; // Reset to first page when filters change
});

// Simulate API call
onMounted(() => {
  setTimeout(() => {
    loading.value = false;
  }, 1000);
});
</script>
