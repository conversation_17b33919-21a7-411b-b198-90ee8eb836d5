<template>
  <div class="p-6 bg-gray-100">
    <div class="mb-6">
      <h1 class="text-2xl font-bold">Analytics</h1>
      <p class="text-gray-500">Track your events performance and user engagement</p>
    </div>

    <div v-if="loading" class="flex justify-center items-center h-64">
      <CoreLoader />
    </div>
    <div v-else>
      <!-- Date Range Selector -->
      <div class="bg-white p-4 shadow mb-6">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
          <div class="mb-4 md:mb-0">
            <h2 class="font-semibold">Date Range</h2>
            <p class="text-sm text-gray-500">Select a time period to analyze</p>
          </div>
          <div class="flex items-center space-x-2">
            <button
              v-for="period in periods"
              :key="period.value"
              @click="selectedPeriod = period.value"
              class="px-3 py-1 text-sm rounded"
              :class="selectedPeriod === period.value ? 'bg-red-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'"
            >
              {{ period.label }}
            </button>
            <button class="px-3 py-1 text-sm bg-gray-200 text-gray-700 hover:bg-gray-300 rounded flex items-center">
              <Icon icon="heroicons:calendar" class="w-4 h-4 mr-1" />
              Custom
            </button>
          </div>
        </div>
      </div>

      <!-- Stats Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div class="bg-white p-4 shadow">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-blue-100 mr-4">
              <Icon icon="heroicons:ticket" class="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <p class="text-sm text-gray-500">Total Tickets Sold</p>
              <p class="text-2xl font-bold">{{ stats.ticketsSold }}</p>
              <div class="flex items-center text-sm">
                <span :class="stats.ticketsGrowth >= 0 ? 'text-green-600' : 'text-red-600'">
                  <Icon :icon="stats.ticketsGrowth >= 0 ? 'heroicons:arrow-up' : 'heroicons:arrow-down'" class="w-4 h-4 inline" />
                  {{ Math.abs(stats.ticketsGrowth) }}%
                </span>
                <span class="text-gray-500 ml-1">vs previous period</span>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-white p-4 shadow">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-green-100 mr-4">
              <Icon icon="heroicons:currency-dollar" class="w-6 h-6 text-green-600" />
            </div>
            <div>
              <p class="text-sm text-gray-500">Revenue</p>
              <p class="text-2xl font-bold">${{ stats.revenue.toFixed(2) }}</p>
              <div class="flex items-center text-sm">
                <span :class="stats.revenueGrowth >= 0 ? 'text-green-600' : 'text-red-600'">
                  <Icon :icon="stats.revenueGrowth >= 0 ? 'heroicons:arrow-up' : 'heroicons:arrow-down'" class="w-4 h-4 inline" />
                  {{ Math.abs(stats.revenueGrowth) }}%
                </span>
                <span class="text-gray-500 ml-1">vs previous period</span>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-white p-4 shadow">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-purple-100 mr-4">
              <Icon icon="heroicons:users" class="w-6 h-6 text-purple-600" />
            </div>
            <div>
              <p class="text-sm text-gray-500">Total Attendees</p>
              <p class="text-2xl font-bold">{{ stats.attendees }}</p>
              <div class="flex items-center text-sm">
                <span :class="stats.attendeesGrowth >= 0 ? 'text-green-600' : 'text-red-600'">
                  <Icon :icon="stats.attendeesGrowth >= 0 ? 'heroicons:arrow-up' : 'heroicons:arrow-down'" class="w-4 h-4 inline" />
                  {{ Math.abs(stats.attendeesGrowth) }}%
                </span>
                <span class="text-gray-500 ml-1">vs previous period</span>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-white p-4 shadow">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-yellow-100 mr-4">
              <Icon icon="heroicons:eye" class="w-6 h-6 text-yellow-600" />
            </div>
            <div>
              <p class="text-sm text-gray-500">Page Views</p>
              <p class="text-2xl font-bold">{{ stats.pageViews }}</p>
              <div class="flex items-center text-sm">
                <span :class="stats.pageViewsGrowth >= 0 ? 'text-green-600' : 'text-red-600'">
                  <Icon :icon="stats.pageViewsGrowth >= 0 ? 'heroicons:arrow-up' : 'heroicons:arrow-down'" class="w-4 h-4 inline" />
                  {{ Math.abs(stats.pageViewsGrowth) }}%
                </span>
                <span class="text-gray-500 ml-1">vs previous period</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Charts Section -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <div class="bg-white p-4 shadow">
          <h2 class="font-semibold mb-4">Ticket Sales Over Time</h2>
          <div class="h-64">
            <!-- Chart would go here -->
            <div class="flex items-center justify-center h-full text-gray-400">
              <p>Chart visualization will be implemented here</p>
            </div>
          </div>
        </div>

        <div class="bg-white p-4 shadow">
          <h2 class="font-semibold mb-4">Revenue Breakdown</h2>
          <div class="h-64">
            <!-- Chart would go here -->
            <div class="flex items-center justify-center h-full text-gray-400">
              <p>Chart visualization will be implemented here</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Top Events Table -->
      <div class="bg-white p-4 shadow mb-6">
        <h2 class="font-semibold mb-4">Top Performing Events</h2>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead>
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Event</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tickets Sold</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Revenue</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Conversion Rate</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="event in topEvents" :key="event.id">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10">
                      <img class="h-10 w-10 rounded-full" :src="event.image" alt="" />
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium text-gray-900">{{ event.title }}</div>
                      <div class="text-sm text-gray-500">{{ formatDate(event.start) }}</div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ event.tickets_sold }}</div>
                  <div class="text-sm text-gray-500">of {{ event.total_tickets }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">${{ event.revenue.toFixed(2) }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ Math.round(event.conversion_rate) }}%</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                    :class="{
                      'bg-green-100 text-green-800': event.status === 'Active',
                      'bg-yellow-100 text-yellow-800': event.status === 'Upcoming',
                      'bg-gray-100 text-gray-800': event.status === 'Ended'
                    }">
                    {{ event.status }}
                  </span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { ENDPOINTS } from '@/utils/api';

definePageMeta({
  layout: "dashboard",
});

const loading = ref(true);
const selectedPeriod = ref('7d');
const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

const periods = [
  { label: '7 Days', value: '7d' },
  { label: '30 Days', value: '30d' },
  { label: '90 Days', value: '90d' },
  { label: 'This Year', value: 'year' },
];

interface AnalyticsStats {
  ticketsSold: number;
  ticketsGrowth: number;
  revenue: number;
  revenueGrowth: number;
  attendees: number;
  attendeesGrowth: number;
  pageViews: number;
  pageViewsGrowth: number;
}

interface TopEvent {
  id: number;
  title: string;
  start: string;
  image?: string;
  tickets_sold: number;
  total_tickets: number;
  revenue: number;
  conversion_rate: number;
  status: string;
}

const stats = ref<AnalyticsStats>({
  ticketsSold: 0,
  ticketsGrowth: 0,
  revenue: 0,
  revenueGrowth: 0,
  attendees: 0,
  attendeesGrowth: 0,
  pageViews: 0,
  pageViewsGrowth: 0
});

const topEvents = ref<TopEvent[]>([]);

const fetchAnalyticsStats = async () => {
  try {
    const response = await httpClient.get<AnalyticsStats>(`${ENDPOINTS.ANALYTICS.STATS}?period=${selectedPeriod.value}`);
    stats.value = response;
  } catch (error) {
    console.error('Error fetching analytics stats:', error);
    $toast.error('Failed to load analytics statistics');
  }
};

const fetchTopEvents = async () => {
  try {
    const response = await httpClient.get<TopEvent[]>(`${ENDPOINTS.ANALYTICS.EVENTS}?period=${selectedPeriod.value}`);
    topEvents.value = response;
  } catch (error) {
    console.error('Error fetching top events:', error);
    $toast.error('Failed to load top events');
  }
};

const fetchAnalyticsData = async () => {
  loading.value = true;
  try {
    await Promise.all([
      fetchAnalyticsStats(),
      fetchTopEvents()
    ]);
  } catch (error) {
    console.error('Error fetching analytics data:', error);
  } finally {
    loading.value = false;
  }
};

watch(selectedPeriod, () => {
  fetchAnalyticsData();
});

onMounted(async () => {
  await fetchAnalyticsData();
});

const formatDate = (dateString: string): string => {
  if (!dateString) return '';
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};
</script>
