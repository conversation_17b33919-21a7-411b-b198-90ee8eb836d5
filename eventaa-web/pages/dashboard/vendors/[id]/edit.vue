<template>
  <div class="p-6 bg-gray-100 dark:bg-gray-900 min-h-screen">
    <div class="mb-6 flex justify-between items-center">
      <div class="flex items-center space-x-4">
        <button @click="$router.back()" class="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200">
          <Icon icon="heroicons:arrow-left" class="w-6 h-6" />
        </button>
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Edit Vendor</h1>
          <p class="text-gray-500 dark:text-gray-400">Update vendor information</p>
        </div>
      </div>
    </div>

    <div v-if="loading" class="flex justify-center items-center py-20">
      <CoreLoader />
    </div>

    <div v-else-if="!vendor" class="flex flex-col justify-center items-center py-20">
      <Icon icon="heroicons:exclamation-triangle" class="w-16 h-16 text-gray-400 dark:text-gray-600 mb-4" />
      <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Vendor not found</h3>
      <p class="text-gray-500 dark:text-gray-400 text-center max-w-md">
        The vendor you're trying to edit doesn't exist or has been removed.
      </p>
    </div>

    <form v-else @submit.prevent="submitForm" class="space-y-6">
      <!-- Basic Information -->
      <div class="bg-white dark:bg-gray-800 shadow overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">Basic Information</h3>
        </div>
        <div class="px-6 py-4 space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Business Name *</label>
              <input
                id="name"
                v-model="form.name"
                type="text"
                required
                class="block w-full border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm focus:border-red-500 focus:ring-red-500"
                placeholder="Enter business name"
              />
            </div>
            <div>
              <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Business Email *</label>
              <input
                id="email"
                v-model="form.business_email"
                type="email"
                required
                class="block w-full border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm focus:border-red-500 focus:ring-red-500"
                placeholder="Enter business email"
              />
            </div>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Phone Number *</label>
              <input
                id="phone"
                v-model="form.phone"
                type="tel"
                required
                class="block w-full border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm focus:border-red-500 focus:ring-red-500"
                placeholder="Enter phone number"
              />
            </div>
            <div>
              <label for="website" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Website</label>
              <input
                id="website"
                v-model="form.website"
                type="url"
                class="block w-full border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm focus:border-red-500 focus:ring-red-500"
                placeholder="https://example.com"
              />
            </div>
          </div>

          <div>
            <label for="location" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Location *</label>
            <input
              id="location"
              v-model="form.location"
              type="text"
              required
              class="block w-full border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm focus:border-red-500 focus:ring-red-500"
              placeholder="Enter business location"
            />
          </div>

          <div>
            <label for="bio" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Description</label>
            <textarea
              id="bio"
              v-model="form.bio"
              rows="4"
              class="block w-full border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm focus:border-red-500 focus:ring-red-500"
              placeholder="Describe your business and services"
            ></textarea>
          </div>
        </div>
      </div>

      <!-- Status Management (Admin Only) -->
      <div class="bg-white dark:bg-gray-800 shadow overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">Status Management</h3>
        </div>
        <div class="px-6 py-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Status</label>
              <div class="relative">
                <select
                  id="status"
                  v-model="form.status"
                  class="block w-full border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm focus:border-red-500 focus:ring-red-500 appearance-none pr-10"
                >
                  <option value="pending">Pending</option>
                  <option value="approved">Approved</option>
                  <option value="rejected">Rejected</option>
                  <option value="suspended">Suspended</option>
                </select>
                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                  <Icon icon="heroicons:chevron-up-down" class="h-5 w-5 text-gray-400 dark:text-gray-500" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="flex justify-end space-x-4">
        <button
          type="button"
          @click="$router.back()"
          class="px-6 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
        >
          Cancel
        </button>
        <button
          type="submit"
          :disabled="submitting"
          class="px-6 py-2 bg-red-600 text-white text-sm font-medium hover:bg-red-700 disabled:opacity-50 flex items-center"
        >
          <Icon v-if="submitting" icon="heroicons:arrow-path" class="w-4 h-4 mr-2 animate-spin" />
          {{ submitting ? 'Updating...' : 'Update Vendor' }}
        </button>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { ENDPOINTS } from '@/utils/api';
import type { ApiVendor } from '~/types/vendor';

definePageMeta({
  layout: "dashboard",
});

const route = useRoute();
const router = useRouter();
const loading = ref(true);
const submitting = ref(false);
const vendor = ref<ApiVendor | null>(null);
const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

const vendorId = computed(() => route.params.id as string);

const form = ref({
  name: '',
  business_email: '',
  phone: '',
  website: '',
  location: '',
  bio: '',
  status: 'pending' as 'pending' | 'approved' | 'rejected' | 'suspended'
});

const fetchVendor = async () => {
  try {
    loading.value = true;
    const response = await httpClient.get<ApiVendor>(`${ENDPOINTS.VENDORS.READ}/${vendorId.value}`);
    vendor.value = response;
    
    // Populate form
    form.value = {
      name: response.name || '',
      business_email: response.business_email || '',
      phone: response.phone || '',
      website: response.website || '',
      location: response.location || '',
      bio: response.bio || '',
      status: response.status || 'pending'
    };
  } catch (error) {
    console.error('Error fetching vendor:', error);
    $toast.error('Failed to load vendor details');
    vendor.value = null;
  } finally {
    loading.value = false;
  }
};

const submitForm = async () => {
  try {
    submitting.value = true;
    
    await httpClient.post(`${ENDPOINTS.VENDORS.UPDATE}/${vendor.value?.id}`, form.value);
    
    $toast.success('Vendor updated successfully');
    router.push(`/dashboard/vendors/${vendorId.value}`);
  } catch (error: any) {
    console.error('Error updating vendor:', error);
    $toast.error(error.response?.data?.message || 'Failed to update vendor');
  } finally {
    submitting.value = false;
  }
};

onMounted(() => {
  fetchVendor();
});
</script>
