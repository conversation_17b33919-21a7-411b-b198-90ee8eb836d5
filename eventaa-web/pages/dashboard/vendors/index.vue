<template>
  <div class="p-6 bg-zinc-100 dark:bg-zinc-900 min-h-screen">
    <div class="mb-6 flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Vendors Management</h1>
        <p class="text-gray-500 dark:text-gray-400">Manage vendors, approvals, and services</p>
      </div>
      <div class="flex space-x-2">
        <button @click="exportVendors" :disabled="loading" class="px-4 py-2 bg-white dark:bg-zinc-800 border border-zinc-300 dark:border-zinc-600 shadow-sm hover:bg-zinc-50 dark:hover:bg-zinc-700 flex items-center text-gray-700 dark:text-gray-300 disabled:opacity-50">
          <Icon icon="mdi:microsoft-excel" class="w-5 h-5 text-green-600 mr-1" />
          Export
        </button>
        <button @click="navigateTo('/dashboard/vendors/create')" class="px-4 py-2 bg-red-600 text-white shadow-sm hover:bg-red-700 flex items-center">
          <Icon icon="heroicons:plus" class="w-5 h-5 mr-1" />
          Add Vendor
        </button>
      </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <div class="bg-white dark:bg-zinc-800 p-4 shadow">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900 mr-4">
            <Icon icon="heroicons:building-storefront" class="w-6 h-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <p class="text-sm text-gray-500 dark:text-gray-400">Total Vendors</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ stats.total_vendors }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-zinc-800 p-4 shadow">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-yellow-100 dark:bg-yellow-900 mr-4">
            <Icon icon="heroicons:clock" class="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
          </div>
          <div>
            <p class="text-sm text-gray-500 dark:text-gray-400">Pending Approval</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ stats.pending_approval }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-zinc-800 p-4 shadow">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-green-100 dark:bg-green-900 mr-4">
            <Icon icon="heroicons:check-circle" class="w-6 h-6 text-green-600 dark:text-green-400" />
          </div>
          <div>
            <p class="text-sm text-gray-500 dark:text-gray-400">Approved</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ stats.approved_vendors }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-zinc-800 p-4 shadow">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-purple-100 dark:bg-purple-900 mr-4">
            <Icon icon="heroicons:currency-dollar" class="w-6 h-6 text-purple-600 dark:text-purple-400" />
          </div>
          <div>
            <p class="text-sm text-gray-500 dark:text-gray-400">Total Revenue</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">${{ formatCurrency(stats.total_revenue) }}</p>
          </div>
        </div>
      </div>
    </div>

    <div class="bg-white dark:bg-zinc-800 p-4 shadow mb-6">
      <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4">
        <div class="flex-1">
          <label for="status-filter" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Status</label>
          <div class="relative">
            <select id="status-filter" v-model="filters.status" class="w-full border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-gray-900 dark:text-white shadow-sm focus:border-red-500 focus:ring-red-500 appearance-none pr-10">
              <option value="">All Statuses</option>
              <option value="pending">Pending</option>
              <option value="approved">Approved</option>
              <option value="rejected">Rejected</option>
              <option value="suspended">Suspended</option>
            </select>
            <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
              <Icon icon="heroicons:chevron-up-down" class="h-5 w-5 text-gray-400 dark:text-gray-500" />
            </div>
          </div>
        </div>
        <div class="flex-1">
          <label for="category-filter" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Category</label>
          <div class="relative">
            <select id="category-filter" v-model="filters.category" class="w-full border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-gray-900 dark:text-white shadow-sm focus:border-red-500 focus:ring-red-500 appearance-none pr-10">
              <option value="">All Categories</option>
              <option v-for="category in categories" :key="category.id" :value="category.id">{{ category.name }}</option>
            </select>
            <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
              <Icon icon="heroicons:chevron-up-down" class="h-5 w-5 text-gray-400 dark:text-gray-500" />
            </div>
          </div>
        </div>
        <div class="flex-1">
          <label for="location-filter" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Location</label>
          <div class="relative">
            <select id="location-filter" v-model="filters.location" class="w-full border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-gray-900 dark:text-white shadow-sm focus:border-red-500 focus:ring-red-500 appearance-none pr-10">
              <option value="">All Locations</option>
              <option v-for="location in locations" :key="location" :value="location">{{ location }}</option>
            </select>
            <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
              <Icon icon="heroicons:chevron-up-down" class="h-5 w-5 text-gray-400 dark:text-gray-500" />
            </div>
          </div>
        </div>
        <div class="flex-1">
          <label for="search" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Search</label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Icon icon="heroicons:magnifying-glass" class="h-5 w-5 text-gray-400 dark:text-gray-500" />
            </div>
            <input
              id="search"
              v-model="searchQuery"
              type="text"
              placeholder="Search vendors..."
              class="block w-full pl-10 border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-gray-900 dark:text-white shadow-sm focus:border-red-500 focus:ring-red-500"
            />
          </div>
        </div>
      </div>
    </div>

    <div class="bg-white dark:bg-zinc-800 shadow">
      <div v-if="loading" class="flex justify-center items-center py-20">
        <CoreLoader />
      </div>
      <div v-else-if="vendors.length === 0" class="flex flex-col justify-center items-center py-20">
        <Icon icon="heroicons:building-storefront" class="w-16 h-16 text-gray-400 dark:text-gray-600 mb-4" />
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No vendors found</h3>
        <p class="text-gray-500 dark:text-gray-400 text-center max-w-md">
          No vendors match your current filters. Try adjusting your search criteria.
        </p>
      </div>
      <div v-else>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead>
              <tr>
                <th class="px-6 py-3 bg-zinc-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Vendor</th>
                <th class="px-6 py-3 bg-zinc-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Contact</th>
                <th class="px-6 py-3 bg-zinc-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Location</th>
                <th class="px-6 py-3 bg-zinc-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Rating</th>
                <th class="px-6 py-3 bg-zinc-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                <th class="px-6 py-3 bg-zinc-50 dark:bg-zinc-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-zinc-800 divide-y divide-gray-200 dark:divide-gray-700">
              <tr v-for="vendor in vendors" :key="vendor.id" class="hover:bg-zinc-50 dark:hover:bg-zinc-700">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10">
                      <img class="h-10 w-10 rounded-full border border-zinc-200 dark:border-zinc-700" :src="`${runtimeConfig.public.baseUrl}storage/${vendor.logo}`" :alt="vendor.name.toLowerCase().replaceAll(' ', '-')" />
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium text-gray-900 dark:text-white">{{ vendor.name }}</div>
                      <div class="text-sm text-gray-500 dark:text-gray-400">{{ vendor.services?.length || 0 }} services</div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900 dark:text-white">{{ vendor.business_email }}</div>
                  <div class="text-sm text-gray-500 dark:text-gray-400">{{ vendor.phone }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  {{ vendor.location }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <Icon icon="heroicons:star" class="w-4 h-4 text-yellow-400 mr-1" />
                    <span class="text-sm text-gray-900 dark:text-white">{{ vendor.ratings_avg_rating?.toFixed(1) || 'N/A' }}</span>
                    <span class="text-sm text-gray-500 dark:text-gray-400 ml-1">({{ vendor.ratings_count || 0 }})</span>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                    :class="{
                      'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200': vendor.status === 'pending',
                      'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200': vendor.status === 'approved',
                      'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200': vendor.status === 'rejected',
                      'bg-zinc-100 dark:bg-zinc-900 text-gray-800 dark:text-gray-200': String(vendor.status) === 'suspended'
                    }">
                    {{ formatStatus(String(vendor.status)) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div class="flex space-x-2">
                    <button @click="viewVendor(vendor)" class="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200" title="View Details">
                      <Icon icon="heroicons:eye" class="w-5 h-5" />
                    </button>
                    <button @click="editVendor(vendor)" class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-200" title="Edit">
                      <Icon icon="heroicons:pencil-square" class="w-5 h-5" />
                    </button>
                    <button v-if="vendor.status === 'pending'" @click="approveVendor(vendor)" class="text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-200" title="Approve">
                      <Icon icon="heroicons:check-circle" class="w-5 h-5" />
                    </button>
                    <button v-if="vendor.status === 'pending'" @click="rejectVendor(vendor)" class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-200" title="Reject">
                      <Icon icon="heroicons:x-circle" class="w-5 h-5" />
                    </button>
                    <button v-if="vendor.status === 'approved'" @click="showSuspendConfirmation(vendor)" class="text-orange-600 dark:text-orange-400 hover:text-orange-900 dark:hover:text-orange-200" title="Suspend">
                      <Icon icon="heroicons:pause-circle" class="w-5 h-5" />
                    </button>
                    <button v-if="vendor.status === 'suspended'" @click="reactivateVendor(vendor)" class="text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-200" title="Reactivate">
                      <Icon icon="heroicons:play-circle" class="w-5 h-5" />
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <div class="bg-white dark:bg-zinc-800 px-4 py-3 flex items-center justify-between border-t border-zinc-200 dark:border-zinc-700 sm:px-6">
          <div class="flex-1 flex justify-between sm:hidden">
            <button @click="prevPage" :disabled="currentPage === 1" class="relative inline-flex items-center px-4 py-2 border border-zinc-300 dark:border-zinc-600 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-zinc-700 hover:bg-zinc-50 dark:hover:bg-zinc-600 disabled:opacity-50">
              Previous
            </button>
            <button @click="nextPage" :disabled="currentPage === totalPages" class="ml-3 relative inline-flex items-center px-4 py-2 border border-zinc-300 dark:border-zinc-600 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-zinc-700 hover:bg-zinc-50 dark:hover:bg-zinc-600 disabled:opacity-50">
              Next
            </button>
          </div>
          <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p class="text-sm text-gray-700 dark:text-gray-300">
                Showing <span class="font-medium">{{ paginationStart }}</span> to <span class="font-medium">{{ paginationEnd }}</span> of <span class="font-medium">{{ totalItems }}</span> vendors
              </p>
            </div>
            <div>
              <nav class="relative z-0 inline-flex shadow-sm -space-x-px" aria-label="Pagination">
                <button @click="prevPage" :disabled="currentPage === 1" class="relative inline-flex items-center px-2 py-2 border border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-zinc-50 dark:hover:bg-zinc-600 disabled:opacity-50">
                  <span class="sr-only">Previous</span>
                  <Icon icon="heroicons:chevron-left" class="h-5 w-5" />
                </button>
                <button v-for="page in displayedPages" :key="page" @click="goToPage(page)" class="relative inline-flex items-center px-4 py-2 border border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-sm font-medium" :class="page === currentPage ? 'z-10 bg-red-50 dark:bg-red-900 border-red-500 text-red-600 dark:text-red-200' : 'text-gray-500 dark:text-gray-400 hover:bg-zinc-50 dark:hover:bg-zinc-600'">
                  {{ page }}
                </button>
                <button @click="nextPage" :disabled="currentPage === totalPages" class="relative inline-flex items-center px-2 py-2 border border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-zinc-50 dark:hover:bg-zinc-600 disabled:opacity-50">
                  <span class="sr-only">Next</span>
                  <Icon icon="heroicons:chevron-right" class="h-5 w-5" />
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Suspend Confirmation Dialog -->
    <TransitionRoot appear :show="showSuspendDialog" as="template">
      <Dialog as="div" @close="showSuspendDialog = false" class="relative z-50">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-25" />
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div class="flex min-h-full items-center justify-center p-4 text-center">
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel class="w-full max-w-md transform overflow-hidden bg-white dark:bg-gray-800 p-6 text-left align-middle shadow-xl transition-all">
                <div class="flex items-center mb-4">
                  <Icon icon="heroicons:exclamation-triangle" class="w-12 h-12 text-orange-500 mr-4" />
                  <div>
                    <DialogTitle as="h3" class="text-lg font-medium text-gray-900 dark:text-white">
                      Suspend Vendor
                    </DialogTitle>
                    <p class="text-sm text-gray-500 dark:text-gray-400">This action will suspend the vendor's account</p>
                  </div>
                </div>

                <div class="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg p-4 mb-4">
                  <div class="flex">
                    <Icon icon="heroicons:exclamation-triangle" class="w-5 h-5 text-orange-400 mr-2 mt-0.5" />
                    <div class="text-sm">
                      <p class="text-orange-800 dark:text-orange-200 font-medium">Warning</p>
                      <p class="text-orange-700 dark:text-orange-300 mt-1">
                        Suspending <strong>{{ vendorToSuspend?.name }}</strong> will:
                      </p>
                      <ul class="list-disc list-inside text-orange-700 dark:text-orange-300 mt-2 space-y-1">
                        <li>Hide their profile from public view</li>
                        <li>Prevent them from receiving new bookings</li>
                        <li>Disable their vendor dashboard access</li>
                        <li>Require manual reactivation by an admin</li>
                      </ul>
                    </div>
                  </div>
                </div>

                <div class="flex justify-end space-x-3">
                  <button
                    @click="showSuspendDialog = false"
                    class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600"
                  >
                    Cancel
                  </button>
                  <button
                    @click="confirmSuspendVendor"
                    :disabled="suspending"
                    class="px-4 py-2 text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 disabled:opacity-50 flex items-center"
                  >
                    <Icon v-if="suspending" icon="heroicons:arrow-path" class="w-4 h-4 mr-2 animate-spin" />
                    {{ suspending ? 'Suspending...' : 'Suspend Vendor' }}
                  </button>
                </div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue';
import { ENDPOINTS } from '@/utils/api';
import type { ApiVendor, ApiResponse } from '~/types/vendor';

definePageMeta({
  layout: "dashboard",
});

const runtimeConfig = useRuntimeConfig();
const loading = ref(true);
const searchQuery = ref('');
const currentPage = ref(1);
const itemsPerPage = 10;
const totalItems = ref(0);
const totalPages = computed(() => Math.ceil(totalItems.value / itemsPerPage));
const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

const filters = ref({
  status: '',
  category: '',
  location: '',
});

const vendors = ref<ApiVendor[]>([]);
const categories = ref<any[]>([]);
const locations = ref<string[]>([]);
const stats = ref({
  total_vendors: 0,
  pending_approval: 0,
  approved_vendors: 0,
  rejected_vendors: 0,
  suspended_vendors: 0,
  total_revenue: 0,
  total_bookings: 0,
});

// Suspend dialog state
const showSuspendDialog = ref(false);
const suspending = ref(false);
const vendorToSuspend = ref<ApiVendor | null>(null);

const paginationStart = computed(() => {
  if (totalItems.value === 0) return 0;
  return (currentPage.value - 1) * itemsPerPage + 1;
});

const paginationEnd = computed(() => {
  return Math.min(currentPage.value * itemsPerPage, totalItems.value);
});

const displayedPages = computed(() => {
  const pages = [];
  const maxPagesToShow = 5;

  if (totalPages.value <= maxPagesToShow) {
    for (let i = 1; i <= totalPages.value; i++) {
      pages.push(i);
    }
  } else {
    const leftSide = Math.floor(maxPagesToShow / 2);
    const rightSide = maxPagesToShow - leftSide - 1;

    if (currentPage.value > leftSide && currentPage.value < totalPages.value - rightSide) {
      for (let i = currentPage.value - leftSide; i <= currentPage.value + rightSide; i++) {
        pages.push(i);
      }
    } else if (currentPage.value <= leftSide) {
      for (let i = 1; i <= maxPagesToShow; i++) {
        pages.push(i);
      }
    } else {
      for (let i = totalPages.value - maxPagesToShow + 1; i <= totalPages.value; i++) {
        pages.push(i);
      }
    }
  }

  return pages;
});

const fetchVendors = async () => {
  try {
    loading.value = true;
    const params = new URLSearchParams();

    params.append('page', currentPage.value.toString());
    params.append('per_page', itemsPerPage.toString());
    params.append('admin', 'true');

    if (searchQuery.value.trim()) {
      params.append('search', searchQuery.value.trim());
    }
    if (filters.value.status) {
      params.append('status', filters.value.status);
    }
    if (filters.value.category) {
      params.append('category', filters.value.category);
    }
    if (filters.value.location) {
      params.append('location', filters.value.location);
    }

    const response = await httpClient.get<ApiResponse>(
      `${ENDPOINTS.VENDORS.GET_ALL}?${params.toString()}`
    );

    vendors.value = response.data || [];
    totalItems.value = response.total || 0;
    currentPage.value = response.current_page || 1;
  } catch (error) {
    console.error('Error fetching vendors:', error);
    $toast.error('Failed to load vendors');
    vendors.value = [];
    totalItems.value = 0;
  } finally {
    loading.value = false;
  }
};

const fetchCategories = async () => {
  try {
    const response = await httpClient.get<any[]>(`${ENDPOINTS.VENDORS.CATEGORIES}`);
    categories.value = response || [];
  } catch (error) {
    console.error('Error fetching categories:', error);
  }
};

const fetchLocations = async () => {
  try {
    const response = await httpClient.get<string[]>(`${ENDPOINTS.VENDORS.LOCATIONS}`);
    locations.value = response || [];
  } catch (error) {
    console.error('Error fetching locations:', error);
  }
};

const fetchStats = async () => {
  try {
    const response = await httpClient.get<any>(`${ENDPOINTS.VENDORS.STATS}`);
    stats.value = response || stats.value;
  } catch (error) {
    console.error('Error fetching stats:', error);
  }
};

const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-US').format(amount);
};

const formatStatus = (status: string): string => {
  switch (status) {
    case 'pending': return 'Pending';
    case 'approved': return 'Approved';
    case 'rejected': return 'Rejected';
    case 'suspended': return 'Suspended';
    default: return status;
  }
};

const viewVendor = (vendor: ApiVendor): void => {
  navigateTo(`/dashboard/vendors/${vendor.slug}`);
};

const editVendor = (vendor: ApiVendor): void => {
  navigateTo(`/dashboard/vendors/${vendor.id}/edit`);
};

const approveVendor = async (vendor: ApiVendor): Promise<void> => {
  try {
    await httpClient.post(`${ENDPOINTS.VENDORS.APPROVE}/${vendor.id}`);
    $toast.success(`${vendor.name} has been approved`);
    await Promise.all([fetchVendors(), fetchStats()]);
  } catch (error) {
    console.error('Error approving vendor:', error);
    $toast.error('Failed to approve vendor');
  }
};

const rejectVendor = async (vendor: ApiVendor): Promise<void> => {
  try {
    await httpClient.post(`${ENDPOINTS.VENDORS.REJECT}/${vendor.id}`);
    $toast.success(`${vendor.name} has been rejected`);
    await Promise.all([fetchVendors(), fetchStats()]);
  } catch (error) {
    console.error('Error rejecting vendor:', error);
    $toast.error('Failed to reject vendor');
  }
};

const showSuspendConfirmation = (vendor: ApiVendor): void => {
  vendorToSuspend.value = vendor;
  showSuspendDialog.value = true;
};

const confirmSuspendVendor = async (): Promise<void> => {
  if (!vendorToSuspend.value) return;

  try {
    suspending.value = true;
    await httpClient.post(`${ENDPOINTS.VENDORS.SUSPEND}/${vendorToSuspend.value.id}`);
    $toast.success(`${vendorToSuspend.value.name} has been suspended`);
    showSuspendDialog.value = false;
    vendorToSuspend.value = null;
    await Promise.all([fetchVendors(), fetchStats()]);
  } catch (error) {
    console.error('Error suspending vendor:', error);
    $toast.error('Failed to suspend vendor');
  } finally {
    suspending.value = false;
  }
};

const reactivateVendor = async (vendor: ApiVendor): Promise<void> => {
  try {
    await httpClient.post(`${ENDPOINTS.VENDORS.REACTIVATE}/${vendor.id}`);
    $toast.success(`${vendor.name} has been reactivated`);
    await Promise.all([fetchVendors(), fetchStats()]);
  } catch (error) {
    console.error('Error reactivating vendor:', error);
    $toast.error('Failed to reactivate vendor');
  }
};

const exportVendors = async (): Promise<void> => {
  try {
    const params = new URLSearchParams();

    if (searchQuery.value.trim()) {
      params.append('search', searchQuery.value.trim());
    }
    if (filters.value.status) {
      params.append('status', filters.value.status);
    }
    if (filters.value.category) {
      params.append('category', filters.value.category);
    }
    if (filters.value.location) {
      params.append('location', filters.value.location);
    }

    const response = await httpClient.get(`${ENDPOINTS.VENDORS.EXPORT}?${params.toString()}`, {
      responseType: 'blob'
    }) as Blob;

    const url = window.URL.createObjectURL(response);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', `vendors-${new Date().toISOString().split('T')[0]}.xlsx`);
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);

    $toast.success('Vendors exported successfully');
  } catch (error) {
    console.error('Error exporting vendors:', error);
    $toast.error('Failed to export vendors');
  }
};

const prevPage = (): void => {
  if (currentPage.value > 1) {
    currentPage.value--;
    fetchVendors();
  }
};

const nextPage = (): void => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
    fetchVendors();
  }
};

const goToPage = (page: number): void => {
  currentPage.value = page;
  fetchVendors();
};

let searchTimeout: NodeJS.Timeout;
watch(searchQuery, () => {
  clearTimeout(searchTimeout);
  searchTimeout = setTimeout(() => {
    currentPage.value = 1;
    fetchVendors();
  }, 300);
});

watch(filters, () => {
  currentPage.value = 1;
  fetchVendors();
}, { deep: true });

onMounted(async () => {
  await Promise.all([
    fetchCategories(),
    fetchLocations(),
    fetchStats(),
    fetchVendors()
  ]);
});
</script>
