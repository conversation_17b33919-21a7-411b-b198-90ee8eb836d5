<template>
  <div class="p-6 bg-gray-100 dark:bg-gray-900 min-h-screen">
    <div class="mb-6 flex justify-between items-center">
      <div class="flex items-center space-x-4">
        <button @click="$router.back()" class="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200">
          <Icon icon="heroicons:arrow-left" class="w-6 h-6" />
        </button>
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Add New Vendor</h1>
          <p class="text-gray-500 dark:text-gray-400">Create a new vendor profile</p>
        </div>
      </div>
    </div>

    <form @submit.prevent="submitForm" class="space-y-6">
      <!-- Basic Information -->
      <div class="bg-white dark:bg-gray-800 shadow overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">Basic Information</h3>
        </div>
        <div class="px-6 py-4 space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Business Name *</label>
              <input
                id="name"
                v-model="form.name"
                type="text"
                required
                class="block w-full border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm focus:border-red-500 focus:ring-red-500"
                placeholder="Enter business name"
              />
            </div>
            <div>
              <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Business Email *</label>
              <input
                id="email"
                v-model="form.business_email"
                type="email"
                required
                class="block w-full border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm focus:border-red-500 focus:ring-red-500"
                placeholder="Enter business email"
              />
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Phone Number *</label>
              <input
                id="phone"
                v-model="form.phone"
                type="tel"
                required
                class="block w-full border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm focus:border-red-500 focus:ring-red-500"
                placeholder="Enter phone number"
              />
            </div>
            <div>
              <label for="website" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Website</label>
              <input
                id="website"
                v-model="form.website"
                type="url"
                class="block w-full border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm focus:border-red-500 focus:ring-red-500"
                placeholder="https://example.com"
              />
            </div>
          </div>

          <div>
            <label for="location" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Location *</label>
            <input
              id="location"
              v-model="form.location"
              type="text"
              required
              class="block w-full border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm focus:border-red-500 focus:ring-red-500"
              placeholder="Enter business location"
            />
          </div>

          <div>
            <label for="bio" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Description</label>
            <textarea
              id="bio"
              v-model="form.bio"
              rows="4"
              class="block w-full border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm focus:border-red-500 focus:ring-red-500"
              placeholder="Describe your business and services"
            ></textarea>
          </div>
        </div>
      </div>

      <!-- Logo Upload -->
      <div class="bg-white dark:bg-gray-800 shadow overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">Logo</h3>
        </div>
        <div class="px-6 py-4">
          <div class="flex items-center space-x-6">
            <div class="flex-shrink-0">
              <img v-if="logoPreview" :src="logoPreview" alt="Logo preview" class="h-24 w-24 rounded-full object-cover" />
              <div v-else class="h-24 w-24 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                <Icon icon="heroicons:building-storefront" class="h-12 w-12 text-gray-400 dark:text-gray-600" />
              </div>
            </div>
            <div>
              <input
                ref="logoInput"
                type="file"
                accept="image/*"
                @change="handleLogoUpload"
                class="hidden"
              />
              <button
                type="button"
                @click="$refs.logoInput?.click()"
                class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
              >
                Upload Logo
              </button>
              <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">PNG, JPG up to 2MB</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Services -->
      <div class="bg-white dark:bg-gray-800 shadow overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">Services</h3>
        </div>
        <div class="px-6 py-4">
          <div class="space-y-4">
            <div v-for="(service, index) in form.services" :key="index" class="flex items-center space-x-4 p-4 border border-gray-200 dark:border-gray-700">
              <div class="flex-1">
                <select
                  v-model="service.service_id"
                  class="block w-full border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm focus:border-red-500 focus:ring-red-500"
                >
                  <option value="">Select a service</option>
                  <option v-for="availableService in availableServices" :key="availableService.id" :value="availableService.id">
                    {{ availableService.name }}
                  </option>
                </select>
              </div>
              <button
                type="button"
                @click="removeService(index)"
                class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-200"
              >
                <Icon icon="heroicons:trash" class="w-5 h-5" />
              </button>
            </div>
            <button
              type="button"
              @click="addService"
              class="flex items-center px-4 py-2 border border-dashed border-gray-300 dark:border-gray-600 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
            >
              <Icon icon="heroicons:plus" class="w-4 h-4 mr-2" />
              Add Service
            </button>
          </div>
        </div>
      </div>

      <!-- Social Media -->
      <div class="bg-white dark:bg-gray-800 shadow overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">Social Media</h3>
        </div>
        <div class="px-6 py-4 space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label for="facebook" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Facebook</label>
              <input
                id="facebook"
                v-model="form.facebook"
                type="url"
                class="block w-full border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm focus:border-red-500 focus:ring-red-500"
                placeholder="https://facebook.com/yourpage"
              />
            </div>
            <div>
              <label for="instagram" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Instagram</label>
              <input
                id="instagram"
                v-model="form.instagram"
                type="url"
                class="block w-full border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm focus:border-red-500 focus:ring-red-500"
                placeholder="https://instagram.com/yourpage"
              />
            </div>
          </div>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label for="twitter" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Twitter</label>
              <input
                id="twitter"
                v-model="form.twitter"
                type="url"
                class="block w-full border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm focus:border-red-500 focus:ring-red-500"
                placeholder="https://twitter.com/yourpage"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="flex justify-end space-x-4">
        <button
          type="button"
          @click="$router.back()"
          class="px-6 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
        >
          Cancel
        </button>
        <button
          type="submit"
          :disabled="loading"
          class="px-6 py-2 bg-red-600 text-white text-sm font-medium hover:bg-red-700 disabled:opacity-50 flex items-center"
        >
          <Icon v-if="loading" icon="heroicons:arrow-path" class="w-4 h-4 mr-2 animate-spin" />
          {{ loading ? 'Creating...' : 'Create Vendor' }}
        </button>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { ENDPOINTS } from '@/utils/api';

definePageMeta({
  layout: "dashboard",
});

const router = useRouter();
const loading = ref(false);
const logoPreview = ref<string | null>(null);
const logoFile = ref<File | null>(null);
const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

const form = ref({
  name: '',
  business_email: '',
  phone: '',
  website: '',
  location: '',
  bio: '',
  facebook: '',
  instagram: '',
  twitter: '',
  services: [] as Array<{ service_id: string }>
});

const availableServices = ref<any[]>([]);

const fetchServices = async () => {
  try {
    const response = await httpClient.get<any[]>(`${ENDPOINTS.SERVICES.BASE}`);
    availableServices.value = response || [];
  } catch (error) {
    console.error('Error fetching services:', error);
  }
};

const handleLogoUpload = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const file = target.files?.[0];

  if (file) {
    if (file.size > 2 * 1024 * 1024) {
      $toast.error('File size must be less than 2MB');
      return;
    }

    logoFile.value = file;
    const reader = new FileReader();
    reader.onload = (e) => {
      logoPreview.value = e.target?.result as string;
    };
    reader.readAsDataURL(file);
  }
};

const addService = () => {
  form.value.services.push({ service_id: '' });
};

const removeService = (index: number) => {
  form.value.services.splice(index, 1);
};

const submitForm = async () => {
  try {
    loading.value = true;

    const formData = new FormData();

    // Add basic form data
    Object.entries(form.value).forEach(([key, value]) => {
      if (key === 'services') {
        formData.append('services', JSON.stringify(value.filter(s => s.service_id)));
      } else if (value) {
        formData.append(key, value as string);
      }
    });

    // Add logo if uploaded
    if (logoFile.value) {
      formData.append('logo', logoFile.value);
    }

    await httpClient.post(`${ENDPOINTS.VENDORS.CREATE}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });

    $toast.success('Vendor created successfully');
    router.push('/dashboard/vendors');
  } catch (error: any) {
    console.error('Error creating vendor:', error);
    $toast.error(error.response?.data?.message || 'Failed to create vendor');
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  fetchServices();
  addService(); // Add one service field by default
});
</script>
