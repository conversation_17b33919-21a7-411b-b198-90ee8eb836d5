<template>
  <div>
    <div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
      <div>
        <h1 class="text-2xl font-semibold">Permissions</h1>
        <p class="text-gray-500 dark:text-gray-400">Manage system permissions and access control</p>
      </div>
      <div class="mt-4 md:mt-0">
        <button class="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 flex items-center">
          <Icon icon="heroicons:plus" class="h-5 w-5 mr-2" />
          Create Permission
        </button>
      </div>
    </div>

    <!-- Permissions by Category -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-md p-6 mb-6">
      <h2 class="text-lg font-semibold mb-4">Permissions by Category</h2>

      <div v-if="loading" class="py-8">
        <div class="animate-pulse space-y-4">
          <div class="h-10 bg-gray-200 dark:bg-gray-700 rounded"></div>
          <div class="h-10 bg-gray-200 dark:bg-gray-700 rounded"></div>
          <div class="h-10 bg-gray-200 dark:bg-gray-700 rounded"></div>
        </div>
      </div>

      <div v-else>
        <div v-for="(categoryPermissions, category) in permissionsByCategory" :key="category" class="mb-6">
          <div class="flex items-center mb-3">
            <h3 class="text-md font-medium">{{ formatCategory(String(category)) }}</h3>
            <span class="ml-2 px-2 py-0.5 text-xs rounded-full bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300">
              {{ categoryPermissions.length }}
            </span>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div
              v-for="permission in categoryPermissions"
              :key="permission.id"
              class="p-4 rounded-md bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600"
            >
              <div class="flex justify-between">
                <div>
                  <h4 class="font-medium text-gray-900 dark:text-white">{{ permission.display_name }}</h4>
                  <p class="text-xs text-gray-500 dark:text-gray-400">{{ permission.name }}</p>
                </div>
                <div class="flex space-x-2">
                  <button class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                    <Icon icon="heroicons:pencil-square" class="h-5 w-5" />
                  </button>
                  <button class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300">
                    <Icon icon="heroicons:trash" class="h-5 w-5" />
                  </button>
                </div>
              </div>
              <p v-if="permission.description" class="mt-2 text-sm text-gray-500 dark:text-gray-400">
                {{ permission.description }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Role Permissions -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-md p-6">
      <h2 class="text-lg font-semibold mb-4">Assign Permissions to Roles</h2>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Select Role</label>
          <select
            v-model="selectedRoleId"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700"
            @change="fetchRolePermissions"
          >
            <option value="">Select a role</option>
            <option v-for="role in roles" :key="role.id" :value="role.id">{{ role.display_name }}</option>
          </select>
        </div>

        <div v-if="selectedRoleId">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Assigned Permissions</label>

          <div v-if="rolePermissionsLoading" class="py-4 text-center">
            <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-gray-300 dark:border-gray-600"></div>
          </div>

          <div v-else-if="rolePermissions.length === 0" class="py-4 text-center text-gray-500 dark:text-gray-400">
            No permissions assigned to this role
          </div>

          <div v-else class="max-h-60 overflow-y-auto p-2 bg-gray-50 dark:bg-gray-700 rounded-md">
            <div
              v-for="permission in rolePermissions"
              :key="permission.id"
              class="flex items-center justify-between p-2 mb-1 rounded-md bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600"
            >
              <div>
                <p class="text-sm font-medium text-gray-900 dark:text-white">{{ permission.display_name }}</p>
                <p class="text-xs text-gray-500 dark:text-gray-400">{{ permission.name }}</p>
              </div>
              <button
                class="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
                @click="removePermissionFromRole(permission)"
              >
                <Icon icon="heroicons:x-mark" class="w-5 h-5 text-red-500" />
              </button>
            </div>
          </div>
        </div>
      </div>

      <div v-if="selectedRoleId" class="mt-6">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Add Permissions</label>
        <div class="flex space-x-2">
          <select
            v-model="permissionToAdd"
            class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700"
          >
            <option value="">Select a permission</option>
            <option
              v-for="permission in availablePermissions"
              :key="permission.id"
              :value="permission.id"
            >
              {{ permission.display_name }} ({{ permission.name }})
            </option>
          </select>
          <button
            class="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700"
            :disabled="!permissionToAdd"
            @click="addPermissionToRole"
          >
            Add
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { ENDPOINTS } from '~/utils/api';

definePageMeta({
  layout: "dashboard",
});

interface Permission {
  id: number;
  name: string;
  display_name: string;
  category: string;
  description?: string;
}

interface Role {
  id: number;
  name: string;
  display_name: string;
}

interface ApiResponse<T> {
  data: T;
  message?: string;
  status: number;
}

const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

const loading = ref(true);
const roles = ref<Role[]>([]);
const permissions = ref<Permission[]>([]);
const selectedRoleId = ref('');
const rolePermissions = ref<Permission[]>([]);
const rolePermissionsLoading = ref(false);
const permissionToAdd = ref('');

const permissionsByCategory = computed(() => {
  const result: { [key: string]: Permission[] } = {};

  permissions.value.forEach(permission => {
    if (!result[permission.category]) {
      result[permission.category] = [];
    }
    result[permission.category].push(permission);
  });

  return result;
});

const availablePermissions = computed(() => {
  return permissions.value.filter(permission =>
    !rolePermissions.value.some(rp => rp.id === permission.id)
  );
});

onMounted(async () => {
  await Promise.all([
    fetchPermissions(),
    fetchRoles()
  ]);
});

const fetchPermissions = async () => {
  loading.value = true;
  try {
    const response = await httpClient.get<ApiResponse<Permission[]>>(ENDPOINTS.PERMISSIONS.GET);
    permissions.value = response.data;
  } catch (error) {
    console.error('Error fetching permissions:', error);
    $toast.error('Failed to fetch permissions');
  } finally {
    loading.value = false;
  }
};

const fetchRoles = async () => {
  try {
    const response = await httpClient.get<ApiResponse<Role[]>>(ENDPOINTS.ROLES.GET);
    roles.value = response.data;
  } catch (error) {
    console.error('Error fetching roles:', error);
    $toast.error('Failed to fetch roles');
  }
};

const fetchRolePermissions = async () => {
  if (!selectedRoleId.value) {
    rolePermissions.value = [];
    return;
  }

  rolePermissionsLoading.value = true;
  try {
    const response = await httpClient.get<ApiResponse<Permission[]>>(`${ENDPOINTS.ROLES.SHOW}/${selectedRoleId.value}/permissions`);
    rolePermissions.value = response.data;
  } catch (error) {
    console.error('Error fetching role permissions:', error);
    $toast.error('Failed to fetch role permissions');
  } finally {
    rolePermissionsLoading.value = false;
  }
};

const formatCategory = (category: string) => {
  return category
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

const addPermissionToRole = async () => {
  if (!selectedRoleId.value || !permissionToAdd.value) return;

  try {
    await httpClient.post(`${ENDPOINTS.ROLES.ASSIGN_PERMISSIONS}/${selectedRoleId.value}`, {
      permissions: [permissionToAdd.value]
    });

    await fetchRolePermissions();
    $toast.success('Permission added to role');
    permissionToAdd.value = '';
  } catch (error) {
    console.error('Error adding permission to role:', error);
    $toast.error('Failed to add permission to role');
  }
};

const removePermissionFromRole = async (permission: Permission) => {
  try {
    const remainingPermissions = rolePermissions.value
      .filter(p => p.id !== permission.id)
      .map(p => p.id);

    await httpClient.post(`${ENDPOINTS.ROLES.ASSIGN_PERMISSIONS}/${selectedRoleId.value}`, {
      permissions: remainingPermissions
    });

    await fetchRolePermissions();
    $toast.success('Permission removed from role');
  } catch (error) {
    console.error('Error removing permission from role:', error);
    $toast.error('Failed to remove permission from role');
  }
};
</script>
