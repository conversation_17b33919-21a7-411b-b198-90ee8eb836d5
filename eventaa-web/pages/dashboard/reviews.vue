<template>
  <div class="p-6 bg-gray-100">
    <div class="mb-6 flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold">Reviews</h1>
        <p class="text-gray-500">Manage event and vendor reviews</p>
      </div>
      <div class="flex space-x-2">
        <button class="px-4 py-2 bg-white border border-gray-300 rounded shadow-sm hover:bg-gray-50 flex items-center">
          <Icon icon="heroicons:arrow-down-tray" class="w-5 h-5 text-gray-600 mr-1" />
          Export
        </button>
      </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <div class="bg-white p-4 shadow rounded">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-yellow-100 mr-4">
            <Icon icon="heroicons:star" class="w-6 h-6 text-yellow-600" />
          </div>
          <div>
            <p class="text-sm text-gray-500">Average Rating</p>
            <div class="flex items-center">
              <p class="text-2xl font-bold">{{ stats.averageRating.toFixed(1) }}</p>
              <div class="flex ml-2">
                <Icon
                  v-for="i in 5"
                  :key="i"
                  :icon="i <= Math.round(stats.averageRating) ? 'heroicons:star-solid' : 'heroicons:star'"
                  class="w-4 h-4"
                  :class="i <= Math.round(stats.averageRating) ? 'text-yellow-500' : 'text-gray-300'"
                />
              </div>
            </div>
            <div class="flex items-center text-sm">
              <span :class="stats.ratingGrowth >= 0 ? 'text-green-600' : 'text-red-600'">
                <Icon :icon="stats.ratingGrowth >= 0 ? 'heroicons:arrow-up' : 'heroicons:arrow-down'" class="w-4 h-4 inline" />
                {{ Math.abs(stats.ratingGrowth) }}%
              </span>
              <span class="text-gray-500 ml-1">vs last month</span>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white p-4 shadow rounded">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-blue-100 mr-4">
            <Icon icon="heroicons:chat-bubble-left-right" class="w-6 h-6 text-blue-600" />
          </div>
          <div>
            <p class="text-sm text-gray-500">Total Reviews</p>
            <p class="text-2xl font-bold">{{ stats.totalReviews }}</p>
            <div class="flex items-center text-sm">
              <span :class="stats.reviewsGrowth >= 0 ? 'text-green-600' : 'text-red-600'">
                <Icon :icon="stats.reviewsGrowth >= 0 ? 'heroicons:arrow-up' : 'heroicons:arrow-down'" class="w-4 h-4 inline" />
                {{ Math.abs(stats.reviewsGrowth) }}%
              </span>
              <span class="text-gray-500 ml-1">vs last month</span>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white p-4 shadow rounded">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-green-100 mr-4">
            <Icon icon="heroicons:check-circle" class="w-6 h-6 text-green-600" />
          </div>
          <div>
            <p class="text-sm text-gray-500">Positive Reviews</p>
            <p class="text-2xl font-bold">{{ stats.positiveReviews }}%</p>
            <div class="flex items-center text-sm">
              <span :class="stats.positiveGrowth >= 0 ? 'text-green-600' : 'text-red-600'">
                <Icon :icon="stats.positiveGrowth >= 0 ? 'heroicons:arrow-up' : 'heroicons:arrow-down'" class="w-4 h-4 inline" />
                {{ Math.abs(stats.positiveGrowth) }}%
              </span>
              <span class="text-gray-500 ml-1">vs last month</span>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white p-4 shadow rounded">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-red-100 mr-4">
            <Icon icon="heroicons:flag" class="w-6 h-6 text-red-600" />
          </div>
          <div>
            <p class="text-sm text-gray-500">Flagged Reviews</p>
            <p class="text-2xl font-bold">{{ stats.flaggedReviews }}</p>
            <div class="flex items-center text-sm">
              <span :class="stats.flaggedGrowth <= 0 ? 'text-green-600' : 'text-red-600'">
                <Icon :icon="stats.flaggedGrowth <= 0 ? 'heroicons:arrow-down' : 'heroicons:arrow-up'" class="w-4 h-4 inline" />
                {{ Math.abs(stats.flaggedGrowth) }}%
              </span>
              <span class="text-gray-500 ml-1">vs last month</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="bg-white p-4 shadow mb-6">
      <div class="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-4">
        <div class="flex-1">
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Icon icon="heroicons:magnifying-glass" class="h-5 w-5 text-gray-400" />
            </div>
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search reviews"
              class="block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:border-red-500 focus:ring-red-500"
            />
          </div>
        </div>
        <div>
          <select
            v-model="typeFilter"
            class="border-gray-300 rounded-md shadow-sm focus:border-red-500 focus:ring-red-500"
          >
            <option value="all">All Types</option>
            <option value="event">Event</option>
            <option value="vendor">Vendor</option>
          </select>
        </div>
        <div>
          <select
            v-model="ratingFilter"
            class="border-gray-300 rounded-md shadow-sm focus:border-red-500 focus:ring-red-500"
          >
            <option value="all">All Ratings</option>
            <option value="5">5 Stars</option>
            <option value="4">4 Stars</option>
            <option value="3">3 Stars</option>
            <option value="2">2 Stars</option>
            <option value="1">1 Star</option>
          </select>
        </div>
        <div>
          <select
            v-model="statusFilter"
            class="border-gray-300 rounded-md shadow-sm focus:border-red-500 focus:ring-red-500"
          >
            <option value="all">All Status</option>
            <option value="published">Published</option>
            <option value="pending">Pending</option>
            <option value="flagged">Flagged</option>
            <option value="rejected">Rejected</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Reviews Table -->
    <div class="bg-white shadow">
      <div v-if="loading" class="flex justify-center items-center py-20">
        <CoreLoader />
      </div>
      <div v-else>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead>
              <tr>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reviewer</th>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rating</th>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Review</th>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">For</th>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="review in filteredReviews" :key="review.id" class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10">
                      <img class="h-10 w-10 rounded-full" :src="review.reviewer.avatar || 'https://via.placeholder.com/40'" alt="" />
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium text-gray-900">{{ review.reviewer.name }}</div>
                      <div class="text-sm text-gray-500">{{ review.reviewer.email }}</div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex">
                    <Icon
                      v-for="i in 5"
                      :key="i"
                      :icon="i <= review.rating ? 'heroicons:star-solid' : 'heroicons:star'"
                      class="w-5 h-5"
                      :class="i <= review.rating ? 'text-yellow-500' : 'text-gray-300'"
                    />
                  </div>
                </td>
                <td class="px-6 py-4">
                  <div class="text-sm text-gray-900">{{ truncateText(review.content, 100) }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ review.for.name }}</div>
                  <div class="text-sm text-gray-500">{{ formatType(review.type) }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ formatDate(review.date) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                    :class="{
                      'bg-green-100 text-green-800': review.status === 'published',
                      'bg-yellow-100 text-yellow-800': review.status === 'pending',
                      'bg-red-100 text-red-800': review.status === 'flagged',
                    }">
                    {{ formatStatus(review.status) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div class="flex space-x-2">
                    <button @click="viewReview(review)" class="text-gray-600 hover:text-gray-900">
                      <Icon icon="heroicons:eye" class="w-5 h-5" />
                    </button>
                    <button
                      v-if="review.status === 'pending'"
                      @click="approveReview(review)"
                      class="text-green-600 hover:text-green-900"
                    >
                      <Icon icon="heroicons:check-circle" class="w-5 h-5" />
                    </button>
                    <button
                      v-if="review.status === 'pending' || review.status === 'flagged'"
                      @click="rejectReview(review)"
                      class="text-red-600 hover:text-red-900"
                    >
                      <Icon icon="heroicons:x-circle" class="w-5 h-5" />
                    </button>
                    <button
                      v-if="review.status === 'published'"
                      @click="flagReview(review)"
                      class="text-yellow-600 hover:text-yellow-900"
                    >
                      <Icon icon="heroicons:flag" class="w-5 h-5" />
                    </button>
                    <button @click="deleteReview(review.id)" class="text-red-600 hover:text-red-900">
                      <Icon icon="heroicons:trash" class="w-5 h-5" />
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
          <div class="flex-1 flex justify-between sm:hidden">
            <button @click="prevPage" :disabled="currentPage === 1" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
              Previous
            </button>
            <button @click="nextPage" :disabled="currentPage === totalPages" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
              Next
            </button>
          </div>
          <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p class="text-sm text-gray-700">
                Showing <span class="font-medium">{{ paginationStart }}</span> to <span class="font-medium">{{ paginationEnd }}</span> of <span class="font-medium">{{ totalItems }}</span> reviews
              </p>
            </div>
            <div>
              <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button @click="prevPage" :disabled="currentPage === 1" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                  <span class="sr-only">Previous</span>
                  <Icon icon="heroicons:chevron-left" class="h-5 w-5" />
                </button>
                <button v-for="page in displayedPages" :key="page" @click="goToPage(page)" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium" :class="page === currentPage ? 'z-10 bg-red-50 border-red-500 text-red-600' : 'text-gray-500 hover:bg-gray-50'">
                  {{ page }}
                </button>
                <button @click="nextPage" :disabled="currentPage === totalPages" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                  <span class="sr-only">Next</span>
                  <Icon icon="heroicons:chevron-right" class="h-5 w-5" />
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { ENDPOINTS } from '~/utils/api';

definePageMeta({
  layout: "dashboard",
});

interface ReviewStats {
  averageRating: number;
  ratingGrowth: number;
  totalReviews: number;
  reviewsGrowth: number;
  positiveReviews: number;
  positiveGrowth: number;
  flaggedReviews: number;
  flaggedGrowth: number;
}

interface Reviewer {
  name: string;
  email: string;
  avatar?: string;
}

interface ReviewSubject {
  id: number;
  name: string;
}

interface Review {
  id: number;
  reviewer: Reviewer;
  rating: number;
  content: string;
  type: 'event' | 'vendor';
  for: ReviewSubject;
  date: string;
  status: 'published' | 'pending' | 'flagged' | 'hidden';
}

interface ApiResponse<T> {
  data: T;
  message?: string;
  status: number;
}

interface ReviewsResponse {
  reviews: Review[];
  total: number;
}

const loading = ref(true);
const searchQuery = ref('');
const typeFilter = ref('all');
const ratingFilter = ref('all');
const statusFilter = ref('all');
const currentPage = ref(1);
const itemsPerPage = 10;
const totalItems = ref(0);
const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

const stats = ref<ReviewStats>({
  averageRating: 0,
  ratingGrowth: 0,
  totalReviews: 0,
  reviewsGrowth: 0,
  positiveReviews: 0,
  positiveGrowth: 0,
  flaggedReviews: 0,
  flaggedGrowth: 0
});

const reviews = ref<Review[]>([]);

async function fetchStats() {
  try {
    const response = await httpClient.get<ApiResponse<ReviewStats>>(ENDPOINTS.REVIEWS.STATS);
    stats.value = response.data;
  } catch (error) {
    console.error('Error fetching review stats:', error);
    $toast.error('Failed to load review statistics');
  }
}

async function fetchReviews() {
  try {
    loading.value = true;
    const params = new URLSearchParams({
      page: currentPage.value.toString(),
      limit: itemsPerPage.toString(),
      search: searchQuery.value,
      type: typeFilter.value !== 'all' ? typeFilter.value : '',
      rating: ratingFilter.value !== 'all' ? ratingFilter.value : '',
      status: statusFilter.value !== 'all' ? statusFilter.value : ''
    });

    const response = await httpClient.get<ApiResponse<ReviewsResponse>>(`${ENDPOINTS.REVIEWS.GET}?${params.toString()}`);
    reviews.value = response.data.reviews;
    totalItems.value = response.data.total;
  } catch (error) {
    console.error('Error fetching reviews:', error);
    $toast.error('Failed to load reviews');
  } finally {
    loading.value = false;
  }
}

async function updateReviewStatus(reviewId: number, status: string) {
  try {
    await httpClient.put(`${ENDPOINTS.REVIEWS.UPDATE}/${reviewId}`, { status });
    await fetchReviews();
    $toast.success(`Review status updated successfully`);
  } catch (error) {
    console.error('Error updating review status:', error);
    $toast.error('Failed to update review status');
  }
}

async function deleteReview(reviewId: number) {
  try {
    await httpClient.delete(`${ENDPOINTS.REVIEWS.DELETE}/${reviewId}`);
    await fetchReviews();
    $toast.success('Review deleted successfully');
  } catch (error) {
    console.error('Error deleting review:', error);
    $toast.error('Failed to delete review');
  }
}

function approveReview(review: Review): void {
  updateReviewStatus(review.id, 'published');
}

function rejectReview(review: Review): void {
  updateReviewStatus(review.id, 'hidden');
}

function flagReview(review: Review): void {
  updateReviewStatus(review.id, 'flagged');
}

watch([searchQuery, typeFilter, ratingFilter, statusFilter], () => {
  currentPage.value = 1;
  fetchReviews();
});

onMounted(async () => {
  await Promise.all([fetchStats(), fetchReviews()]);
});

const filteredReviews = computed(() => {
  let result = [...reviews.value];

  // Apply search
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(r =>
      r.reviewer.name.toLowerCase().includes(query) ||
      r.reviewer.email.toLowerCase().includes(query) ||
      r.content.toLowerCase().includes(query) ||
      r.for.name.toLowerCase().includes(query)
    );
  }

  // Apply type filter
  if (typeFilter.value !== 'all') {
    result = result.filter(r => r.type === typeFilter.value);
  }

  // Apply rating filter
  if (ratingFilter.value !== 'all') {
    result = result.filter(r => r.rating === parseInt(ratingFilter.value));
  }

  // Apply status filter
  if (statusFilter.value !== 'all') {
    result = result.filter(r => r.status === statusFilter.value);
  }

  // Sort by date (newest first)
  result.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

  totalItems.value = result.length;

  // Apply pagination
  const start = (currentPage.value - 1) * itemsPerPage;
  const end = start + itemsPerPage;
  return result.slice(start, end);
});

const totalPages = computed(() => Math.ceil(totalItems.value / itemsPerPage));

const paginationStart = computed(() => {
  if (totalItems.value === 0) return 0;
  return (currentPage.value - 1) * itemsPerPage + 1;
});

const paginationEnd = computed(() => {
  return Math.min(currentPage.value * itemsPerPage, totalItems.value);
});

const displayedPages = computed(() => {
  const pages = [];
  const maxPagesToShow = 5;

  if (totalPages.value <= maxPagesToShow) {
    for (let i = 1; i <= totalPages.value; i++) {
      pages.push(i);
    }
  } else {
    const leftSide = Math.floor(maxPagesToShow / 2);
    const rightSide = maxPagesToShow - leftSide - 1;

    if (currentPage.value > leftSide && currentPage.value < totalPages.value - rightSide) {
      // Middle case
      for (let i = currentPage.value - leftSide; i <= currentPage.value + rightSide; i++) {
        pages.push(i);
      }
    } else if (currentPage.value <= leftSide) {
      // Near the start
      for (let i = 1; i <= maxPagesToShow; i++) {
        pages.push(i);
      }
    } else {
      // Near the end
      for (let i = totalPages.value - maxPagesToShow + 1; i <= totalPages.value; i++) {
        pages.push(i);
      }
    }
  }

  return pages;
});

function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
}

function formatType(type: string) {
  return type.charAt(0).toUpperCase() + type.slice(1);
}

function formatStatus(status: string) {
  return status.charAt(0).toUpperCase() + status.slice(1);
}

function truncateText(text: string, maxLength: number) {
  if (!text) return '';
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
}

function prevPage() {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
}

function nextPage() {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
}

function goToPage(page: number) {
  currentPage.value = page;
}

function viewReview(review: Review) {
  console.log('View review:', review);
  $toast.info(`Viewing review for ${review.for.name}`);
}
</script>
