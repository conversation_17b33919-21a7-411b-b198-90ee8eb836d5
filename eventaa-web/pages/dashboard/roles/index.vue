<template>
  <div class="p-6">
    <div class="mb-6">
      <h1 class="text-2xl font-bold dashboard-text-primary">Role Management</h1>
      <p class="dashboard-text-secondary mt-1">Manage vendor roles and permissions</p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Roles List -->
      <div class="lg:col-span-1 dashboard-bg-card rounded-lg shadow-sm p-4">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-lg font-semibold dashboard-text-primary">Roles</h2>
          <button
            v-if="vendorStore.hasPermissions(['roles.create'])"
            class="px-3 py-1 text-sm rounded-md dashboard-bg-active dashboard-text-primary"
            @click="createNewRole"
          >
            Add Role
          </button>
        </div>

        <div v-if="loading" class="py-8 text-center">
          <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 dashboard-border"></div>
        </div>

        <div v-else-if="roles.length === 0" class="py-8 text-center dashboard-text-secondary">
          No roles found
        </div>

        <div v-else class="space-y-2">
          <div
            v-for="role in roles"
            :key="role.id"
            class="p-3 rounded-md dashboard-bg-hover cursor-pointer"
            :class="{ 'dashboard-bg-active': selectedRole?.id === role.id }"
            @click="selectRole(role)"
          >
            <div class="flex justify-between items-center">
              <div>
                <h3 class="font-medium dashboard-text-primary">{{ role.display_name }}</h3>
                <p class="text-xs dashboard-text-secondary">{{ role.name }}</p>
              </div>
              <div class="flex space-x-2">
                <button
                  v-if="vendorStore.hasPermissions(['roles.edit'])"
                  class="p-1 rounded-full hover:dashboard-bg-hover"
                  @click.stop="editRole(role)"
                >
                  <Icon icon="heroicons:pencil-square" class="w-4 h-4 dashboard-text-secondary" />
                </button>
                <button
                  v-if="vendorStore.hasPermissions(['roles.delete'])"
                  class="p-1 rounded-full hover:dashboard-bg-hover"
                  @click.stop="deleteRole(role)"
                >
                  <Icon icon="heroicons:trash" class="w-4 h-4 text-red-500" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Role Details -->
      <div class="lg:col-span-2 dashboard-bg-card rounded-lg shadow-sm p-4">
        <div v-if="!selectedRole" class="py-12 text-center">
          <Icon icon="heroicons:user-group" class="w-16 h-16 mx-auto dashboard-text-secondary opacity-30" />
          <h3 class="mt-4 text-lg font-medium dashboard-text-primary">Select a role to view details</h3>
          <p class="dashboard-text-secondary">Or create a new role to get started</p>
        </div>

        <div v-else>
          <div class="flex justify-between items-center mb-6">
            <div>
              <h2 class="text-xl font-semibold dashboard-text-primary">{{ selectedRole.display_name }}</h2>
              <p class="dashboard-text-secondary">{{ selectedRole.description }}</p>
            </div>
            <div class="flex space-x-3">
              <button
                v-if="vendorStore.hasPermissions(['roles.permissions.assign'])"
                class="px-3 py-1 text-sm rounded-md dashboard-bg-active dashboard-text-primary"
                @click="showPermissionsDialog = true"
              >
                Manage Permissions
              </button>
            </div>
          </div>

          <div class="mb-6">
            <h3 class="text-md font-medium dashboard-text-primary mb-2">Permissions</h3>
            <div v-if="selectedRole.permissions && selectedRole.permissions.length > 0" class="grid grid-cols-1 md:grid-cols-2 gap-2">
              <div
                v-for="permission in selectedRole.permissions"
                :key="permission.id"
                class="flex items-center p-2 rounded-md dashboard-bg-hover"
              >
                <Icon icon="heroicons:check-circle" class="w-5 h-5 text-green-500 mr-2" />
                <div>
                  <p class="text-sm font-medium dashboard-text-primary">{{ permission.display_name }}</p>
                  <p class="text-xs dashboard-text-secondary">{{ permission.name }}</p>
                </div>
              </div>
            </div>
            <div v-else class="py-4 text-center dashboard-text-secondary">
              No permissions assigned to this role
            </div>
          </div>

          <div>
            <h3 class="text-md font-medium dashboard-text-primary mb-2">Users with this role</h3>
            <div v-if="selectedRole.users && selectedRole.users.length > 0" class="grid grid-cols-1 md:grid-cols-2 gap-2">
              <div
                v-for="user in selectedRole.users"
                :key="user.id"
                class="flex items-center p-2 rounded-md dashboard-bg-hover"
              >
                <img
                  :src="user.avatar ? `${runtimeConfig.public.baseUrl}storage/avatars/${user.avatar}` : `https://ui-avatars.com/api/?name=${encodeURIComponent(user.name)}`"
                  class="w-8 h-8 rounded-full mr-2"
                  :alt="user.name"
                />
                <div>
                  <p class="text-sm font-medium dashboard-text-primary">{{ user.name }}</p>
                  <p class="text-xs dashboard-text-secondary">{{ user.email }}</p>
                </div>
              </div>
            </div>
            <div v-else class="py-4 text-center dashboard-text-secondary">
              No users assigned to this role
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useVendorStore } from '@/store/vendor';
import type { Role } from '@/types/role';
import { ENDPOINTS } from '@/utils/api';

definePageMeta({
  layout: 'vendor-dashboard',
  middleware: ['vendor'],
  meta: {
    requiredPermissions: ['roles.view']
  }
});

const vendorStore = useVendorStore();
const runtimeConfig = useRuntimeConfig();
const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

const roles = ref<Role[]>([]);
const selectedRole = ref<Role | null>(null);
const loading = ref<boolean>(true);
const showPermissionsDialog = ref<boolean>(false);

onMounted(async () => {
  await fetchRoles();
});

const fetchRoles = async () => {
  loading.value = true;
  try {
    const response = await httpClient.get<Role[]>(ENDPOINTS.ROLES.GET);
    if (response) {
      roles.value = response;
    }
  } catch (error) {
    console.error('Error fetching roles:', error);
    $toast.error('Failed to fetch roles');
  } finally {
    loading.value = false;
  }
};

const selectRole = async (role: Role) => {
  try {
    const response = await httpClient.get<Role>(`${ENDPOINTS.ROLES.SHOW}/${role.id}`);
    if (response) {
      selectedRole.value = response;
    }
  } catch (error) {
    console.error('Error fetching role details:', error);
    $toast.error('Failed to fetch role details');
  }
};

const createNewRole = () => {
  $toast.info('Create role functionality will be implemented');
};

const editRole = (role: Role) => {
  $toast.info(`Edit role: ${role.name}`);
};

const deleteRole = (role: Role) => {
  $toast.info(`Delete role: ${role.name}`);
};
</script>
