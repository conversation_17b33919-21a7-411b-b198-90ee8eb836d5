<template>
    <div class="p-6">
        <div class="mb-6 flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold">Venues</h1>
                <p class="text-gray-500">Manage places where your events takes place as well as bookings</p>
            </div>

            <div class="flex items-center">
                <CorePrimaryButton text="Add venue" start-icon="mdi:plus"
                    @click="$router.push('/dashboard/venues/create')" />
            </div>
        </div>

        <div class="w-full flex items-center justify-between mb-4">
            <div class="flex items-center">
                <button @click="activeView = 'table'" class="flex items-center px-2 py-1.5 border"
                    :class="activeView === 'table' ? 'bg-red-600 text-white' : 'border-gray-300'">
                    <Icon icon="ri:table-fill" class="w-5 h-5 inline mr-1" />
                    Table
                </button>
                <button @click="activeView = 'map'" class="flex items-center px-2 py-1.5 border border-l-0"
                    :class="activeView === 'map' ? 'bg-red-600 text-white' : 'border-gray-300'">
                    <Icon icon="fa6-solid:map-location-dot" class="w-5 h-5 inline mr-1" />
                    Map
                </button>
            </div>

            <div class="flex items-center space-x-2">
                <div v-if="activeView === 'map'" class="flex items-center space-x-2">
                    <div class="relative">
                        <Listbox v-model="countryFilter">
                            <div class="relative">
                                <ListboxButton
                                    class="w-48 flex items-center justify-between bg-white px-3 py-1.5 border cursor-pointer focus:outline-none">
                                    <span class="block truncate">{{ countryFilter || 'All Countries' }}</span>
                                    <Icon icon="ph:caret-up-down-fill" class="h-5 w-5 text-gray-400" />
                                </ListboxButton>
                                <Transition leave-active-class="transition duration-100 ease-in"
                                    leave-from-class="opacity-100" leave-to-class="opacity-0">
                                    <ListboxOptions
                                        class="absolute z-30 mt-1 max-h-60 w-full overflow-auto bg-white border-gray-300 shadow-lg focus:outline-none">
                                        <ListboxOption v-slot="{ active, selected }" :value="''" as="template">
                                            <li :class="[
                                                active ? 'bg-red-600 text-white' : 'text-gray-900',
                                                'relative cursor-pointer select-none py-2 pl-3 pr-9'
                                            ]">
                                                <span class="block truncate"
                                                    :class="selected ? 'font-semibold' : 'font-normal'">
                                                    All Countries
                                                </span>
                                                <span v-if="selected" :class="[
                                                    active ? 'text-white' : 'text-red-600',
                                                    'absolute inset-y-0 right-0 flex items-center pr-4'
                                                ]">
                                                    <Icon icon="heroicons:check" class="h-5 w-5" />
                                                </span>
                                            </li>
                                        </ListboxOption>
                                        <ListboxOption v-for="country in uniqueCountries" :key="country"
                                            v-slot="{ active, selected }" :value="country" as="template">
                                            <li :class="[
                                                active ? 'bg-red-600 text-white' : 'text-gray-900',
                                                'relative cursor-pointer select-none py-2 pl-3 pr-9'
                                            ]">
                                                <span class="block truncate"
                                                    :class="selected ? 'font-semibold' : 'font-normal'">
                                                    {{ country }}
                                                </span>
                                                <span v-if="selected" :class="[
                                                    active ? 'text-white' : 'text-red-600',
                                                    'absolute inset-y-0 right-0 flex items-center pr-4'
                                                ]">
                                                    <Icon icon="heroicons:check" class="h-5 w-5" />
                                                </span>
                                            </li>
                                        </ListboxOption>
                                    </ListboxOptions>
                                </Transition>
                            </div>
                        </Listbox>
                    </div>

                    <div class="relative">
                        <Listbox v-model="cityFilter">
                            <div class="relative">
                                <ListboxButton
                                    class="w-48 flex items-center justify-between bg-white px-3 py-1.5 border cursor-pointer focus:outline-none">
                                    <span class="block truncate">{{ cityFilter || 'All Cities' }}</span>
                                    <Icon icon="ph:caret-up-down-fill" class="h-5 w-5 text-gray-400" />
                                </ListboxButton>
                                <Transition leave-active-class="transition duration-100 ease-in"
                                    leave-from-class="opacity-100" leave-to-class="opacity-0">
                                    <ListboxOptions
                                        class="absolute z-30 mt-1 max-h-60 w-full overflow-auto bg-white shadow-lg focus:outline-none">
                                        <ListboxOption v-slot="{ active, selected }" :value="''" as="template">
                                            <li :class="[
                                                active ? 'bg-red-600 text-white' : 'text-gray-900',
                                                'relative cursor-pointer select-none py-2 pl-3 pr-9'
                                            ]">
                                                <span class="block truncate"
                                                    :class="selected ? 'font-semibold' : 'font-normal'">
                                                    All Cities
                                                </span>
                                                <span v-if="selected" :class="[
                                                    active ? 'text-white' : 'text-red-600',
                                                    'absolute inset-y-0 right-0 flex items-center pr-4'
                                                ]">
                                                    <Icon icon="heroicons:check" class="h-5 w-5" />
                                                </span>
                                            </li>
                                        </ListboxOption>
                                        <ListboxOption v-for="city in uniqueCities" :key="city"
                                            v-slot="{ active, selected }" :value="city" as="template">
                                            <li :class="[
                                                active ? 'bg-red-600 text-white' : 'text-gray-900',
                                                'relative cursor-pointer select-none py-2 pl-3 pr-9'
                                            ]">
                                                <span class="block truncate"
                                                    :class="selected ? 'font-semibold' : 'font-normal'">
                                                    {{ city }}
                                                </span>
                                                <span v-if="selected" :class="[
                                                    active ? 'text-white' : 'text-red-600',
                                                    'absolute inset-y-0 right-0 flex items-center pr-4'
                                                ]">
                                                    <Icon icon="heroicons:check" class="h-5 w-5" />
                                                </span>
                                            </li>
                                        </ListboxOption>
                                    </ListboxOptions>
                                </Transition>
                            </div>
                        </Listbox>
                    </div>
                </div>
                <CoreSearch v-model="search" placeholder="Search by name..." />
            </div>
        </div>

        <div v-if="activeView === 'table'">
            <Datatable v-model:server-options="serverOptions" :server-items-length="totalItems" :loading="loading"
                :headers="headers" :items="venues" buttons-pagination @update:options="handleOptionsChange"
                theme-color="#dc2626" search-field="name" :search-value="search">
                <template #loading>
                    <CoreLoader />
                </template>
                <template #item-actions="item">
                    <div class="flex space-x-2">
                        <button @click="$router.push(`/dashboard/venues/${item.slug}`)">
                            <Icon icon="solar:eye-broken" class="w-5 h-5" />
                        </button>
                        <button @click="$router.push(`/dashboard/venues/update/${item.slug}`)">
                            <Icon icon="fluent:edit-28-regular" class="w-5 h-5" />
                        </button>
                        <button @click="openDeleteDialog(item)">
                            <Icon icon="mage:trash" class="w-5 h-5" />
                        </button>
                    </div>
                </template>
            </Datatable>
        </div>

        <div v-else-if="activeView === 'map'" class="bg-gray-100">
            <div v-if="isMapLoading" class="flex justify-center items-center h-96">
                <CoreLoader />
            </div>
            <div v-else>
                <GoogleMap :api-key="runtimeConfig.public.googleMapsApiKey" class="h-96 w-full" :center="mapCenter"
                    :zoom="mapZoom" mapTypeId="terrain" @map-clicked="handleMapClick">
                    <CustomMarker v-for="venue in filteredVenues" :key="venue.id" :options="{
                        position: { lat: Number(venue.latitude), lng: Number(venue.longitude) },
                        anchorPoint: 'BOTTOM_CENTER',
                    }">
                        <div style="text-align: center">
                            <div class="relative shadow-xl shadow-black"
                                :class="{ 'animate-bounce': selectedVenue?.id === venue.id }"
                                @mouseenter="hoveredVenue = venue" @mouseleave="hoveredVenue = null"
                                @click="toggleSelectedVenue(venue)">
                                <button
                                    class="relative inline-flex items-center justify-center p-2 cursor-pointer bg-red-600 text-white hover:bg-red-700 transition-colors">
                                    <Icon icon="fa6-solid:map-location-dot" class="w-6 h-6" />
                                </button>
                                <span
                                    class="absolute left-1/2 -bottom-1 transform -translate-x-1/2 w-0 h-0 border-l-[6px] border-l-transparent border-r-[6px] border-r-transparent border-t-[6px] border-t-red-600"></span>
                            </div>

                            <div v-if="hoveredVenue?.id === venue.id && selectedVenue?.id !== venue.id"
                                class="absolute mt-2 -translate-x-1/2 left-1/2 bg-white p-2 shadow-md border border-gray-200 z-10 min-w-48">
                                <div class="font-semibold">{{ venue.name }}</div>
                            </div>

                            <div v-if="selectedVenue?.id === venue.id"
                                class="absolute mt-2 -translate-x-1/2 left-1/2 bg-white p-3 shadow-lg border border-gray-300 z-20 min-w-64">
                                <div class="flex justify-between items-center mb-1">
                                    <h3 class="font-bold text-lg">{{ venue.name }}</h3>
                                    <button @click.stop="selectedVenue = null" class="p-1 hover:bg-gray-100">
                                        <Icon icon="mdi:close" class="w-4 h-4" />
                                    </button>
                                </div>
                                <div class="text-left">
                                    <p class="text-gray-700">{{ venue.address }}</p>
                                    <p class="text-gray-700">{{ venue.city }}, {{ venue.country }}</p>
                                    <div class="flex space-x-2 mt-2 justify-end">
                                        <button class="px-2 py-1" @click="$router.push(`/dashboard/venues/${venue.slug}`)">
                                            <Icon icon="solar:eye-broken" class="w-5 h-5" />
                                        </button>
                                        <button class="px-2 py-1"
                                            @click.stop="$router.push(`/dashboard/venues/update/${venue.slug}`)">
                                            <Icon icon="fluent:edit-28-regular" class="w-5 h-5" />
                                        </button>
                                        <button class="px-2 py-1" @click.stop="openDeleteDialog(venue)">
                                            <Icon icon="mage:trash" class="w-5 h-5" />
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </CustomMarker>
                </GoogleMap>
                <div class="p-4 text-base text-gray-500">
                    <p>Showing {{ filteredVenues.length }} venues.</p>
                    <p v-if="filteredVenues.length === 0">No venues with location data found for the current
                        filters.</p>
                    <p v-else-if="filteredVenues.length < venues.length">Some venues may not appear on the map due to
                        missing location data.</p>
                </div>
            </div>
        </div>

        <TransitionRoot appear :show="isDeleteDialogOpen" as="template">
            <Dialog as="div" @close="closeDeleteDialog" class="relative z-10">
                <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0"
                    enter-to="opacity-100" leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                    <div class="fixed inset-0 bg-black bg-opacity-25" />
                </TransitionChild>

                <div class="fixed inset-0 overflow-y-auto">
                    <div class="flex min-h-full items-center justify-center p-4 text-center">
                        <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
                            enter-to="opacity-100 scale-100" leave="duration-200 ease-in"
                            leave-from="opacity-100 scale-100" leave-to="opacity-0 scale-95">
                            <DialogPanel
                                class="w-full max-w-md transform overflow-hidden bg-white p-6 text-left align-middle shadow-xl transition-all">
                                <DialogTitle as="h3" class="text-xl font-semibold leading-6 text-gray-900">
                                    Delete Venue
                                </DialogTitle>
                                <div class="mt-4">
                                    <p class="text-gray-600">
                                        Are you sure you want to delete "<strong>{{ venueToDelete?.name }}</strong>"?
                                        This action cannot be undone.
                                    </p>

                                    <div class="mt-6 flex justify-end space-x-3">
                                        <button type="button" @click="closeDeleteDialog"
                                            class="px-4 py-2 border border-gray-300 text-gray-700 hover:bg-gray-50">
                                            Cancel
                                        </button>
                                        <CorePrimaryButton :text="isSubmitting ? 'Deleting...' : 'Delete'"
                                            :loading="isSubmitting" @click="deleteVenue" />
                                    </div>
                                </div>
                            </DialogPanel>
                        </TransitionChild>
                    </div>
                </div>
            </Dialog>
        </TransitionRoot>
    </div>
</template>

<script setup lang="ts">
import { Dialog, DialogPanel, DialogTitle, TransitionRoot, TransitionChild } from '@headlessui/vue';
import { Listbox, ListboxButton, ListboxOptions, ListboxOption } from '@headlessui/vue';
import { GoogleMap } from 'vue3-google-map';

definePageMeta({
    layout: "dashboard"
});

useHead({
    title: "Venues | EventaHub Malawi",
    meta: [
        {
            name: "description",
            content: "Manage venues on our platform."
        }
    ]
});

const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();
const search = ref<string>('');
const hoveredVenue = ref<Venue | null>(null);
const activeView = ref<'table' | 'map'>('table');
const isMapLoading = ref<boolean>(true);
const runtimeConfig = useRuntimeConfig();
const mapCenter = ref({ lat: -13.962612, lng: 33.774119 });
const mapZoom = ref(6);
const selectedVenue = ref<Venue | null>(null);
const countryFilter = ref<string>('');
const cityFilter = ref<string>('');

interface Venue {
    id: number;
    name: string;
    address: string;
    street: string | null;
    city: string;
    country: string;
    latitude: number | null;
    longitude: number | null;
    slug?: string;
    created_at?: string;
    updated_at?: string;
}

const venues = ref<Venue[]>([]);
const loading = ref<boolean>(false);
const totalItems = ref<number>(0);
const serverOptions = ref({
    page: 1,
    rowsPerPage: 10,
    sortBy: 'name',
    sortType: 'asc'
});

const toggleSelectedVenue = (venue: Venue) => {
    selectedVenue.value = selectedVenue.value?.id === venue.id ? null : venue;
}

const headers = [
    { text: 'NAME', value: 'name' },
    { text: 'ADDRESS', value: 'address' },
    { text: 'CITY', value: 'city' },
    { text: 'COUNTRY', value: 'country' },
    { text: 'ACTIONS', value: 'actions', sortable: false }
];

const isDeleteDialogOpen = ref<boolean>(false);
const isSubmitting = ref<boolean>(false);
const venueToDelete = ref<Venue | null>(null);

const uniqueCountries = computed(() => {
    const countries = venues.value.map(venue => venue.country);
    return [...new Set(countries)].sort();
});

const uniqueCities = computed(() => {
    let cities;
    if (countryFilter.value) {
        cities = venues.value
            .filter(venue => venue.country === countryFilter.value)
            .map(venue => venue.city);
    } else {
        cities = venues.value.map(venue => venue.city);
    }
    return [...new Set(cities)].sort();
});

const filteredVenues = computed(() => {
    return venues.value.filter(venue => {
        if (venue.latitude === null || venue.longitude === null) {
            return false;
        }

        if (countryFilter.value && venue.country !== countryFilter.value) {
            return false;
        }

        if (cityFilter.value && venue.city !== cityFilter.value) {
            return false;
        }

        if (search.value && !venue.name.toLowerCase().includes(search.value.toLowerCase())) {
            return false;
        }

        return true;
    });
});

watch(countryFilter, () => {
    cityFilter.value = '';
});

watch(activeView, (newView) => {
    if (newView === 'map') {
        isMapLoading.value = true;
        setTimeout(() => {
            isMapLoading.value = false;
            adjustMapView();
        }, 500);
    }
});

onMounted(() => {
    fetchVenues();
});

const fetchVenues = async (): Promise<void> => {
    loading.value = true;
    try {
        const { page, rowsPerPage, sortBy, sortType } = serverOptions.value;
        const response: any = await httpClient.get(ENDPOINTS.VENUES.USER, {
            params: {
                page,
                per_page: rowsPerPage,
                sort_by: sortBy,
                sort_type: sortType,
                search: search.value
            }
        });
        venues.value = response.data;
        totalItems.value = response.total;

        if (activeView.value === 'map') {
            adjustMapView();
        }
    } catch (error) {
        console.error('Error fetching venues:', error);
    } finally {
        loading.value = false;
    }
};

const handleOptionsChange = (): void => {
    fetchVenues();
};

const openDeleteDialog = (venue: Venue): void => {
    venueToDelete.value = venue;
    isDeleteDialogOpen.value = true;
};

const closeDeleteDialog = (): void => {
    isDeleteDialogOpen.value = false;
    venueToDelete.value = null;
};

const deleteVenue = async (): Promise<void> => {
    if (!venueToDelete.value) return;

    isSubmitting.value = true;
    try {
        const response: any = await httpClient.delete(`${ENDPOINTS.VENUES.DELETE}/${venueToDelete.value.id}`);
        if (response) {
            closeDeleteDialog();
            fetchVenues();
            $toast.success(response.message);
        }
    } catch (error) {
        console.error('Error deleting venue:', error);
    } finally {
        isSubmitting.value = false;
    }
};

const handleMapClick = (): void => {
    selectedVenue.value = null;
};

const openInfoWindow = (venue: Venue): void => {
    selectedVenue.value = venue;
};

const adjustMapView = (): void => {
    const venuesWithCoords = filteredVenues.value;

    if (venuesWithCoords.length === 0) {
        mapCenter.value = { lat: -13.962612, lng: 33.774119 };
        mapZoom.value = 6;
        return;
    }

    if (venuesWithCoords.length === 1) {
        mapCenter.value = {
            lat: Number(venuesWithCoords[0].latitude),
            lng: Number(venuesWithCoords[0].longitude)
        };
        mapZoom.value = 12;
        return;
    }

    let minLat = Infinity;
    let maxLat = -Infinity;
    let minLng = Infinity;
    let maxLng = -Infinity;

    venuesWithCoords.forEach(venue => {
        const lat = Number(venue.latitude);
        const lng = Number(venue.longitude);

        minLat = Math.min(minLat, lat);
        maxLat = Math.max(maxLat, lat);
        minLng = Math.min(minLng, lng);
        maxLng = Math.max(maxLng, lng);
    });

    mapCenter.value = {
        lat: (minLat + maxLat) / 2,
        lng: (minLng + maxLng) / 2
    };

    const latDiff = maxLat - minLat;
    const lngDiff = maxLng - minLng;
    const maxDiff = Math.max(latDiff, lngDiff);

    if (maxDiff > 10) mapZoom.value = 5;
    else if (maxDiff > 5) mapZoom.value = 6;
    else if (maxDiff > 2) mapZoom.value = 7;
    else if (maxDiff > 1) mapZoom.value = 8;
    else if (maxDiff > 0.5) mapZoom.value = 9;
    else if (maxDiff > 0.2) mapZoom.value = 10;
    else if (maxDiff > 0.1) mapZoom.value = 11;
    else mapZoom.value = 12;
};
</script>

<style scoped>
button,
.rounded-none,
[class*="rounded"] {
    border-radius: 0 !important;
}

.ListboxButton,
.ListboxOptions,
.ListboxOption {
    border-radius: 0 !important;
}
</style>