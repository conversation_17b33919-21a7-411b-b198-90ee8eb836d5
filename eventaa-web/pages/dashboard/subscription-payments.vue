<template>
  <div class="p-6 bg-gray-100">
    <div class="mb-6 flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold">Subscription Payments</h1>
        <p class="text-gray-500">Manage subscription plans and payments</p>
      </div>
      <div class="flex space-x-2">
        <button class="px-4 py-2 bg-white border border-gray-300 rounded shadow-sm hover:bg-gray-50 flex items-center">
          <Icon icon="heroicons:arrow-down-tray" class="w-5 h-5 text-gray-600 mr-1" />
          Export
        </button>
        <button class="px-4 py-2 bg-red-600 text-white rounded shadow-sm hover:bg-red-700 flex items-center">
          <Icon icon="heroicons:plus" class="w-5 h-5 mr-1" />
          Add Plan
        </button>
      </div>
    </div>

    <!-- Subscription Plans Section -->
    <div class="mb-6">
      <h2 class="text-lg font-semibold mb-4">Subscription Plans</h2>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div v-for="plan in plans" :key="plan.id" class="bg-white shadow rounded overflow-hidden">
          <div class="p-6 border-b">
            <div class="flex justify-between items-center mb-2">
              <h3 class="text-xl font-bold">{{ plan.name }}</h3>
              <span class="px-2 py-1 text-xs font-semibold rounded-full" 
                :class="plan.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'">
                {{ plan.isActive ? 'Active' : 'Inactive' }}
              </span>
            </div>
            <div class="flex items-baseline mb-4">
              <span class="text-3xl font-bold">{{ formatCurrency(plan.price) }}</span>
              <span class="text-gray-500 ml-1">/ {{ plan.billingCycle }}</span>
            </div>
            <p class="text-gray-600 mb-4">{{ plan.description }}</p>
            <div class="space-y-2">
              <div v-for="(feature, index) in plan.features" :key="index" class="flex items-start">
                <Icon icon="heroicons:check" class="w-5 h-5 text-green-500 mr-2 flex-shrink-0" />
                <span class="text-sm">{{ feature }}</span>
              </div>
            </div>
          </div>
          <div class="p-4 bg-gray-50 flex justify-end space-x-2">
            <button @click="editPlan(plan)" class="text-blue-600 hover:text-blue-900">
              <Icon icon="heroicons:pencil-square" class="w-5 h-5" />
            </button>
            <button @click="togglePlanStatus(plan)" class="text-gray-600 hover:text-gray-900">
              <Icon :icon="plan.isActive ? 'heroicons:pause' : 'heroicons:play'" class="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Subscription Payments Section -->
    <div>
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-lg font-semibold">Recent Subscription Payments</h2>
        <div class="flex items-center space-x-2">
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Icon icon="heroicons:magnifying-glass" class="h-5 w-5 text-gray-400" />
            </div>
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search by user or transaction ID"
              class="block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:border-red-500 focus:ring-red-500"
            />
          </div>
          <select 
            v-model="statusFilter"
            class="border-gray-300 rounded-md shadow-sm focus:border-red-500 focus:ring-red-500"
          >
            <option value="all">All Statuses</option>
            <option value="completed">Completed</option>
            <option value="pending">Pending</option>
            <option value="failed">Failed</option>
            <option value="refunded">Refunded</option>
          </select>
        </div>
      </div>

      <div class="bg-white shadow">
        <div v-if="loading" class="flex justify-center items-center py-20">
          <CoreLoader />
        </div>
        <div v-else>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead>
                <tr>
                  <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Transaction ID</th>
                  <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                  <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Plan</th>
                  <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                  <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                  <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="payment in filteredPayments" :key="payment.id" class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {{ payment.transactionId }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="flex-shrink-0 h-10 w-10">
                        <img class="h-10 w-10 rounded-full" :src="payment.user.avatar || 'https://via.placeholder.com/40'" alt="" />
                      </div>
                      <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900">{{ payment.user.name }}</div>
                        <div class="text-sm text-gray-500">{{ payment.user.email }}</div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {{ payment.plan.name }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {{ formatCurrency(payment.amount) }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {{ formatDate(payment.date) }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full" 
                      :class="{
                        'bg-green-100 text-green-800': payment.status === 'completed',
                        'bg-yellow-100 text-yellow-800': payment.status === 'pending',
                        'bg-red-100 text-red-800': payment.status === 'failed',
                        'bg-gray-100 text-gray-800': payment.status === 'refunded'
                      }">
                      {{ formatStatus(payment.status) }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div class="flex space-x-2">
                      <button @click="viewPaymentDetails(payment)" class="text-gray-600 hover:text-gray-900">
                        <Icon icon="heroicons:eye" class="w-5 h-5" />
                      </button>
                      <button 
                        v-if="payment.status === 'completed'"
                        @click="issueRefund(payment)" 
                        class="text-red-600 hover:text-red-900"
                      >
                        <Icon icon="heroicons:arrow-path" class="w-5 h-5" />
                      </button>
                      <button @click="downloadInvoice(payment)" class="text-blue-600 hover:text-blue-900">
                        <Icon icon="heroicons:document-arrow-down" class="w-5 h-5" />
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Pagination -->
          <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div class="flex-1 flex justify-between sm:hidden">
              <button @click="prevPage" :disabled="currentPage === 1" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                Previous
              </button>
              <button @click="nextPage" :disabled="currentPage === totalPages" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                Next
              </button>
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p class="text-sm text-gray-700">
                  Showing <span class="font-medium">{{ paginationStart }}</span> to <span class="font-medium">{{ paginationEnd }}</span> of <span class="font-medium">{{ totalItems }}</span> payments
                </p>
              </div>
              <div>
                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                  <button @click="prevPage" :disabled="currentPage === 1" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                    <span class="sr-only">Previous</span>
                    <Icon icon="heroicons:chevron-left" class="h-5 w-5" />
                  </button>
                  <button v-for="page in displayedPages" :key="page" @click="goToPage(page)" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium" :class="page === currentPage ? 'z-10 bg-red-50 border-red-500 text-red-600' : 'text-gray-500 hover:bg-gray-50'">
                    {{ page }}
                  </button>
                  <button @click="nextPage" :disabled="currentPage === totalPages" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                    <span class="sr-only">Next</span>
                    <Icon icon="heroicons:chevron-right" class="h-5 w-5" />
                  </button>
                </nav>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { ENDPOINTS } from '@/utils/api';

definePageMeta({
  layout: "dashboard",
});

const loading = ref(true);
const searchQuery = ref('');
const statusFilter = ref('all');
const currentPage = ref(1);
const itemsPerPage = 10;
const totalItems = ref(0);
const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

// Mock data - would be replaced with API calls
const plans = ref([
  {
    id: 1,
    name: 'Basic',
    description: 'Perfect for small events and beginners',
    price: 29.99,
    currency: 'USD',
    billingCycle: 'month',
    isActive: true,
    features: [
      'Up to 3 events per month',
      'Basic analytics',
      'Email support',
      'Standard ticket types'
    ]
  },
  {
    id: 2,
    name: 'Professional',
    description: 'Ideal for growing businesses and regular events',
    price: 79.99,
    currency: 'USD',
    billingCycle: 'month',
    isActive: true,
    features: [
      'Up to 10 events per month',
      'Advanced analytics',
      'Priority email support',
      'Custom ticket types',
      'Attendee management'
    ]
  },
  {
    id: 3,
    name: 'Enterprise',
    description: 'For large organizations with multiple events',
    price: 199.99,
    currency: 'USD',
    billingCycle: 'month',
    isActive: true,
    features: [
      'Unlimited events',
      'Premium analytics',
      '24/7 phone support',
      'Custom branding',
      'API access',
      'Dedicated account manager'
    ]
  }
]);

const payments = ref([
  {
    id: 1,
    transactionId: 'TRX-12345',
    user: {
      name: 'John Doe',
      email: '<EMAIL>',
      avatar: 'https://randomuser.me/api/portraits/men/1.jpg'
    },
    plan: { id: 2, name: 'Professional' },
    amount: 79.99,
    currency: 'USD',
    date: '2024-05-01',
    status: 'completed'
  },
  {
    id: 2,
    transactionId: 'TRX-12346',
    user: {
      name: 'Jane Smith',
      email: '<EMAIL>',
      avatar: 'https://randomuser.me/api/portraits/women/2.jpg'
    },
    plan: { id: 1, name: 'Basic' },
    amount: 29.99,
    currency: 'USD',
    date: '2024-05-02',
    status: 'completed'
  },
  {
    id: 3,
    transactionId: 'TRX-12347',
    user: {
      name: 'Robert Johnson',
      email: '<EMAIL>',
      avatar: 'https://randomuser.me/api/portraits/men/3.jpg'
    },
    plan: { id: 3, name: 'Enterprise' },
    amount: 199.99,
    currency: 'USD',
    date: '2024-05-03',
    status: 'pending'
  },
  {
    id: 4,
    transactionId: 'TRX-12348',
    user: {
      name: 'Emily Davis',
      email: '<EMAIL>',
      avatar: 'https://randomuser.me/api/portraits/women/4.jpg'
    },
    plan: { id: 2, name: 'Professional' },
    amount: 79.99,
    currency: 'USD',
    date: '2024-05-04',
    status: 'failed'
  },
  {
    id: 5,
    transactionId: 'TRX-12349',
    user: {
      name: 'Michael Wilson',
      email: '<EMAIL>',
      avatar: 'https://randomuser.me/api/portraits/men/5.jpg'
    },
    plan: { id: 1, name: 'Basic' },
    amount: 29.99,
    currency: 'USD',
    date: '2024-05-05',
    status: 'refunded'
  }
]);

const filteredPayments = computed(() => {
  let result = [...payments.value];
  
  // Apply search
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(p => 
      p.user.name.toLowerCase().includes(query) || 
      p.user.email.toLowerCase().includes(query) ||
      p.transactionId.toLowerCase().includes(query)
    );
  }
  
  // Apply status filter
  if (statusFilter.value !== 'all') {
    result = result.filter(p => p.status === statusFilter.value);
  }
  
  totalItems.value = result.length;
  
  // Apply pagination
  const start = (currentPage.value - 1) * itemsPerPage;
  const end = start + itemsPerPage;
  return result.slice(start, end);
});

const totalPages = computed(() => Math.ceil(totalItems.value / itemsPerPage));

const paginationStart = computed(() => {
  if (totalItems.value === 0) return 0;
  return (currentPage.value - 1) * itemsPerPage + 1;
});

const paginationEnd = computed(() => {
  return Math.min(currentPage.value * itemsPerPage, totalItems.value);
});

const displayedPages = computed(() => {
  const pages = [];
  const maxPagesToShow = 5;
  
  if (totalPages.value <= maxPagesToShow) {
    for (let i = 1; i <= totalPages.value; i++) {
      pages.push(i);
    }
  } else {
    const leftSide = Math.floor(maxPagesToShow / 2);
    const rightSide = maxPagesToShow - leftSide - 1;
    
    if (currentPage.value > leftSide && currentPage.value < totalPages.value - rightSide) {
      // Middle case
      for (let i = currentPage.value - leftSide; i <= currentPage.value + rightSide; i++) {
        pages.push(i);
      }
    } else if (currentPage.value <= leftSide) {
      // Near the start
      for (let i = 1; i <= maxPagesToShow; i++) {
        pages.push(i);
      }
    } else {
      // Near the end
      for (let i = totalPages.value - maxPagesToShow + 1; i <= totalPages.value; i++) {
        pages.push(i);
      }
    }
  }
  
  return pages;
});

function formatCurrency(amount, currency = 'USD') {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency
  }).format(amount);
}

function formatDate(dateString) {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
}

function formatStatus(status) {
  return status.charAt(0).toUpperCase() + status.slice(1);
}

function prevPage() {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
}

function nextPage() {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
}

function goToPage(page) {
  currentPage.value = page;
}

function editPlan(plan) {
  // Implement edit plan functionality
  console.log('Edit plan:', plan);
  $toast.info(`Editing plan: ${plan.name}`);
}

function togglePlanStatus(plan) {
  // In a real app, this would call an API
  plan.isActive = !plan.isActive;
  const status = plan.isActive ? 'activated' : 'deactivated';
  $toast.success(`Plan "${plan.name}" has been ${status}`);
}

function viewPaymentDetails(payment) {
  // Implement view payment details functionality
  console.log('View payment details:', payment);
  $toast.info(`Viewing details for transaction: ${payment.transactionId}`);
}

function issueRefund(payment) {
  // Implement refund functionality
  console.log('Issue refund for payment:', payment);
  $toast.success(`Refund initiated for transaction: ${payment.transactionId}`);
  
  // Update payment status
  payment.status = 'refunded';
}

function downloadInvoice(payment) {
  // Implement invoice download functionality
  console.log('Download invoice for payment:', payment);
  $toast.success(`Invoice for transaction ${payment.transactionId} is being downloaded`);
}

watch([searchQuery, statusFilter], () => {
  currentPage.value = 1; // Reset to first page when filters change
});

// Simulate API call
onMounted(() => {
  setTimeout(() => {
    loading.value = false;
  }, 1000);
});
</script>
