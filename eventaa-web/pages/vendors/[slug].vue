<template>
    <div class="min-h-screen bg-white dark:bg-zinc-900">
        <div class="container mx-auto px-4 py-4 sm:px-6 lg:px-8">
            <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                <router-link to="/vendors" class="hover:text-sky-600 dark:hover:text-sky-400 transition duration-150">Vendors</router-link>
                <svg class="h-4 w-4 mx-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
                <span class="font-medium text-gray-900 dark:text-white">{{ vendor?.name || 'Vendor Details' }}</span>
            </div>
        </div>

        <main class="container mx-auto px-4 sm:px-6 lg:px-8 pb-12">
            <div v-if="loading" class="grid grid-cols-1 lg:grid-cols-3 gap-4 md:gap-6">
                <div class="lg:col-span-1">
                    <div class="bg-white dark:bg-zinc-800 shadow p-6 relative">
                        <div class="flex flex-col items-center">
                            <div class="w-32 h-32 rounded-full bg-zinc-200 dark:bg-zinc-700 animate-pulse mb-4"></div>
                            <div class="h-6 w-48 bg-zinc-200 dark:bg-zinc-700 animate-pulse mb-4"></div>
                            <div class="flex items-center mb-4">
                                <div class="h-4 w-32 bg-zinc-200 dark:bg-zinc-700 animate-pulse"></div>
                            </div>
                            <div class="flex flex-wrap justify-center gap-2 mb-6 w-full">
                                <div class="h-6 w-24 bg-zinc-200 dark:bg-zinc-700 animate-pulse rounded-full"></div>
                                <div class="h-6 w-20 bg-zinc-200 dark:bg-zinc-700 animate-pulse rounded-full"></div>
                                <div class="h-6 w-28 bg-zinc-200 dark:bg-zinc-700 animate-pulse rounded-full"></div>
                            </div>
                            <div class="h-20 w-full bg-zinc-200 dark:bg-zinc-700 animate-pulse mb-4"></div>
                            <div class="grid grid-cols-3 w-full gap-2 mb-6">
                                <div class="h-10 bg-zinc-200 dark:bg-zinc-700 animate-pulse"></div>
                                <div class="h-10 bg-zinc-200 dark:bg-zinc-700 animate-pulse"></div>
                                <div class="h-10 bg-zinc-200 dark:bg-zinc-700 animate-pulse"></div>
                            </div>
                            <div class="h-12 w-full bg-zinc-200 dark:bg-zinc-700 animate-pulse mb-6"></div>
                            <div class="w-full mt-6 space-y-4">
                                <div class="h-6 w-full bg-zinc-200 dark:bg-zinc-700 animate-pulse"></div>
                                <div class="h-6 w-full bg-zinc-200 dark:bg-zinc-700 animate-pulse"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="lg:col-span-2 shadow p-4 sm:p-5 bg-white dark:bg-zinc-800 ">
                    <div class="border-b border-zinc-200 dark:border-zinc-700 mb-6">
                        <div class="h-10 w-full bg-zinc-200 dark:bg-zinc-700 animate-pulse"></div>
                    </div>
                    <div class="mb-12">
                        <div class="h-8 w-48 bg-zinc-200 dark:bg-zinc-700 animate-pulse mb-6"></div>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                            <div class="md:col-span-2 lg:col-span-4 h-64 bg-zinc-200 dark:bg-zinc-700 animate-pulse "></div>
                            <div class="h-24 bg-zinc-200 dark:bg-zinc-700 animate-pulse "></div>
                            <div class="h-24 bg-zinc-200 dark:bg-zinc-700 animate-pulse "></div>
                            <div class="h-24 bg-zinc-200 dark:bg-zinc-700 animate-pulse "></div>
                            <div class="h-24 bg-zinc-200 dark:bg-zinc-700 animate-pulse "></div>
                        </div>
                    </div>
                    <div class="mb-12">
                        <div class="h-8 w-48 bg-zinc-200 dark:bg-zinc-700 animate-pulse mb-6"></div>
                        <div class="space-y-6">
                            <div class="h-10 w-full bg-zinc-200 dark:bg-zinc-700 animate-pulse"></div>
                            <div class="h-10 w-full bg-zinc-200 dark:bg-zinc-700 animate-pulse"></div>
                            <div class="h-10 w-full bg-zinc-200 dark:bg-zinc-700 animate-pulse"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div v-else class="grid grid-cols-1 lg:grid-cols-3 gap-4 md:gap-6">
                <div class="lg:col-span-1">
                    <div class="bg-white dark:bg-zinc-800 shadow p-2 sm:p-6 relative">
                        <div class="absolute top-4 right-4 flex flex-col items-center">
                            <button @click="toggleLike" class="transition-transform duration-300 hover:scale-110">
                                <svg class="text-zinc-600" v-if="!liked" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M16.696 3C14.652 3 12.887 4.197 12 5.943C11.113 4.197 9.348 3 7.304 3C4.374 3 2 5.457 2 8.481s1.817 5.796 4.165 8.073S12 21 12 21s3.374-2.133 5.835-4.446C20.46 14.088 22 11.514 22 8.481S19.626 3 16.696 3"/></svg>
                                <svg class="text-red-600 animate-pulse" v-else xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path fill="currentColor" d="M16.696 3C14.652 3 12.887 4.197 12 5.943C11.113 4.197 9.348 3 7.304 3C4.374 3 2 5.457 2 8.481s1.817 5.796 4.165 8.073S12 21 12 21s3.374-2.133 5.835-4.446C20.46 14.088 22 11.514 22 8.481S19.626 3 16.696 3"/></svg>
                            </button>
                            <span class="text-xs font-medium mt-1">{{ likesCount }}</span>
                        </div>

                        <div class="flex flex-col items-center">
                            <div class="w-24 h-24 sm:w-32 sm:h-32 rounded-full overflow-hidden border-4 border-white dark:border-zinc-700 shadow-lg mb-4">
                                <img :src="`${runtimeConfig.public.baseUrl}storage/${vendor?.logo}`" :alt="vendor?.name"
                                    class="w-full h-full object-cover" />
                            </div>
                            <h1 class="text-xl sm:text-2xl font-bold text-center mb-1 text-gray-900 dark:text-white">{{ vendor?.name }}</h1>
                            <div v-if="vendor?.is_verified" class="flex items-center mb-4">
                                <span class="mr-1">👍</span>
                                <span class="font-medium dark:text-gray-200">Top Vendor</span>
                            </div>

                            <div class="flex flex-wrap justify-center gap-2 mb-6 w-full">
                                <span v-if="vendor?.is_verified"
                                    class="px-3 py-1 bg-pink-100 dark:bg-pink-900/30 text-pink-600 dark:text-pink-400 rounded-full text-sm">Trusted
                                    Vendor</span>
                                <span class="flex items-center px-3 py-1 bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 rounded-full text-sm">{{
                                    vendor?.is_available ? 'Available' : 'Not Available' }}
                                    <Icon icon="fluent:person-available-16-regular" class="inline-block ml-1 w-4 h-4" />
                                    </span>
                                <span v-for="service in vendor?.services" :key="service.id"
                                    class="px-3 py-1 bg-sky-50 dark:bg-sky-900/30 font-medium text-sky-400 rounded-full text-sm mt-2">
                                    {{ service.service.name }}
                                </span>
                            </div>

                            <p class="text-gray-600 dark:text-gray-300 text-center mb-4">
                                {{ vendor?.bio }}
                                <button v-if="vendor?.bio && vendor?.bio.length > 100"
                                    class="text-red-600 dark:text-red-400 font-medium">Read More</button>
                            </p>

                            <div class="grid grid-cols-2 w-full gap-2 mb-6">
                                <VendorsBookingDialog
                                :vendor-id="vendor?.id ?? 0"
                                :vendor-slug="String($route.params.slug)"
                                @booking-added="handleBookingAdded"
                                ref="bookingDialog"
                              />

                                <VendorsVendorShare v-if="vendor" :vendor="vendor" />
                            </div>

                            <div class="w-full mt-6 space-y-4">
                                <div class="flex justify-between items-center">
                                    <div class="flex items-center text-gray-600 dark:text-gray-300">
                                        <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                        </svg>
                                        <span>Location</span>
                                    </div>
                                    <span class="text-gray-900 dark:text-white font-medium">{{ vendor?.location }}</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <div class="flex items-center text-gray-600 dark:text-gray-300">
                                        <svg class="h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32"><path fill="currentColor" d="M4 4v18h6v6h18V10h-6V4zm2 2h14v4.563L10.562 20H6zm5 2v1H8v2h4.938c-.13 1.148-.481 2.055-1.063 2.688a4.5 4.5 0 0 1-.906-.407C10.266 12.863 10 12.418 10 12H8c0 1.191.734 2.184 1.719 2.844A8.3 8.3 0 0 1 8 15v2c1.773 0 3.25-.406 4.375-1.156c.523.09 1.055.156 1.625.156v-1.875c.543-.91.832-1.973.938-3.125H16V9h-3V8zm10.438 4H26v14H12v-4.563zM20 13.844l-.938 2.844l-2 6l-.062.156V24h2v-.875l.031-.125h1.938l.031.125V24h2v-1.156l-.063-.157l-2-6zm0 6.281l.281.875h-.562z"/></svg>
                                        <span>Languages</span>
                                    </div>
                                    <span class="text-gray-900 dark:text-white font-medium">{{ vendor?.languages }}</span>
                                </div>
                                <div v-if="vendor?.prices && vendor?.prices.length > 0"
                                    class="flex justify-between items-center">
                                    <div class="flex items-center text-gray-600 dark:text-gray-300">
                                        <svg class="h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" width="512" height="512" viewBox="0 0 512 512"><path fill="currentColor" d="m210.6 44.39l-7-4.39c-13.7-8.4-30.8-13.28-45.5-8.7c-15.8 4.92-28.4 17.09-35 35.37l-9.4-4.84c-16.2-8.34-24.68-8.47-31.71-5.31c-5.61 2.51-11.46 8.55-18.09 17.37l82.4 63.71c12.9 4.2 31.8 4.1 50.7-.8c19-4.9 37.9-14.5 51.7-27.4l31.1-76.9c-27.4-21.65-52.4-9.11-69.2 11.89m53.1 76.51c-17 17.2-42.3 28.8-62 34c-6.9 1.8-13.8 3.1-20.5 3.8c-3.7 6.1-6.8 12.3-9.2 18.5c4.8 24.4 13.8 44.4 27.3 60.8l-14.4 12c-8.3-10-15.7-20.8-21.3-32.8c-.9 23.2 4.3 47.2 12.8 72.2l-17.7 6c-15.6-45.6-20.9-92.3 1-136.3c-7.4-.6-14.4-2-20.9-4.3l-.3-.1q-6.45 6.15-12.3 12.9c-31.57 36.6-48.96 85.3-39.86 123.2c4.87 20.3 13.6 39.5 26.16 55.9c18.4-.4 35.8 0 51.6 6c7.5-.8 15.2-1.3 23.2-1.3c28.5 0 54.3 5.3 73.8 14.5c7.6 3.6 14.5 7.9 20 12.8c0-5.3.8-11 2.4-15.2c-8.9-8.4-14.5-18.6-14.5-30.2c0-16.1 10.7-29.4 26.2-39c0-4.6.9-9 2.5-13.2c-10.1-8.7-16.6-19.5-16.6-32.1c0-7.9 2.6-15.1 7-21.6c-4.4-6.4-7-13.6-7-21.5c0-3.9.6-7.5 1.7-11c-9.7-8.6-15.8-19.2-15.8-31.4c0-12.1 6-22.6 15.6-31.1c-5.9-4.6-12.2-8.5-18.9-11.5m111.4 2.3c-26 0-49.5 5.5-65.6 13.6c-16.2 8.1-23.8 18.1-23.8 26.7c0 8.7 7.6 18.7 23.8 26.8c16.1 8.1 39.6 13.6 65.6 13.6c11.3 0 22-1.1 31.9-2.9v-17c13.9-2.1 25.4-5.9 32.8-10.8v17.6c12.5-3.6 24.5-16.9 24.8-27.3c0-8.6-7.6-18.6-23.8-26.7s-39.6-13.6-65.7-13.6m96.5 67.7c-3.3 3.5-7.2 6.8-11.6 9.8l.2 29c12.6-7.5 18.5-16.2 18.5-23.8c0-4.8-2.3-10-7.1-15m-171.8 15.4c.3 8.6 7.9 18.3 23.8 26.3c16.2 8.2 39.6 13.6 65.7 13.6c16.3 0 31.6-2.2 44.7-5.8l.7-27.2c-17.2 6-37.6 9.3-59.6 9.3c-28.5 0-54.4-5.7-74-15.5c-.5-.2-.9-.5-1.3-.7m2 34.8c-1.4 2.7-2 5.4-2 7.9c0 8.7 7.6 18.7 23.8 26.8s39.6 13.5 65.7 13.5c13.2 0 25.7-1.3 37-3.8v-24c-11.6 2.2-24 3.3-37 3.3c-28.6 0-54.5-5.6-74.1-15.5c-4.9-2.4-9.4-5.2-13.4-8.2m174.9 0c-6.1 4.3-11.4 7.5-17.6 10.2v22.3c13.3-7.7 19.6-16.7 19.6-24.6c0-2.5-.6-5.2-2-7.9m7.5 36.8c-2 2-4.2 3.9-6.6 5.8v32.4c10.3-7 15.3-14.7 15.3-21.7c0-5.3-2.9-11-8.7-16.5m-170.1 14c-.1.9-.2 1.7-.2 2.5c0 8.7 7.6 18.6 23.8 26.7c16.2 8.2 39.7 13.6 65.7 13.6c14.9 0 29.1-1.8 41.4-4.8V300c-16.3 5.2-35.2 8-55.5 8c-28.6 0-54.5-5.7-74.1-15.5c-.4-.2-.7-.4-1.1-.6m-13.6 21.4c-8.7 6.5-12.8 13.6-12.8 20c0 8.7 7.6 18.6 23.8 26.8c16.2 8.1 39.6 13.5 65.7 13.5c9.5 0 18.7-.7 27.3-2v-18.2h-1.1c-28.6 0-54.5-5.7-74.1-15.6c-12.5-6.2-22.9-14.5-28.8-24.5M463 343.9c-7.9 2.8-16.5 5.1-25.7 6.6v12.1c1.9-.8 3.8-1.6 5.6-2.5c9.8-5 16.4-10.6 20.1-16.2m9.2 18.2c-3.8 3.8-8.2 7.2-13.1 10.3V401c13.3-7.6 19.6-16.6 19.6-24.5c0-4.6-2.1-9.6-6.5-14.4m-348.7 2.8c-10.2.1-21.2 1.4-32.6 4.1c-22.81 5.3-42.42 15-55.22 25.7c-12.8 10.6-17.8 21.4-16.3 29.1s9.4 14.8 24.8 18.9c15.35 4 36.82 4.2 59.62-1.1c9.2-2.2 17.8-5 25.7-8.3v-20.7c14.6-6.5 25.5-14.3 30.4-21.9v24.4c12.1-10.4 16.8-20.8 15.4-28.4c-1.4-7.7-9.4-14.8-24.8-18.8c-7.7-2-16.9-3.1-27-3m64.6 5.2c2.7 3.9 4.6 8.3 5.6 13.2c1.1 6 .6 11.8-1.2 17.5c9.9 2.6 18.9 6.1 26.7 10.5c4.4 2.4 8.5 5.3 12.1 8.3c9-2.1 16.6-5.1 22-8.7v20.6c16.1-7.6 23.5-16.9 23.5-24.3c0-7.5-7.4-16.8-23.6-24.4c-16.1-7.5-39.3-12.6-65.1-12.7m111.8 5c-.1.4-.1.9-.1 1.4c0 8.7 7.6 18.6 23.8 26.8c16.2 8.1 39.6 13.5 65.7 13.5c13.2 0 25.7-1.4 37-3.8v-26.9c-14.8 4-31.5 6.2-49.1 6.2c-28.6 0-54.5-5.7-74.1-15.5c-1.1-.6-2.2-1.2-3.2-1.7m2.8 37.3c-2 3.3-2.9 6.5-2.9 9.6c0 8.7 7.6 18.6 23.8 26.8c16.2 8.1 39.6 13.5 65.7 13.5c13.2 0 25.7-1.4 37-3.8v-26.4c-11.6 2.2-24 3.4-37 3.4c-28.6 0-54.5-5.7-74.1-15.6c-4.5-2.2-8.7-4.7-12.5-7.5m173.1 0c-5.8 3.9-10.9 7-16.7 9.5v24.6c13.3-7.6 19.6-16.6 19.6-24.5c0-3.1-.9-6.3-2.9-9.6m-292.6 5.4c-3.5 4.4-7.6 8.6-12.2 12.4c-15.6 13.1-37.6 23.7-63 29.6c-9.06 2.1-18.06 3.4-26.7 4.1c3.2 5.3 8.83 10.5 17.07 15.1c13.63 7.7 33.63 12.9 55.83 12.9c10.1 0 19.7-1.1 28.5-3v-20.8c13.8-2.1 25.4-5.9 32.8-10.8v18.4c10-7 14.8-14.9 14.8-22.4c0-8.7-6.5-18-20.2-25.7c-7.4-4.1-16.6-7.5-26.9-9.8"/></svg>
                                        <span>Pricing</span>
                                    </div>
                                    <span class="text-gray-900 dark:text-white font-medium">
                                        {{ formatCurrency(vendor?.prices[0].price, vendor?.prices[0].currency?.name) }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="lg:col-span-2 shadow p-4 sm:p-5 bg-white dark:bg-zinc-800 ">
                    <div class="border-b border-zinc-200 dark:border-zinc-700 mb-6">
                        <nav class="-mb-px flex flex-wrap space-x-4 sm:space-x-8">
                            <button v-for="tab in tabs" :key="tab.id" :class="[
                                activeTab === tab.id
                                    ? 'border-b-4 border-red-500 py-2 px-1 font-medium text-red-600 dark:text-red-400'
                                    : 'border-b-2 border-transparent py-2 px-1 font-medium text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600 transition duration-150'
                            ]" @click="setActiveTab(tab.id)">
                                {{ tab.name }}
                            </button>
                        </nav>
                    </div>

                    <div v-if="activeTab === 'overview' || activeTab === 'gallery'" class="mb-12">
                        <div class="flex justify-between items-center mb-6">
                            <h2 class="text-xl font-bold text-gray-900 dark:text-white">Media Gallery</h2>
                            <button v-if="activeTab !== 'gallery'" @click="setActiveTab('gallery')"
                                class="text-sm text-gray-500 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-400 transition duration-150">See More</button>
                        </div>

                        <div v-if="vendor?.media && vendor?.media.length > 0"
                            class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div class="md:col-span-4 overflow-hidden h-64 relative">
                                <img :src="`${runtimeConfig.public.baseUrl}storage/${vendor?.media[0].path}`"
                                    :alt="vendor?.media[0].title" class="w-full h-full object-cover" />
                            </div>
                            <template v-if="vendor?.media.length > 0">
                                <div v-for="(media) in vendor?.media.slice(1, 4)" :key="media.id"
                                    class="overflow-hidden h-24 relative">
                                    <img :src="`${runtimeConfig.public.baseUrl}storage/${media.path}`" :alt="media.title"
                                        class="w-full h-full object-cover" />
                                </div>
                            </template>
                            <div v-if="vendor?.media.length > 4"
                                class=" overflow-hidden h-24 relative bg-gray-200 flex items-center justify-center cursor-pointer"
                                @click="setActiveTab('gallery')">
                                <span class="text-sm font-medium text-gray-700">Show Gallery</span>
                            </div>
                        </div>
                        <div v-else class="text-center py-8 bg-gray-50 dark:bg-zinc-800 ">
                            <span class="text-gray-500 dark:text-gray-400">No media available</span>
                        </div>
                    </div>

                    <div v-if="activeTab === 'overview' || activeTab === 'services'" class="mb-12">
                        <div class="flex justify-between items-center mb-6">
                            <h2 class="text-xl font-bold text-gray-900 dark:text-white">Services Offered</h2>
                            <button v-if="activeTab !== 'services'" @click="setActiveTab('services')"
                                class="text-sm text-gray-500 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-400 transition duration-150">See More</button>
                        </div>

                        <div v-if="vendor?.services && vendor?.services.length > 0" class="space-y-6">
                            <div v-for="service in vendor?.services" :key="service.id" class="flex items-center">
                                <div
                                    class="flex-shrink-0 h-10 w-10 bg-red-100 flex items-center justify-center mr-4">
                                    <svg class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24"
                                        stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                    </svg>
                                </div>
                                <span class="text-gray-800">{{ service.service.name}}</span>
                            </div>
                        </div>
                        <div v-else class="text-center py-8 bg-gray-50 dark:bg-zinc-800 ">
                            <span class="text-gray-500 dark:text-gray-400">No services listed</span>
                        </div>
                    </div>

                    <div v-if="activeTab === 'overview' || activeTab === 'reviews'">
                        <div class="flex justify-between items-center mb-6">
                            <h2 class="text-xl font-bold text-gray-900 dark:text-white">Reviews</h2>
                            <div class="flex items-center space-x-4">
                                <VendorsRatingDialog
                                  v-if="canRateVendor && authStore.isAuthenticated"
                                  :vendor-id="vendor?.id ?? 0"
                                  @rating-submitted="handleRatingSubmitted"
                                  ref="ratingDialog"
                                />
                                <button v-if="activeTab !== 'reviews'" @click="setActiveTab('reviews')"
                                    class="text-sm text-gray-500 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-400 transition duration-150">See All</button>
                            </div>
                        </div>

                        <div v-if="vendor?.ratings && vendor?.ratings.length > 0"
                            class="flex flex-col md:flex-row items-start">
                            <div class="w-full md:w-1/3 flex flex-col items-center mb-6 md:mb-0">
                                <div class="flex items-center mb-2">
                                    <span class="text-5xl font-bold">{{ averageRating }}</span>
                                    <div class="flex flex-col ml-2">
                                        <svg class="h-8 w-8 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path
                                                d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                        </svg>
                                    </div>
                                </div>
                                <p class="text-center text-sm text-gray-600 mb-4">
                                    {{ vendor?.bio && vendor?.bio.length > 60 ? vendor?.bio.substring(0, 60) + '...' :
                                        vendor?.bio }}
                                </p>
                                <div class="w-full space-y-2">
                                    <div v-for="i in 5" :key="i" class="flex items-center">
                                        <span class="text-sm w-8">{{ 6 - i }}</span>
                                        <div class="flex-1 h-2 bg-gray-200 rounded-full overflow-hidden">
                                            <div class="bg-red-600 h-full"
                                                :style="{ width: getRatingPercentage(6 - i) + '%' }"></div>
                                        </div>
                                    </div>
                                </div>
                                <p class="text-sm text-gray-500 mt-2">Overall Rating ({{ vendor?.ratings.length }}
                                    review{{
                                        vendor?.ratings.length !== 1 ? 's' : '' }})</p>

                                <div v-if="!authStore.isAuthenticated" class="mt-4 text-center">
                                    <p class="text-sm text-gray-500">
                                        <router-link to="/" class="text-red-600 hover:text-red-700">Sign in</router-link> to rate this vendor
                                    </p>
                                </div>
                                <div v-else-if="hasRatedVendor" class="mt-4 text-center">
                                    <p class="text-sm text-gray-500">
                                        You have already rated this vendor
                                    </p>
                                </div>
                                <div v-else-if="!canRateVendor" class="mt-4 text-center">
                                    <p class="text-sm text-gray-500">
                                        You need to have a completed booking with this vendor to rate them
                                    </p>
                                </div>
                            </div>

                        </div>
                        <div v-else class="text-center py-8 bg-gray-50 dark:bg-zinc-800 ">
                            <span class="text-gray-500 dark:text-gray-400">No reviews yet</span>

                            <div v-if="!authStore.isAuthenticated" class="mt-4">
                            </div>
                            <div v-else-if="hasRatedVendor" class="mt-4">
                                <p class="text-sm text-gray-500">
                                    You have already rated this vendor
                                </p>
                            </div>
                            <div v-else-if="!canRateVendor" class="mt-4">
                                <p class="text-sm text-gray-500">
                                    You need to have a completed booking with this vendor to rate them
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
      <VendorsChatPopup
        v-if="vendor"
        :vendor-id="vendor?.slug ?? ''"
        ref="chatPopupRef"
      />
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute } from 'vue-router';
import type { ApiVendor } from '@/types/vendor';
import VendorsVendorShare from '@/components/vendors/VendorShare.vue';
import VendorsRatingDialog from '@/components/vendors/RatingDialog.vue';
import { useAuthStore } from "@/store/auth";

const activeTab = ref<string>('overview');
const loading = ref<boolean>(true);
const liked = ref<boolean>(false);
const likesCount = ref<number>(0);
const vendor = ref<ApiVendor>();
const bookingDialog = ref();
const chatPopupRef = ref();
const ratingDialog = ref();
const canRateVendor = ref<boolean>(false);
const hasRatedVendor = ref<boolean>(false);
const authStore = useAuthStore();
const route = useRoute();
const runtimeConfig = useRuntimeConfig();
const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

useHead(() => ({
  title: vendor.value ? `${vendor.value.name} - EventaHub Vendor` : 'Vendor Details - EventaHub',
  meta: [
    { name: 'description', content: vendor.value?.bio || 'Discover this vendor on EventaHub' },
    { name: 'keywords', content: `event vendor, ${vendor.value?.name || ''}, ${vendor.value?.services?.map(s => s.service.name).join(', ') || ''}` },
    { property: 'og:title', content: vendor.value?.name || 'Vendor Details' },
    { property: 'og:description', content: vendor.value?.bio || 'Discover this vendor on EventaHub' },
    { property: 'og:image', content: vendor.value?.logo ? `${runtimeConfig.public.baseUrl}storage/${vendor.value.logo}` : '' },
    { property: 'og:url', content: `${runtimeConfig.public.baseUrl}vendors/${route.params.slug}` },
    { property: 'og:type', content: 'website' },
    { name: 'twitter:card', content: 'summary_large_image' },
    { name: 'twitter:title', content: vendor.value?.name || 'Vendor Details' },
    { name: 'twitter:description', content: vendor.value?.bio || 'Discover this vendor on EventaHub' },
    { name: 'twitter:image', content: vendor.value?.logo ? `${runtimeConfig.public.baseUrl}storage/${vendor.value.logo}` : '' },
  ],
  link: [
    { rel: 'canonical', href: `${runtimeConfig.public.baseUrl}vendors/${route.params.slug}` }
  ]
}));

const tabs = [
    { id: 'overview', name: 'Overview' },
    { id: 'services', name: 'Services' },
    { id: 'gallery', name: 'Gallery' },
    { id: 'reviews', name: 'Reviews' }
];

const averageRating = computed(() => {
    if (!vendor.value?.ratings || vendor.value?.ratings.length === 0) return '0.0';

    const sum = vendor.value?.ratings.reduce((acc, rating) => acc + rating.rating, 0);
    return (sum / vendor.value?.ratings.length).toFixed(1);
});

const getRatingPercentage = (stars: number) => {
    if (!vendor.value?.ratings || vendor.value?.ratings.length === 0) return 0;

    const count = vendor.value?.ratings.filter(rating => rating.rating === stars).length;
    return Math.round((count / vendor.value?.ratings.length) * 100);
};

const setActiveTab = (tab: string): void => {
    activeTab.value = tab;
};

const fetchVendor = async (): Promise<void> => {
    loading.value = true;
    try {
        const response: ApiVendor = await httpClient.get(`${ENDPOINTS.VENDORS.READ}/${route.params.slug}`);
        if (response) {
            vendor.value = response;
            likesCount.value = response.likes_count || 0;
        }
    } catch (error) {
        console.error(error);
    } finally {
        loading.value = false;
    }
};

const handleBookingAdded = (): void => {
    $toast.success('Booking request submitted successfully');
};

const handleRatingSubmitted = (): void => {
    $toast.success('Rating submitted successfully');
    fetchVendor();
    hasRatedVendor.value = true;
    canRateVendor.value = false;
};

const formatCurrency = (price: string | number, currencyCode: string = 'MWK'): string => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currencyCode,
        minimumFractionDigits: 0
    }).format(Number(price));
};

const toggleLike = async (): Promise<void> => {
    if (!vendor.value) return;

    try {
        const response: {liked: boolean; likes_count: number; message: string} = await httpClient.post(ENDPOINTS.VENDORS.LIKE, {
            vendor_id: vendor.value.id
        });

        if (response) {
            liked.value = response.liked;
            likesCount.value = response.likes_count;
            $toast.success(response.message);
        }
    } catch (error) {
        console.error(error);
        $toast.error('Failed to update favorite status');
    }
};

const openChat = (): void => {
    if (chatPopupRef.value) {
        chatPopupRef.value.openChat();
    }
};

const checkIfLiked = async (): Promise<void> => {
    if (!vendor.value) return;

    try {
        const response: {liked: boolean; likes_count: number} = await httpClient.get(`${ENDPOINTS.VENDORS.LIKE_STATUS}/${vendor.value.id}`);

        if (response) {
            liked.value = response.liked;
            likesCount.value = response.likes_count;
        }
    } catch (error) {
        console.error(error);
    }
};

const structuredData = computed(() => {
  if (!vendor.value) return {};

  return {
    '@context': 'https://schema.org',
    '@type': 'LocalBusiness',
    'name': vendor.value.name,
    'image': vendor.value.logo ? `${runtimeConfig.public.baseUrl}storage/${vendor.value.logo}` : '',
    'description': vendor.value.bio,
    'address': {
      '@type': 'PostalAddress',
      'addressLocality': vendor.value.location
    },
    'telephone': vendor.value.phone,
    'email': vendor.value.business_email,
    'url': `${runtimeConfig.public.baseUrl}vendors/${route.params.slug}`,
    'aggregateRating': vendor.value.ratings && vendor.value.ratings.length > 0 ? {
      '@type': 'AggregateRating',
      'ratingValue': parseFloat(averageRating.value),
      'reviewCount': vendor.value.ratings.length
    } : undefined,
    'priceRange': vendor.value.prices && vendor.value.prices.length > 0 ?
      formatCurrency(vendor.value.prices[0].price, vendor.value.prices[0].currency?.name) :
      undefined
  };
});

const addJsonLd = (): void => {
  if (!vendor.value) return;

  useHead({
    script: [
      {
        type: 'application/ld+json',
        children: JSON.stringify(structuredData.value)
      }
    ]
  });
};

const checkIfCanRate = async (): Promise<void> => {
  if (!authStore.isAuthenticated || !vendor.value) return;

  try {
    const response: {can_rate: boolean; has_rated: boolean} = await httpClient.get(`${ENDPOINTS.VENDORS.RATINGS.CAN_RATE}/${vendor.value.id}`);

    if (response) {
      canRateVendor.value = response.can_rate;
      hasRatedVendor.value = response.has_rated || false;
    }
  } catch (error) {
    console.error(error);
  }
};

onMounted(() => {
  fetchVendor();
});

watch(() => vendor.value, () => {
  if (vendor.value) {
    addJsonLd();
    checkIfLiked();
    checkIfCanRate();
  }
}, { deep: true });

watch(() => authStore.isAuthenticated, () => {
  if (authStore.isAuthenticated && vendor.value) {
    checkIfCanRate();
  }
}, { immediate: true });
</script>

<style scoped></style>