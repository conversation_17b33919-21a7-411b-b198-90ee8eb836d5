interface LocationData {
  latitude: number | null
  longitude: number | null
  city: string | null
  error: string | null
}

export function useGeolocation() {
  const location: Ref<LocationData> = ref({
    latitude: null,
    longitude: null,
    city: null,
    error: null
  })

  const getCurrentLocation = (): Promise<LocationData> => {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        location.value.error = 'Geolocation is not supported by this browser.'
        reject(location.value)
        return
      }

      navigator.geolocation.getCurrentPosition(
        async (position) => {
          location.value.latitude = position.coords.latitude
          location.value.longitude = position.coords.longitude

          try {
            const response = await fetch(
              `https://nominatim.openstreetmap.org/reverse?format=json&lat=${position.coords.latitude}&lon=${position.coords.longitude}`
            )
            
            if (!response.ok) {
              throw new Error('Unable to fetch location details')
            }
            const data = await response.json()
            location.value.city = 
              data.address.city || 
              data.address.town || 
              data.address.village || 
              data.address.municipality || 
              'Unknown Location'

            resolve(location.value)
          } catch (err) {
            location.value.error = 'Failed to retrieve city information'
            reject(location.value)
          }
        },
        (error) => {
          switch(error.code) {
            case error.PERMISSION_DENIED:
              location.value.error = 'User denied the request for Geolocation.'
              break
            case error.POSITION_UNAVAILABLE:
              location.value.error = 'Location information is unavailable.'
              break
            case error.TIMEOUT:
              location.value.error = 'The request to get user location timed out.'
              break
            default:
              location.value.error = 'An unknown error occurred.'
          }
          reject(location.value)
        },
        {
          enableHighAccuracy: true,
          timeout: 5000,
          maximumAge: 0
        }
      )
    })
  }

  return {
    location,
    getCurrentLocation
  }
}