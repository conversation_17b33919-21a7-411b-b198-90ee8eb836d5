<template>
  <div class="flex h-screen relative dashboard-bg-main">
    <DashboardSidebar />
    <div class="flex-1 flex flex-col overflow-hidden">
      <DashboardHeader @toggle-dark-mode="toggleDarkMode" />
      <NuxtLoadingIndicator/>
      <main class="flex-1 overflow-x-hidden overflow-y-auto dashboard-bg-main dashboard-text-primary">
        <NuxtPage />
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useSidebarStore } from '@/store/sidebar';

definePageMeta({
  middleware: ['auth']
});

const isDarkMode = ref<boolean>(false);
const sidebarStore = useSidebarStore();

const toggleDarkMode = (): void => {
  isDarkMode.value = !isDarkMode.value;

  if (isDarkMode.value) {
    document.documentElement.classList.add('dark');
  } else {
    document.documentElement.classList.remove('dark');
  }

  localStorage.setItem('darkMode', isDarkMode.value ? 'true' : 'false');
};

onMounted(async () => {
  const savedDarkMode = localStorage.getItem('darkMode');
  if (savedDarkMode === 'true') {
    isDarkMode.value = true;
    document.documentElement.classList.add('dark');
  }

  // Fetch event count when layout is mounted
  await sidebarStore.fetchEventCount();
});
</script>

<style lang="scss">
:root {
  --ms-tag-font-weight: 500;
  --ms-tag-color: white;
  --ms-radius: 0px;
  --ms-tag-radius: 0px;
  --ms-tag-remove-radius: 0px;
  --ms-dropdown-radius: 0px;
  --ms-tag-bg: #0ea5e9;
  --sx-color-primary: #0ea5e9;
  --sx-color-on-primary: #fff;
  --sx-color-primary-container: #ffdde8;
  --sx-color-on-primary-container: #0ea5e9;
  --sx-color-secondary: #625b71;
  --sx-color-on-secondary: #fff;
  --sx-color-secondary-container: #e8def8;
  --sx-color-on-secondary-container: #1e192b;
  --sx-color-tertiary: #7d5260;
  --sx-color-on-tertiary: #fff;
  --sx-color-tertiary-container: #ffd8e4;
  --sx-color-on-tertiary-container: #370b1e;
  --sx-color-surface: #fef7ff;
  --sx-color-surface-dim: #ded8e1;
  --sx-color-surface-bright: #fef7ff;
  --sx-color-on-surface: #1c1b1f;
  --sx-color-surface-container: #f3edf7;
  --sx-color-surface-container-low: #f7f2fa;
  --sx-color-surface-container-high: #ece6f0;
  --sx-color-background: #fff;
  --sx-color-on-background: #1c1b1f;
  --sx-color-outline: #79747e;
  --sx-color-outline-variant: #c4c7c5;
  --sx-color-shadow: #000;
  --sx-color-surface-tint: #0ea5e9;
  --sx-color-neutral: var(--sx-color-outline);
  --sx-color-neutral-variant: var(--sx-color-outline-variant);
  --sx-internal-color-gray-ripple-background: #e0e0e0;
  --sx-internal-color-light-gray: #fafafa;
  --sx-internal-color-text: #000;
}

.dp__theme_light {
    --dp-font-family: 'Macan';
    --dp-background-color: #fff;
    --dp-text-color: #212121;
    --dp-hover-color: #f3f3f3;
    --dp-cell-border-radius: 0px;
    --dp-border-radius: 0px;
    --dp-hover-text-color: #212121;
    --dp-hover-icon-color: #959595;
    --dp-primary-color: #dc2626;
    --dp-primary-disabled-color: #6bacea;
    --dp-primary-text-color: #f8f5f5;
    --dp-secondary-color: #c0c4cc;
    --dp-border-color: #ddd;
    --dp-menu-border-color: #ddd;
    --dp-border-color-hover: #ddd;
    --dp-border-color-focus: #ddd;
    --dp-disabled-color: #f6f6f6;
    --dp-scroll-bar-background: #f3f3f3;
    --dp-scroll-bar-color: #959595;
    --dp-success-color: #76d275;
    --dp-success-color-disabled: #a3d9b1;
    --dp-icon-color: #959595;
    --dp-danger-color: #ff6f60;
    --dp-marker-color: #ff6f60;
    --dp-tooltip-color: #fafafa;
    --dp-disabled-color-text: #8e8e8e;
    --dp-highlight-color: rgb(25 118 210 / 10%);
    --dp-range-between-dates-background-color: var(--dp-hover-color, #f3f3f3);
    --dp-range-between-dates-text-color: var(--dp-hover-text-color, #212121);
    --dp-range-between-border-color: var(--dp-hover-color, #f3f3f3);
}
</style>
