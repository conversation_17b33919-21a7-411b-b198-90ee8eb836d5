import { defineStore } from 'pinia'
import type { AuthResponse, AuthState } from '@/types/api'
import type { User } from '@/types/user'

export const useAuthStore = defineStore('auth', {
  state: (): AuthState => ({
    user: null,
    token: null,
    message: null,
    isAuthenticated: false
  }),
  getters: {
    currentUser: (state): User | null => state.user,
    userIsAuthenticated: (state): boolean => state.isAuthenticated,
    authToken: (state): string | null => state.token,
  },
  actions: {
    setAuth(authData: AuthResponse) {
      this.user = authData.user
      this.token = authData.token
      this.isAuthenticated = true
    },
    clearAuth() {
      this.user = null
      this.token = null
      this.message = null
      this.isAuthenticated = false
    },
    setUser(user: User){
      this.user = user
    }
  },
  persist: true
})