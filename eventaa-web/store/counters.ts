import { defineStore } from 'pinia';
import { ENDPOINTS } from '@/utils/api';

interface CountersState {
  pendingBookings: number;
  unreadNotifications: number;
  unreadMessages: number;
  pendingReviews: number;
  loading: {
    bookings: boolean;
    notifications: boolean;
    messages: boolean;
    reviews: boolean;
  };
}

export const useCountersStore = defineStore('counters', {
  state: (): CountersState => ({
    pendingBookings: 0,
    unreadNotifications: 0,
    unreadMessages: 0,
    pendingReviews: 0,
    loading: {
      bookings: false,
      notifications: false,
      messages: false,
      reviews: false,
    }
  }),

  actions: {
    async fetchPendingBookings() {
      const httpClient = useHttpClient();
      this.loading.bookings = true;
      
      try {
        const response: any = await httpClient.get(`${ENDPOINTS.VENDORS.BASE}/bookings/pending-count`);
        if (response && typeof response.count === 'number') {
          this.pendingBookings = response.count;
        }
      } catch (error) {
        console.error('Error fetching pending bookings count:', error);
      } finally {
        this.loading.bookings = false;
      }
    },

    async fetchUnreadNotifications() {
      const httpClient = useHttpClient();
      this.loading.notifications = true;
      
      try {
        const response: any = await httpClient.get(`${ENDPOINTS.NOTIFICATIONS.GET}?unread=1&per_page=1`);
        if (response && typeof response.total === 'number') {
          this.unreadNotifications = response.total;
        }
      } catch (error) {
        console.error('Error fetching unread notifications count:', error);
      } finally {
        this.loading.notifications = false;
      }
    },

    async fetchUnreadMessages() {
      const httpClient = useHttpClient();
      this.loading.messages = true;
      
      try {
        const response: any = await httpClient.get(`${ENDPOINTS.VENDORS.BASE}/messages/unread-count`);
        if (response && typeof response.count === 'number') {
          this.unreadMessages = response.count;
        }
      } catch (error) {
        console.error('Error fetching unread messages count:', error);
      } finally {
        this.loading.messages = false;
      }
    },

    async fetchPendingReviews() {
      const httpClient = useHttpClient();
      this.loading.reviews = true;
      
      try {
        const response: any = await httpClient.get(`${ENDPOINTS.VENDORS.BASE}/reviews/pending-count`);
        if (response && typeof response.count === 'number') {
          this.pendingReviews = response.count;
        }
      } catch (error) {
        console.error('Error fetching pending reviews count:', error);
      } finally {
        this.loading.reviews = false;
      }
    },

    async fetchAllCounts() {
      await Promise.all([
        this.fetchPendingBookings(),
        this.fetchUnreadNotifications(),
        this.fetchUnreadMessages(),
        this.fetchPendingReviews()
      ]);
    },

    incrementPendingBookings() {
      this.pendingBookings++;
    },

    decrementPendingBookings() {
      if (this.pendingBookings > 0) {
        this.pendingBookings--;
      }
    },

    incrementUnreadNotifications() {
      this.unreadNotifications++;
    },

    decrementUnreadNotifications() {
      if (this.unreadNotifications > 0) {
        this.unreadNotifications--;
      }
    },

    incrementUnreadMessages() {
      this.unreadMessages++;
    },

    decrementUnreadMessages() {
      if (this.unreadMessages > 0) {
        this.unreadMessages--;
      }
    },

    incrementPendingReviews() {
      this.pendingReviews++;
    },

    decrementPendingReviews() {
      if (this.pendingReviews > 0) {
        this.pendingReviews--;
      }
    },

    resetCounts() {
      this.pendingBookings = 0;
      this.unreadNotifications = 0;
      this.unreadMessages = 0;
      this.pendingReviews = 0;
    }
  }
});
