import { defineStore } from 'pinia'
import { ENDPOINTS } from '@/utils/api'
import { useHttpClient } from '@/composables/useHttpClient'

export interface Plan {
  id: number
  name: string
  tickets_limit: number
  price: number
  currency: string
  attributes: {
    duration: string
    custom_branding: boolean
    featured_events: boolean
    analytics: boolean
    support: string
    sms_notifications: boolean
    multiple_organizers: boolean
    ticket_scanning: boolean
    payment_gateways: string[]
    event_page_customization: boolean
  }
  created_at: string
  updated_at: string
}

export interface UserSubscription {
  id: number
  user_id: number
  plan_id: number
  start_date: string
  end_date: string | null
  created_at: string
  updated_at: string
  plan?: Plan
}

export interface Invoice {
  id: number
  transaction_id: string
  user_id: number
  amount: number
  currency: string
  description: string
  status: 'Paid' | 'Pending' | 'Failed'
  payment_date: string
  created_at: string
  updated_at: string
}

export interface SubscriptionState {
  plans: Plan[]
  currentSubscription: UserSubscription | null
  invoices: Invoice[]
  loading: boolean
  saving: boolean
  error: string | null
}

export const useSubscriptionStore = defineStore('subscription', {
  state: (): SubscriptionState => ({
    plans: [],
    currentSubscription: null,
    invoices: [],
    loading: false,
    saving: false,
    error: null
  }),

  getters: {
    getCurrentPlan: (state): Plan | null => {
      if (!state.currentSubscription || !state.plans.length) return null
      return state.plans.find(plan => plan.id === state.currentSubscription?.plan_id) || null
    },

    getNextBillingDate: (state): string | null => {
      if (!state.currentSubscription) return null
      return state.currentSubscription.end_date || null
    },

    getPlanFeatures: (state) => (planId: number): string[] => {
      const plan = state.plans.find(p => p.id === planId)
      if (!plan) return []

      const attributes = plan.attributes
      const features = []

      if (attributes.custom_branding) features.push('Custom branding')
      if (attributes.featured_events) features.push('Featured events')
      if (attributes.analytics) features.push('Advanced analytics')
      if (attributes.support) features.push(`${attributes.support.charAt(0).toUpperCase() + attributes.support.slice(1)} support`)
      if (attributes.sms_notifications) features.push('SMS notifications')
      if (attributes.multiple_organizers) features.push('Multiple organizers')
      if (attributes.ticket_scanning) features.push('Ticket scanning')
      if (attributes.payment_gateways && attributes.payment_gateways.length) {
        features.push(`${attributes.payment_gateways.length} payment gateways`)
      }
      if (attributes.event_page_customization) features.push('Event page customization')

      return features
    }
  },

  actions: {
    async fetchPlans(): Promise<boolean> {
      try {
        this.loading = true
        this.error = null
        const httpClient = useHttpClient()

        const response = await httpClient.get<any[]>(ENDPOINTS.SUBSCRIPTION.GET_ALL)

        if (response) {
          this.plans = response.map((plan: any) => ({
            ...plan,
            attributes: typeof plan.attributes === 'string'
              ? JSON.parse(plan.attributes)
              : plan.attributes
          }))
          return true
        }

        return false
      } catch (error: any) {
        console.error('Error fetching subscription plans:', error)
        this.error = error.message || 'Failed to fetch subscription plans'
        return false
      } finally {
        this.loading = false
      }
    },

    async fetchCurrentSubscription(): Promise<boolean> {
      try {
        this.loading = true
        this.error = null
        const httpClient = useHttpClient()

        const response = await httpClient.get<{data: UserSubscription}>(ENDPOINTS.SUBSCRIPTION.CURRENT)

        if (response && response.data) {
          this.currentSubscription = response.data
          return true
        }

        return false
      } catch (error: any) {
        console.error('Error fetching current subscription:', error)
        this.error = error.message || 'Failed to fetch current subscription'
        return false
      } finally {
        this.loading = false
      }
    },

    async fetchInvoices(): Promise<boolean> {
      try {
        this.loading = true
        this.error = null
        const httpClient = useHttpClient()

        const response = await httpClient.get<{data: Invoice[]}>(ENDPOINTS.PAYMENTS.INVOICES)

        if (response && response.data) {
          this.invoices = response.data
          return true
        }

        return false
      } catch (error: any) {
        console.error('Error fetching invoices:', error)
        this.error = error.message || 'Failed to fetch invoices'
        return false
      } finally {
        this.loading = false
      }
    },

    async changePlan(planId: number): Promise<boolean> {
      try {
        this.saving = true
        this.error = null
        const httpClient = useHttpClient()

        const response = await httpClient.post<{data: UserSubscription}>(ENDPOINTS.SUBSCRIPTION.CHANGE_PLAN, {
          plan_id: planId
        })

        if (response && response.data) {
          this.currentSubscription = response.data
          return true
        }

        return false
      } catch (error: any) {
        console.error('Error changing subscription plan:', error)
        this.error = error.message || 'Failed to change subscription plan'
        return false
      } finally {
        this.saving = false
      }
    }
  }
})
