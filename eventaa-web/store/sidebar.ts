import { defineStore } from 'pinia'
import type { SidebarItem } from '@/types'
import { useAuthStore } from '@/store/auth'
import { ENDPOINTS } from '@/utils/api'
import { useHttpClient } from '@/composables/useHttpClient'

export interface SidebarSection {
    title: string;
    items: SidebarItem[];
    roles?: string[];
}

export interface SidebarItemWithRoles extends SidebarItem {
    roles?: string[];
}

export const useSidebarStore = defineStore("sidebar", {
    state: () => ({
        isOpen: false,
        active: <string>"",
        eventCount: 0,
        allSections: <SidebarSection[]>[
            {
                title: "Overview",
                items: [
                    {
                        title: "Dashboard",
                        link: "/dashboard",
                        icon: "heroicons:home"
                    },
                    {
                        title: "Analytics",
                        link: "/dashboard/analytics",
                        icon: "heroicons:chart-bar",
                        roles: ['admin', 'host']
                    },
                    {
                        title: "Calendar",
                        link: "/dashboard/calendar",
                        icon: "heroicons:calendar",
                        roles: ['admin', 'host']
                    }
                ]
            },
            {
                title: "Event Management",
                roles: ['admin', 'host'],
                items: [
                    {
                        title: "Events",
                        link: "/dashboard/manage-events",
                        icon: "heroicons:folder"
                    },
                    {
                        title: "Tickets",
                        link: "/dashboard/tickets",
                        icon: "heroicons:qr-code"
                    },
                    {
                        title: "Attendees",
                        link: "/dashboard/attendees",
                        icon: "heroicons:users"
                    },
                    {
                        title: "Check-ins",
                        link: "/dashboard/check-ins",
                        icon: "heroicons:clipboard-document-check"
                    }
                ]
            },
            {
                title: "Vendor Management",
                roles: ['admin'],
                items: [
                    {
                        title: "Bookings",
                        link: "/dashboard/vendor-bookings",
                        icon: "heroicons:calendar"
                    },
                    {
                        title: "Approval",
                        link: "/dashboard/vendor-approval",
                        icon: "heroicons:check-circle"
                    },
                    {
                        title: "Categories",
                        link: "/dashboard/vendor-categories",
                        icon: "heroicons:tag"
                    }
                ]
            },
            {
                title: "Venue Management",
                roles: ['admin'],
                items: [
                    {
                        title: "Venues",
                        link: "/dashboard/venues",
                        icon: "heroicons:building-office-2"
                    },
                    {
                        title: "Create Venue",
                        link: "/dashboard/venues/create",
                        icon: "heroicons:plus-circle"
                    },
                    {
                        title: "Venue Bookings",
                        link: "/dashboard/venue-bookings",
                        icon: "heroicons:calendar-days"
                    }
                ]
            },
            {
                title: "Finance",
                roles: ['admin', 'host'],
                items: [
                    {
                        title: "Subscriptions",
                        link: "/dashboard/subscription-payments",
                        icon: "heroicons:credit-card",
                        roles: ['admin']
                    },
                    {
                        title: "Payments",
                        link: "/dashboard/payments",
                        icon: "heroicons:currency-dollar"
                    },
                    {
                        title: "Invoices",
                        link: "/dashboard/invoices",
                        icon: "heroicons:receipt-percent"
                    },
                    {
                        title: "Reports",
                        link: "/dashboard/reports",
                        icon: "heroicons:document-text"
                    }
                ]
            },
            {
                title: "Marketing",
                roles: ['admin', 'host'],
                items: [
                    {
                        title: "Sponsors",
                        link: "/dashboard/sponsors",
                        icon: "heroicons:briefcase"
                    },
                    {
                        title: "Articles",
                        link: "/dashboard/articles",
                        icon: "heroicons:document"
                    },
                    {
                        title: "Create Article",
                        link: "/dashboard/articles/create",
                        icon: "heroicons:document-plus"
                    },
                    {
                        title: "Email Campaigns",
                        link: "/dashboard/email-campaigns",
                        icon: "heroicons:envelope",
                        roles: ['admin', 'host']
                    },
                    {
                        title: "Social Media",
                        link: "/dashboard/social-media",
                        icon: "heroicons:share",
                        roles: ['admin', 'host']
                    }
                ]
            },
            {
                title: "Communication",
                items: [
                    {
                        title: "Messages",
                        link: "/dashboard/messages",
                        icon: "heroicons:chat-bubble-left-right"
                    },
                    {
                        title: "Notifications",
                        link: "/dashboard/notifications",
                        icon: "heroicons:bell"
                    },
                    {
                        title: "Reviews",
                        link: "/dashboard/reviews",
                        icon: "heroicons:star"
                    }
                ]
            },
            {
                title: "System",
                roles: ['admin'],
                items: [
                    {
                        title: "Users",
                        link: "/dashboard/users",
                        icon: "heroicons:user-group"
                    },
                    {
                        title: "Roles",
                        link: "/dashboard/roles",
                        icon: "heroicons:user-circle"
                    },
                    {
                        title: "Permissions",
                        link: "/dashboard/permissions",
                        icon: "heroicons:lock-closed"
                    },
                    {
                        title: "Settings",
                        link: "/dashboard/settings",
                        icon: "heroicons:cog-6-tooth"
                    }
                ]
            }
        ]
    }),
    getters: {
        sections: (state) => {
            const authStore = useAuthStore();
            const userRoles = authStore.user?.roles || [];

            return state.allSections
                .map(section => {
                    if (section.roles && section.roles.length > 0) {
                        const hasRequiredRole = section.roles.some(role => userRoles.includes(role));
                        if (!hasRequiredRole) {
                            return null;
                        }
                    }

                    const filteredItems = section.items.filter(item => {
                        if (!item.roles || item.roles.length === 0) {
                            return true;
                        }
                        return item.roles.some(role => userRoles.includes(role));
                    }).map(item => ({
                        ...item,
                        count: item.title === 'Events' ? state.eventCount : item.count
                    }));

                    if (filteredItems.length === 0) {
                        return null;
                    }

                    return {
                        ...section,
                        items: filteredItems
                    };
                })
                .filter(section => section !== null) as SidebarSection[];
        }
    },
    actions: {
        toggle() {
            this.isOpen = !this.isOpen;
        },
        async fetchEventCount() {
            try {
                const httpClient = useHttpClient();
                const response: any = await httpClient.get(`${ENDPOINTS.EVENTS.USER}`);
                if (response && response.events && response.events.data) {
                    this.eventCount = response.events.data.length;
                } else if (response && response.events && Array.isArray(response.events)) {
                    this.eventCount = response.events.length;
                } else {
                    this.eventCount = 0;
                }
            } catch (error) {
                console.error('Error fetching event count:', error);
                this.eventCount = 0;
            }
        },
        updateEventCount(count: number) {
            this.eventCount = count;
        },
        incrementEventCount() {
            this.eventCount++;
        },
        decrementEventCount() {
            if (this.eventCount > 0) {
                this.eventCount--;
            }
        }
    },
    persist: true
})
