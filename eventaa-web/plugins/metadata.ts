export default defineNuxtPlugin(async (nuxtApp) => {
    const client = useHttpClient();

    type CategoriesResponse = any;
    type LocationResponse = any;

    const categories = ref<CategoriesResponse[]>([]);
    const location = ref<LocationResponse>({});

    const fetchCategories = async () => {
        try {
            const categoriesResponse = await client.get<CategoriesResponse>(ENDPOINTS.CATEGORIES.BASE);
            categories.value = categoriesResponse;
        } catch (error) {
            console.error("Failed to fetch categories:", error);
        }
    };

    const fetchLocation = async () => {
        try {
            const response: any = await $fetch('https://api.ipify.org?format=json');
            if (response?.ip) {
                const formData = new FormData();
                formData.append("ip", String(response.ip));
                const locationResponse = await client.post<LocationResponse>(ENDPOINTS.METADATA.LOCATION, formData);
                location.value = locationResponse;
            }
        } catch (error) {
            console.error("Failed to fetch location:", error);
        }
    };

    await Promise.all([
        fetchCategories().catch(() => {}),
        fetchLocation().catch(() => {})
    ]);

    nuxtApp.provide("categories", categories);
    nuxtApp.provide("location", location);
});