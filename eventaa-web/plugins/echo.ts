import Echo from 'laravel-echo';
import Pusher from 'pusher-js';
import { useAuthStore } from '@/store/auth';
import { useVendorStore } from '@/store/vendor';

declare global {
  interface Window {
    Pusher: typeof Pusher;
  }
}

export default defineNuxtPlugin(() => {
  const runtimeConfig = useRuntimeConfig();
  const authStore = useAuthStore();
  window.Pusher = Pusher;

  let echo;

  try {
    const apiBaseUrl = runtimeConfig.public.baseUrl || '';
    console.log(apiBaseUrl);
    const wsHost = apiBaseUrl ? new URL(apiBaseUrl).hostname : window.location.hostname;

    echo = new Echo({
      broadcaster: 'reverb',
      key: import.meta.env.VITE_PUSHER_APP_KEY || 'c8ddef6745c937660552',
      cluster: 'us2',
      wsHost: wsHost,
      wsPort: 6001,
      forceTLS: false,
      disableStats: true,
      enabledTransports: ['ws', 'wss'],
      authEndpoint: `${apiBaseUrl}broadcasting/auth`,
      auth: {
        headers: {
          Authorization: `Bearer ${authStore.token || ''}`,
          Accept: 'application/json',
        },
      },
    });

    echo.connector.pusher.connection.bind('error', (err: any) => {
      console.error('Echo connection error:', err);
      if (err && (err.type === 'AuthError' || err.status === 401 || err.error?.status === 401)) {
        console.error('WebSocket authentication error detected, logging out user');
        authStore.clearAuth();

        try {
          const vendorStore = useVendorStore();
          vendorStore.clearPermissions();
        } catch (e) {
          console.error('Failed to clear vendor permissions:', e);
        }

        try {
          const nuxtApp = useNuxtApp();
          if (nuxtApp.$toast) {
            (nuxtApp.$toast as any).error('Your session has expired. Please log in again.');
          }
        } catch (e) {
          console.error('Failed to show toast:', e);
        }

        navigateTo('/', { replace: true });
      }
    });

    echo.connector.pusher.connection.bind('connected', () => {
      console.log('Echo connected successfully');
    });

  } catch (error) {
    console.error('Failed to initialize Echo:', error);
    echo = {};
  }

  authStore.$subscribe((_mutation, state) => {
    if (echo && echo.connector && echo.connector.options && echo.connector.options.auth) {
      echo.connector.options.auth.headers.Authorization = `Bearer ${state.token || ''}`;
    }
  });

  return {
    provide: {
      echo
    }
  };
});
