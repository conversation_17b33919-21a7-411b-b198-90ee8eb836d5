{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@ckeditor/ckeditor5-build-classic": "^43.3.1", "@ckeditor/ckeditor5-vue": "^7.2.0", "@formkit/nuxt": "^1.6.5", "@headlessui/vue": "^1.7.22", "@heroicons/vue": "^2.1.5", "@nuxtjs/color-mode": "^3.5.2", "@nuxtjs/tailwindcss": "^6.12.1", "@pinia-plugin-persistedstate/nuxt": "^1.2.1", "@pinia/nuxt": "^0.5.5", "@popperjs/core": "^2.11.8", "@schedule-x/calendar": "^2.4.1", "@schedule-x/theme-default": "^2.4.1", "@schedule-x/vue": "^2.3.1", "@vueform/multiselect": "^2.6.9", "@vueform/nuxt": "^1.8.0", "@vuepic/vue-datepicker": "^9.0.3", "@vueup/vue-quill": "^1.2.0", "chart.js": "^4.4.9", "ckeditor5": "^43.3.1", "color-thief-browser": "^2.0.2", "d3": "^7.9.0", "dayjs": "^1.11.13", "jspdf-autotable": "^5.0.2", "laravel-echo": "^1.17.1", "lodash": "^4.17.21", "node-sass": "^9.0.0", "nuxt": "^3.12.3", "pinia": "^2.2.4", "pusher-js": "^8.4.0-rc2", "qrcode.vue": "^3.6.0", "sass": "^1.77.8", "uuid": "^11.0.2", "v-calendar": "^3.1.2", "vue": "latest", "vue-advanced-cropper": "^2.8.9", "vue-chartjs": "^5.3.2", "vue-country-flag-next": "^2.3.2", "vue3-country-region-select": "^1.0.0", "vue3-easy-data-table": "^1.5.47", "vue3-google-login": "^2.0.29", "vue3-google-map": "^0.21.0", "vue3-json-excel": "^1.0.10-alpha", "vue3-toastify": "^0.2.3", "vuedraggable": "^4.1.0"}, "devDependencies": {"@iconify/vue": "^4.1.2"}}