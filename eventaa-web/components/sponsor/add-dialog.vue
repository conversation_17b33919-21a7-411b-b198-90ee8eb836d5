<template>
    <div>
        <button type="button" @click="openModal"
            class="flex items-center bg-red-600 py-2 px-2 text-white hover:bg-red-700 transition-colors">
            <Icon icon="fluent:add-16-filled" class="mr-2 h-5 w-5" />
            Add sponsor
        </button>
    </div>
    <TransitionRoot appear :show="isOpen" as="template">
        <Dialog as="div" @close="closeModal" class="relative z-10">
            <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
                leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                <div class="fixed inset-0 bg-black/25" />
            </TransitionChild>

            <div class="fixed inset-0 overflow-y-auto">
                <div class="flex min-h-full items-center justify-center">
                    <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
                        enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
                        leave-to="opacity-0 scale-95">
                        <DialogPanel
                            class="w-full max-w-xl transform overflow-hidden bg-white shadow-xl transition-all">
                            <DialogTitle class="w-full flex items-center justify-between px-4 py-2 border-b">
                                <h3 class="text-xl font-semibold">Create a sponsor</h3>
                                <button type="button" @click="closeModal"
                                    class="text-gray-500 hover:text-gray-700 transition-colors">
                                    <Icon icon="iconamoon:close" class="h-6 w-6" />
                                </button>
                            </DialogTitle>

                            <FormKit type="form" id="createSponsorForm" @submit="onFormSubmit" :actions="false"
                                #default="{ state: { valid } }">
                                <div class="space-y-4 px-5">
                                    <FormKit type="text" v-model="formData.name" label="Name" name="name"
                                        placeholder="Enter sponsor name" validation="required|string|length:3,100"
                                        :validation-messages="{
                                            required: 'Sponsor name is required',
                                            length: 'Name must be between 3 and 100 characters'
                                        }" />

                                    <EventsImagePicker label="Logo" :max-files="1" accept="image/*"
                                        @files-selected="onLogoPicked" @file-removed="onFileRemoved" />

                                    <div>
                                        <h3 class="text-lg font-semibold mb-2">Sponsor Headquarters</h3>
                                        <CoreLocationPicker @update:location="onUpdateLocation"/>
                                    </div>

                                    <FormKit type="text" v-model="formData.address" label="Address" name="address"
                                        placeholder="Enter address" validation="required|string" />

                                    <div class="grid grid-cols-2 gap-4">
                                        <FormKit type="text" v-model="formData.city" label="City" name="city"
                                            placeholder="Enter city" validation="required|string" />
                                        <FormKit type="text" v-model="formData.country" label="Country" name="country"
                                            placeholder="Enter country" validation="required|string" />
                                    </div>
                                </div>

                                <div class="mt-6 flex justify-end space-x-3 px-4 py-3 border-t">
                                    <button type="button" @click="closeModal"
                                        class="px-4 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 bg-white border border-gray-300 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                        Cancel
                                    </button>
                                    <button type="submit" :disabled="isSubmitting || !valid"
                                        class="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed">
                                        {{ isSubmitting ? 'Creating...' : 'Create Sponsor' }}
                                    </button>
                                </div>
                            </FormKit>
                        </DialogPanel>
                    </TransitionChild>
                </div>
            </div>
        </Dialog>
    </TransitionRoot>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
} from '@headlessui/vue'

interface FormData {
    name: string
    logo: File | null
    latitude: string
    longitude: string
    address: string
    city: string
    country: string
}

interface CreateSponsorPayload {
    name: string
    logo: File | null
    latitude: number
    longitude: number
    address: string
    city: string
    country: string
}

const emits = defineEmits(['sponsor-created'])

const { $toast }: any = useNuxtApp();
const isOpen = ref<boolean>(false);
const httpClient = useHttpClient();
const isSubmitting = ref<boolean>(false);

const formData = reactive<FormData>({
    name: '',
    logo: null,
    latitude: '',
    longitude: '',
    address: '',
    city: '',
    country: ''
})

const closeModal = (): void => {
    isOpen.value = false
    resetForm()
}

const openModal = (): void => {
    isOpen.value = true
}

const resetForm = (): void => {
    formData.name = ''
    formData.logo = null
    formData.latitude = ''
    formData.longitude = ''
    formData.address = ''
    formData.city = ''
    formData.country = ''
}

const onLogoPicked = (files: File[]): void => {
    if (files.length > 0) {
        formData.logo = files[0]
    }
}

const onUpdateLocation = (e: any) => {
    formData.latitude = e.latitude || '';
    formData.longitude = e.longitude || '';
}

const onFileRemoved = (): void => {
    formData.logo = null
}

const createSponsor = async (payload: CreateSponsorPayload): Promise<void> => {
    try {
        const formData = new FormData()
        Object.entries(payload).forEach(([key, value]) => {
            if (value !== null) {
                formData.append(key, value)
            }
        })

        const response = await httpClient.post(ENDPOINTS.SPONSORS.CREATE, formData)
        if (response) {
            $toast.success('Sponsor created successfully')
            emits('sponsor-created', true);
            closeModal()
        }
    } catch (error) {
        console.error('Error creating sponsor:', error)
        $toast.error('Failed to create sponsor')
    }
}

const onFormSubmit = async (): Promise<void> => {
    try {
        isSubmitting.value = true

        const payload: CreateSponsorPayload = {
            name: formData.name,
            logo: formData.logo,
            latitude: parseFloat(formData.latitude),
            longitude: parseFloat(formData.longitude),
            address: formData.address,
            city: formData.city,
            country: formData.country
        }

        await createSponsor(payload)
    } catch (error) {
        console.error('Error in form submission:', error)
        $toast.error('Please check your form inputs')
    } finally {
        isSubmitting.value = false
    }
}
</script>