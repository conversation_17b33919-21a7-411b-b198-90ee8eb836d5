<template>
    <div>
        <button @click="openModal" class="cursor-pointer">
            <Icon icon="cuida:trash-outline" class="w-6 h-6" />
        </button>
    </div>

    <TransitionRoot appear :show="isOpen" as="template">
        <Dialog as="div" @close="closeModal" class="relative z-10">
            <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
                leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                <div class="fixed inset-0 bg-black/25" />
            </TransitionChild>

            <div class="fixed inset-0 overflow-y-auto">
                <div class="flex min-h-full items-center justify-center p-4">
                    <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
                        enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
                        leave-to="opacity-0 scale-95">
                        <DialogPanel
                            class="w-full max-w-md transform overflow-hidden bg-white shadow-xl transition-all">
                            <DialogTitle class="w-full flex items-center justify-between px-4 py-2 border-b">
                                <h3 class="text-xl font-semibold">Delete Sponsor</h3>
                                <button type="button" @click="closeModal"
                                    class="text-gray-500 hover:text-gray-700 transition-colors">
                                    <Icon icon="iconamoon:close" class="h-6 w-6" />
                                </button>
                            </DialogTitle>

                            <div class="p-6">
                                <p class="text-gray-600">
                                    Are you sure you want to delete this sponsor? This action cannot be undone.
                                </p>
                            </div>

                            <div class="flex justify-end space-x-3 px-4 py-3 border-t">
                                <button type="button" @click="closeModal"
                                    class="px-4 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 bg-white border border-gray-300 hover:bg-gray-50">
                                    Cancel
                                </button>
                                <button @click="confirmDelete" :disabled="isDeleting"
                                    class="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed">
                                    {{ isDeleting ? 'Deleting...' : 'Delete Sponsor' }}
                                </button>
                            </div>
                        </DialogPanel>
                    </TransitionChild>
                </div>
            </div>
        </Dialog>
    </TransitionRoot>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
} from '@headlessui/vue'

const props = defineProps<{
    sponsorId: number
}>()

const emit = defineEmits(['sponsor-deleted'])

const { $toast }: any = useNuxtApp()
const httpClient = useHttpClient()
const isOpen = ref(false)
const isDeleting = ref(false)

const openModal = () => {
    isOpen.value = true
}

const closeModal = () => {
    isOpen.value = false
}

const confirmDelete = async () => {
    try {
        isDeleting.value = true
        await httpClient.delete(`${ENDPOINTS.SPONSORS.DELETE}/${props.sponsorId}`)
        $toast.success('Sponsor deleted successfully')
        emit('sponsor-deleted')
        closeModal()
    } catch (error) {
        console.error('Error deleting sponsor:', error)
        $toast.error('Failed to delete sponsor')
    } finally {
        isDeleting.value = false
    }
}
</script>