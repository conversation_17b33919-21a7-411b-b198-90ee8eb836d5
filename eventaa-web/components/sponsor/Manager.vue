<template>
    <div class="px-5 py-5">
        <div>
            <div>
                <h3 class="mb-2 text-lg font-semibold">Select sponsors</h3>
                <div class="flex space-x-4">
                    <MultiSelect v-model="selectedSponsors" mode="tags"
                        :options="sponsors.map((sponsor) => sponsor.name)" />
                    <div class="w-48">
                        <SponsorAddDialog @sponsor-created="fetchSponsors" />
                    </div>
                </div>
            </div>

            <div v-if="filteredSponsors.length === 0" class="flex flex-col items-center justify-center">
                <img src="/assets/svgs/selected.svg" alt="not-selected-svg" class="w-auto h-72" />
                <p class="text-gray-500 text-base">No sponsors selected</p>
            </div>

            <div class="w-full mt-4">
                <div class="relative border-b" v-for="sponsor in filteredSponsors" :key="sponsor.name">
                    <div class="px-2 py-2 bg-gray-50">
                        <div class="w-20 h-20 flex items-center justify-center">
                            <img class="w-16 h-16 object-cover" :src="getSponsorLogoUrl(sponsor.logo)" />
                        </div>
                        <h3 class="text-lg font-semibold">{{ sponsor.name }}</h3>
                        <p class="text-base text-gray-500">
                            {{ sponsor.address }}, {{ sponsor.city }}, {{ sponsor.country }}
                        </p>
                    </div>
                    <div class="absolute top-0 right-0">
                        <button type="button" @click="openGoogleMaps(sponsor)"
                            class="flex items-center justify-center bg-red-600 py-2 px-2 text-white hover:bg-red-700 transition-colors"
                            title="View on Google Maps">
                            <Icon icon="gis:poi-map-o" class="h-6 w-6 text-white" />
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import type { Sponsor } from '@/types';

const runtimeConfig = useRuntimeConfig();
const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

const sponsors = ref<Sponsor[]>([]);
const selectedSponsors = ref<string[]>([]);
const loading = ref<boolean>(false);

const emit = defineEmits(['update:selectedSponsors']);

const filteredSponsors = computed(() => {
    return sponsors.value.filter((sponsor) =>
        selectedSponsors.value.includes(sponsor.name)
    );
});

const fetchSponsors = async (): Promise<void> => {
    loading.value = true;
    try {
        const response = await httpClient.get<{ sponsors: Sponsor[] }>(ENDPOINTS.SPONSORS.READ);
        if (response) {
            sponsors.value = response.sponsors;
        }
    } catch (error) {
        console.error('Error fetching sponsors:', error);
        $toast.error('Failed to load sponsors');
    } finally {
        loading.value = false;
    }
};

const getSponsorLogoUrl = (logo: string): string => {
    return `${runtimeConfig.public.baseUrl}storage/sponsors/${logo}`;
};

const openGoogleMaps = (sponsor: Sponsor): void => {
    if (!sponsor.address || !sponsor.city || !sponsor.country) {
        $toast.warning('Address information is incomplete');
        return;
    }

    const address = encodeURIComponent(`${sponsor.address}, ${sponsor.city}, ${sponsor.country}`);
    const mapsUrl = `https://www.google.com/maps/search/?api=1&query=${address}`;
    window.open(mapsUrl, '_blank');
};

watch(selectedSponsors, () => {
    emit('update:selectedSponsors', filteredSponsors.value);
});

onMounted(() => {
    fetchSponsors();
});
</script>