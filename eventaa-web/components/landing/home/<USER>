<template>
  <div class="px-5 py-5">
    <h2 class="text-2xl font-bold uppercase tracking-wide text-center">
      Become an Event Host
    </h2>
    <p class="text-base text-center text-gray-500 w-full md:w-1/2 mx-auto">
      It takes less than 5 minutes to reach your audience for your events, sell
      tickets, generate revenue, and get ratings.
    </p>
    <div class="w-full grid grid-cols-1 sm:grid-cols-3 lg:grid-cols-3 gap-5 mt-5">
      <div v-for="(card, index) in cards" :key="index" class="relative overflow-hidden" :class="card.bgColor">
        <div class="p-6 text-white h-full relative">
          <!-- Text content with higher z-index -->
          <div class="space-y-4 relative z-10">
            <h3 class="text-xl font-medium">{{ card.title }}</h3>
            <p class="text-sm opacity-90">{{ card.description }}</p>
          </div>

          <!-- Background elements with lower z-index -->
          <div class="absolute top-3 right-3 bg-white/20 rounded-full p-2 z-0">
            <div class="flex items-center justify-center">
              <img :src="card.icon" class="object-cover w-auto h-32" :alt="card.iconAlt" />
            </div>
          </div>
          <div class="absolute -bottom-8 -right-8 w-32 h-32 rounded-full bg-white/10 z-0"></div>
          <div class="absolute -bottom-10 -right-10 w-40 h-40 rounded-full bg-white/5 z-0"></div>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
const cards = [
  {
    title: 'Create A Free Account',
    description: 'Register your account on our platform and start creating events. It takes less than 5 minutes to set up your account.',
    icon: new URL('@/assets/icons/add-user.png', import.meta.url).href,
    iconAlt: 'add-user-icon',
    bgColor: 'bg-red-500'
  },
  {
    title: 'Subscribe',
    description: 'Select any of our packages and subscribe to get started. You can also subscribe to multiple packages at once.',
    icon: new URL('@/assets/icons/credit-card.png', import.meta.url).href,
    iconAlt: 'credit-card-icon',
    bgColor: 'bg-emerald-500'
  },
  {
    title: 'Create Events & Tickets',
    description: 'Set up your event details and generate tickets to sell both online and on-location.',
    icon: new URL('@/assets/icons/ticket.png', import.meta.url).href,
    iconAlt: 'ticket-icon',
    bgColor: 'bg-orange-500'
  }
];
</script>

<style scoped>
.card-shadow {
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
}
</style>