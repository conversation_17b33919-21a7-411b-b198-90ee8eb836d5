<template>
  <div class="py-5 px-5">
    <div>
      <h3 class="text-2xl font-semibold">Recently posted</h3>
      <p class="text-gray-500 text-base">Past 7 days</p>
      <div>
        <div class="w-full grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-5 mt-5">
          <EventsCard v-for="event in events.slice(0, 3)" v-bind:key="event.slug" :event="event" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { EventItem } from '~/types';

const httpClient = useHttpClient();
const loading = ref<boolean>(false);
const events = ref<EventItem[]>([]);

const fetchRecentEvents = async () => {
  try {
    const response = await httpClient.get<{ events: { data: any } }>(ENDPOINTS.EVENTS.GET);
    if (response) {
      events.value = response.events.data;
    }
  } catch (error) {
    console.error(error)
  }
}

onMounted(async () => {
  await fetchRecentEvents();
})
</script>

<style lang="scss" scoped></style>
