<template>
  <div class="relative overflow-hidden">
    <div class="moving-background"></div>
    <div class="flex flex-col sm:py-20 py-10 bg-cover bg-center text-white relative z-10">
      <div class="sm:p-8 p-5 rounded-lg">
        <h3 class="text-3xl sm:text-5xl font-semibold">
          The best event booking app in Malawi
        </h3>
        <p class="font-medium text-gray-100 text-base">
          Find "Festivals, Parties, Conferences, Arts & Theater, Sports"
          events and more...
        </p>
        <div class="w-full flex flex-col sm:flex-row items-center mt-3 bg-white">
          <div class="w-full flex items-center sm:flex-row flex-col">
            <div v-if="computedCategories.length > 1" class="border-b sm:border-b-0 sm:border-r w-full sm:w-auto">
              <CoreImageDropdown :items="computedCategories" v-model="selectedCategory" />
            </div>
            <input type="search" class="w-full outline-none focus:outline-none bg-white px-2 py-2 text-gray-500"
              placeholder="Search for events" v-model="search" @keydown.enter="onSearch" />
          </div>
          <div class="flex items-center space-x-3 px-2 py-2 mt-2 sm:mt-0">
            <div class="relative">
              <button class="bg-gray-50 border border-gray-100 rounded-full px-2 py-2"
                @mouseleave="locationTooltip = true" @click="allowCurrentLocation">
                <Icon icon="tabler:current-location" class="w-5 h-5"
                  :class="useCurrentLocation ? 'text-sky-500' : 'text-gray-400'" />
              </button>
              <div v-if="locationTooltip"
                class="absolute text-sm font-medium left-1/2 transform -translate-x-1/2 -translate-y-full -top-1/2 bg-black bg-opacity-25 p-2 text-gray-100 shadow-md pointer-events-none">
                Click here to search by your current location
                <div
                  class="absolute left-1/2 transform -translate-x-1/2 translate-y-full w-0 h-0 border-l-8 border-r-8 border-t-8 border-l-transparent border-r-transparent border-t-black border-opacity-25">
                </div>
              </div>
            </div>

            <button v-if="!isLocationFetching" :disabled="isLocationFetching" @click="onSearch"
              class="w-full bg-red-600 hover:bg-red-700 transition duration-300 ease-in-out text-white px-3 py-2 flex items-center space-x-2">
              <Icon icon="gg:search" class="w-5 h-5 mr-2" />
              Search
            </button>
            <div v-else class="w-full text-sky-500 font-thin flex items-center space-x-2">
              <CoreLoader width="25" height="25" color="#0ea5e9" class="mr-2"/>
              Fetching...
            </div>
          </div>
        </div>
        <div class="flex space-x-2 py-3 rounded-lg">
          <button v-for="(item, index) in searchItems" :key="index" @click="onSearchTags(item)"
            class="flex flex-row items-center sm:text-base text-sm bg-black bg-opacity-25 hover:bg-opacity-50 transition duration-150 rounded-full px-3 py-1">
            <Icon icon="icon-park-twotone:search" class="mr-1" />
            {{ item }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Category } from '@/types';

const emits = defineEmits(['onQuery', 'tags']);
const locationTooltip = ref<boolean>(false);
const useCurrentLocation = ref<boolean>(false);
const { $categories, $toast }: any = useNuxtApp();
const defaultCategory = { id: '', name: 'Select category', icon: 'other.png' };
const computedCategories = computed(() => {
  const categoryList = $categories && Array.isArray($categories) ? $categories : [];
  return [defaultCategory, ...categoryList];
});
const searchItems: string[] = ["Trending", "Nearby Me", "Recommended"];
const selectedCategory = ref<Category>(defaultCategory);
const search = ref<string>("");
const isLocationFetching = ref<boolean>(false);
const location = ref<any>({});
const { getCurrentLocation } = useGeolocation();

const allowCurrentLocation = async (): Promise<void> => {
  useCurrentLocation.value = !useCurrentLocation.value;
  if (useCurrentLocation.value) {
    try {
      isLocationFetching.value = true;
      const response = await getCurrentLocation();
      if (response) {
        location.value = response;
        $toast?.success("Nearby events of 100km will be shown...");
      }
    } catch (error: any) {
      $toast?.error("Unable to retrieve current location. Using default search.");
    }
    finally {
      isLocationFetching.value = false;
    }
  }
}

const onSearch = async (): Promise<void> => {
  if (location.value.latitude && location.value.longitude) {
    emits('onQuery', {
      search: search.value,
      category: selectedCategory.value.id,
      location: location.value,
    });
  } else {
    $toast?.error("Unable to retrieve current location. Using default search.");
    performDefaultSearch();
  }
}

const performDefaultSearch = () => {
  emits('onQuery', {
    search: search.value,
    category: selectedCategory.value.id,
    location: null
  });
}

const onSearchTags = (tag: string): void => {
  emits('tags', tag);
}
</script>

<style lang="scss" scoped>
.moving-background {
  position: absolute;
  top: -50px;
  left: -50px;
  right: -50px;
  bottom: -50px;
  background-image: url("../../../assets/images/hero.png");
  background-size: cover;
  background-position: center;
  animation: moveBackground 30s linear infinite;
}

@keyframes moveBackground {
  0% {
    transform: translate(0, 0) scale(1.1);
  }

  50% {
    transform: translate(-20px, -20px) scale(1.2);
  }

  100% {
    transform: translate(0, 0) scale(1.1);
  }
}

.body {
  justify-content: baseline;
}
</style>