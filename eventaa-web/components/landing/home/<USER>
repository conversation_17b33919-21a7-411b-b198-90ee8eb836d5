<template>
    <div class="w-full flex flex-col items-center justify-center gap-1 py-10">
        <h3 class="text-2xl font-semibold tracking-wider uppercase">Subscribe to our newsletter</h3>
        <p class="w-1/2 mx-auto justify-center text-center text-gray-600">
            Get the latest updates on events and other news from eventa malawi. Subscribe to our newsletter to stay up to date.
        </p>
        <div class="mt-2">
            <FormKit id="editForm" type="form" submit-label="Update" :actions="false" #default="{ value }">
                <div class="flex items-center">
                    <FormKit type="email" required placeholder="Enter your email address" prefixIcon="email" label=""/>
                    <button class="text-center text-white bg-red-600 py-2 mt-2 px-2">Subscribe</button>
                </div>
            </FormKit>
        </div>
    </div>
</template>

<script setup lang="ts">
</script>