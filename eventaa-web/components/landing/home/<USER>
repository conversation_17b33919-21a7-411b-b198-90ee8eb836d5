<template>
  <div class="relative w-full mb-5 p-5">
    <div>
      <h2 class="text-2xl font-bold mb-4">Explore By Categories</h2>
    </div>
    <div class="relative flex items-center">
      <button class="absolute left-0 bg-red-600 text-white p-2 z-10" @click="prevSlide" :disabled="currentIndex === 0">
        <Icon icon="gg:push-left" class="w-5 h-5" />
      </button>
      <div class="scroll-container flex overflow-x-auto space-x-5 sm:space-x-10 mx-auto">
        <NuxtLink :to="`/events?category=${slide.name}`" v-for="(slide, index) in $categories" :key="index"
          class="w-full flex border border-red-50 hover:scale-105 transition duration-150 rounded relative">
          <div class="w-20 px-2.5 h-full bg-red-50 rounded-l flex items-center justify-center">
            <img :src="`${runtimeConfig.public.baseUrl}storage/categories/${slide.icon}`" :alt="slide.name"
              class="w-10 h-10" />
          </div>
          <div class="p-2">
            <h3 class="text-lg font-semibold">{{ slide.name }}</h3>
            <p class="text-base text-gray-500">{{ slide.events_count }} events found</p>
          </div>
        </NuxtLink>
      </div>
      <button class="absolute right-0 bg-red-600 text-white p-2 z-10" @click="nextSlide"
        :disabled="currentIndex >= $categories.length - 1">
        <Icon icon="streamline:next" class="w-5 h-5" />
      </button>
    </div>
  </div>
</template>

<script lang="ts" setup>
const currentIndex = ref<number>(0);
const { $categories }: any = useNuxtApp();
const runtimeConfig = useRuntimeConfig();

const nextSlide = (): void => {
  const scrollContainer = document.querySelector(".scroll-container");
  scrollContainer?.scrollBy({ left: 300, behavior: "smooth" });
  if (currentIndex.value < $categories.value.length - 1) {
    currentIndex.value++;
  }
};

const prevSlide = (): void => {
  const scrollContainer = document.querySelector(".scroll-container");
  scrollContainer?.scrollBy({ left: -300, behavior: "smooth" });
  if (currentIndex.value > 0) {
    currentIndex.value--;
  }
};
</script>

<style scoped>
.scroll-container::-webkit-scrollbar {
  display: none;
}

.scroll-container {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
</style>