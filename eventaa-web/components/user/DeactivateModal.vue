<template>
    <div>
        <CorePrimaryButton @click="openModal" text="Deactivate" />
    </div>
    <TransitionRoot appear :show="isOpen" as="template">
        <Dialog as="div" @close="closeModal" class="relative z-10">
            <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
                leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                <div class="fixed inset-0 bg-black/25" />
            </TransitionChild>

            <div class="fixed inset-0 overflow-y-auto">
                <div class="flex min-h-full items-center justify-center p-4 text-center">
                    <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
                        enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
                        leave-to="opacity-0 scale-95">
                        <DialogPanel
                            class="w-full max-w-xl transform overflow-hidden bg-white text-left align-middle shadow-xl transition-all">
                            <DialogTitle as="h3" class="w-full border-b px-4 py-3 flex text-xl font-semibold leading-6 text-gray-900">
                                <Icon icon="emojione-v1:warning" class="w-6 h-6 mr-2"/>
                                Delete Account
                            </DialogTitle>
                            <div class="mt-2 px-5">
                                <p class="sm:text-base text-sm text-gray-500 bg-gray-50 px-2 py-2 bordr border-dotted">
                                    Do you really want to delete your account? Your account with be deactivated for 14 days, it will be re-activated upon logging in. After this period your account will be permanently deleted.
                                </p>
                            </div>

                            <div class="w-full border-t mt-4 py-3 px-5 flex items-center justify-end space-x-3">
                                <button @click="closeModal" class="px-4 py-2 border-2 border-black">
                                    Delete
                                </button>
                                <div class="w-24" @click="gladYouStayed">
                                    <CorePrimaryButton text="Cancel"/>
                                </div>
                            </div>
                        </DialogPanel>
                    </TransitionChild>
                </div>
            </div>
        </Dialog>
    </TransitionRoot>
</template>

<script setup lang="ts">
import {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
} from '@headlessui/vue'

const isOpen = ref<boolean>(false);
const { $toast } = useNuxtApp();

const closeModal = (): void => {
    isOpen.value = false
}
const  openModal = (): void => {
    isOpen.value = true
}

const gladYouStayed = (): void => {
    $toast.success("Pheww, We are glad you stayed!")
}
</script>