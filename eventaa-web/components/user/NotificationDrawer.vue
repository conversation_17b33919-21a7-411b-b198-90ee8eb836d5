<template>
    <button @click="open = true" class="relative text-gray-500 dark:text-gray-300 hover:text-gray-600 dark:hover:text-gray-200 focus:outline-none">
        <Icon icon="carbon:notification-filled" class="w-6 h-6 " />
        <div
            class="absolute -top-1 -right-1 w-4 h-4 bg-sky-500 rounded-full flex items-center justify-center text-white text-xs">
            {{ totalUnread }}
        </div>
    </button>
    <TransitionRoot as="template" :show="open">
        <Dialog class="relative z-10" @close="open = false">
            <TransitionChild as="template" enter="ease-in-out duration-500" enter-from="opacity-0"
                enter-to="opacity-100" leave="ease-in-out duration-500" leave-from="opacity-100" leave-to="opacity-0">
                <div class="fixed inset-0 bg-red-500 bg-opacity-25 transition-opacity" />
            </TransitionChild>

            <div class="fixed inset-0 overflow-hidden">
                <div class="absolute inset-0 overflow-hidden">
                    <div class="pointer-events-none fixed inset-y-0 right-0 flex max-w-full pl-10">
                        <TransitionChild as="template"
                            enter="transform transition ease-in-out duration-500 sm:duration-700"
                            enter-from="translate-x-full" enter-to="translate-x-0"
                            leave="transform transition ease-in-out duration-500 sm:duration-700"
                            leave-from="translate-x-0" leave-to="translate-x-full">
                            <DialogPanel class="pointer-events-auto relative w-screen max-w-md">
                                <TransitionChild as="template" enter="ease-in-out duration-500" enter-from="opacity-0"
                                    enter-to="opacity-100" leave="ease-in-out duration-500" leave-from="opacity-100"
                                    leave-to="opacity-0">
                                    <div class="absolute left-0 top-0 -ml-8 flex pt-4 sm:-ml-10">
                                        <button type="button"
                                            class="relative bg-red-600 text-white focus:ring-none focus:outline-none px-2 py-2"
                                            @click="open = false">
                                            <span class="absolute -inset-2.5" />
                                            <span class="sr-only">Close panel</span>
                                            <XMarkIcon class="h-6 w-6" aria-hidden="true" />
                                        </button>
                                    </div>
                                </TransitionChild>
                                <div class="flex h-full flex-col overflow-y-scroll bg-white shadow-xl">
                                    <div>
                                        <DialogTitle
                                            class="text-xl border-b px-4 py-3 font-semibold leading-6 text-gray-900">
                                            Notifications
                                        </DialogTitle>
                                    </div>
                                    <div class="flex items-end justify-end py-3 px-2">
                                        <button
                                            class="flex items-center bg-red-600 text-white transition duration-150 rounded-full px-1 py-1"
                                            @click="markAllAsRead">
                                            <div class="mr-1 bg-red-700 flex p-1 items-center rounded-full">
                                                <Icon icon="emojione-monotone:heavy-check-mark" class="w-4 h-4" />
                                            </div>
                                            <p class="text-base pr-1">Mark all as read</p>
                                        </button>
                                    </div>
                                    <div class="relative mt-0 border-t">
                                        <div v-if="notifications.length > 0 && !loading">
                                            <CoreNotificationItem @mark-as-read="fetchNotifications"
                                                v-for="notification in notifications" :key="notification.id"
                                                :notification="notification" />
                                        </div>
                                        <div v-if="notifications.length == 0 && !loading"
                                            class="w-full flex flex-col items-center space-y-2 justify-center mx-auto py-20">
                                            <img src="/assets/images/later.png" class="h-72 object-cover w-auto"
                                                alt="come-back-later-illustration-eventahub-malawi" />
                                            <p class="text-base text-gray-500">No notifications, come back later</p>
                                            <button @click="fetchNotifications(ENDPOINTS.NOTIFICATIONS.GET)"
                                                class="flex items-center font-medium text-sky-500">
                                                Refresh
                                                <Icon icon="solar:refresh-bold-duotone" class="w-5 h-5 ml-2" />
                                            </button>
                                        </div>
                                    </div>
                                    <div>
                                        <div v-if="loading"  class="animate-pulse space-y-3 px-4">
                                            <div v-for="n in 10" :key="n" class="flex items-start space-x-3">
                                                <div class="h-10 w-10 bg-gray-200"></div>
                                                <div class="flex-1 space-y-2">
                                                    <div class="h-10 bg-gray-200 w-3/4"></div>
                                                    <div class="h-7 bg-gray-200 w-1/2"></div>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                    <div v-if="pagination.total > pagination.perPage" class="px-4 py-2 border-t">
                                        <nav class="flex justify-between">
                                            <button :disabled="!pagination.prevPageUrl"
                                                @click="fetchPage(pagination.prevPageUrl)"
                                                class="text-sky-500 disabled:text-gray-400">Previous</button>
                                            <button :disabled="!pagination.nextPageUrl"
                                                @click="fetchPage(pagination.nextPageUrl)"
                                                class="text-sky-500 disabled:text-gray-400">Next</button>
                                        </nav>
                                    </div>
                                </div>
                            </DialogPanel>
                        </TransitionChild>
                    </div>
                </div>
            </div>
        </Dialog>
    </TransitionRoot>
</template>

<script setup lang="ts">
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue'
import { XMarkIcon } from '@heroicons/vue/24/outline'
import type { NotificationsResponse, Notification, GenericResponse } from '@/types/api';
import { useAuthStore } from '@/store/auth';

const open = ref<boolean>(false);
const loading = ref<boolean>(false);
const authStore = useAuthStore();
const notifications = ref<Notification[]>([]);
const pagination = ref({
    total: 0,
    perPage: 10,
    prevPageUrl: "",
    nextPageUrl: "",
});
const totalUnread = ref<number>(0);
const httpClient = useHttpClient();
const { $toast, $echo }: any = useNuxtApp();

const fetchNotifications = async (url: string = ENDPOINTS.NOTIFICATIONS.GET) => {
    try {
        loading.value = true;
        const response = await httpClient.get<NotificationsResponse>(url);
        notifications.value = response.data;
        pagination.value = {
            total: response.total,
            perPage: response.per_page,
            prevPageUrl: response.prev_page_url,
            nextPageUrl: response.next_page_url,
        };
        totalUnread.value = response.data.filter(n => !n.read_at).length;
        loading.value = false;
    } catch (error: any) {
        loading.value = false;
        $toast.error(error.message);
    } finally {
        loading.value = false;
    }
};

const fetchPage = (url: string) => {
    if (url) fetchNotifications(url);
};

const markAllAsRead = async () => {
    try {
        loading.value = true;
        const response = await httpClient.get<GenericResponse>(ENDPOINTS.NOTIFICATIONS.READ_ALL);
        if (response) {
            $toast.success(response.message);
            fetchNotifications();
        }
    } catch (error: any) {
        $toast.error(error.message);
    } finally {
        loading.value = false;
    }
};

onMounted(fetchNotifications);

const subscribeToNotifications = () => {
    $echo.private(`notifications.${authStore.user?.id}`)
        .listen('notification.created', (event: { id: number; type: string; data: any; read_at: string | null; created_at: string; updated_at: string; }) => {
            console.log("what")
            notifications.value.push(event);
        });
};

onMounted(() => {
    subscribeToNotifications();
});

</script>
