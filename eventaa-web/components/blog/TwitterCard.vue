<script setup lang="ts">
import { computed } from 'vue'

interface Tweet {
  content: string
  tweet_created_at: string
  hashtag: string
}

const props = defineProps<{
  tweet: Tweet
}>()

const formattedContent = computed(() => {
  return props.tweet.content
    .replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank" class="text-blue-500 hover:underline">$1</a>')
    .replace(/#(\w+)/g, '<a href="https://twitter.com/hashtag/$1" target="_blank" class="text-blue-500 hover:underline">#$1</a>')
    .replace(/@(\w+)/g, '<a href="https://twitter.com/$1" target="_blank" class="text-blue-500 hover:underline">@$1</a>')
})
</script>

<template>
  <div class="bg-white border border-gray-200 rounded p-4 hover:bg-gray-50 transition-colors">
    <div class="flex items-start gap-3">
      <Icon icon="logos:twitter" class="w-5 h-5 text-blue-400 mt-1 flex-shrink-0" />
      <div class="flex-1 min-w-0">
        <div class="text-gray-900 break-words" v-html="formattedContent"></div>
        <div class="flex items-center gap-2 mt-2 text-sm text-gray-500">
          <span>{{ new Date(tweet.tweet_created_at).toLocaleDateString() }}</span>
          <span>•</span>
          <span class="text-blue-500">{{ '#' + tweet.hashtag }}</span>
        </div>
      </div>
    </div>
  </div>
</template>