<template>
    <div v-if="profile" key="profile-loaded">
        <div class="w-full px-4 py-2 border-b flex justify-end">
            <div class="w-28 flex items-center justify-end">
                <CorePrimaryButton @click="isEditing ? updateProfile() : toggleEdit()"
                    :start-icon="isEditing ? 'solar:list-check-bold' : 'line-md:edit'"
                    :text="isEditing ? 'Save' : 'Update'" />
            </div>
        </div>

        <div v-if="profile">
            <div class="w-full border-b border-dotted px-4 py-2 bg-gray-50">
                <h3 class="text-lg font-semibold">Name</h3>
                <div v-if="!isEditing">
                    <p class="text-base text-gray-600">{{ profile.name }}</p>
                </div>
                <div v-else>
                    <FormKit type="text" prefixIcon="user" v-model="form.name" />
                </div>
            </div>

            <div class="w-full border-b border-dotted px-4 py-2 bg-white">
                <h3 class="text-lg font-semibold">Tell us about yourself</h3>
                <div v-if="!isEditing">
                    <p v-html="profile.profile?.about"
                        class="text-base text-gray-600 px-2 py-2 border-2 border-dotted mt-2">
                    </p>
                </div>
                <div v-else>
                    <FormKit type="textarea" v-model="form.about" />
                </div>
            </div>

            <div class="w-full border-b border-dotted px-4 py-2 bg-gray-50">
                <h3 class="text-lg font-semibold">Find me on</h3>
                <div v-if="!isEditing">
                    <p class="text-base text-gray-600">{{ form.socialMedia.twitter }}</p>
                </div>
                <div class="flex flex-col items-center space-y-2.5" v-else>
                    <FormKit type="text" v-model="form.socialMedia.twitter" prefixIcon="twitter" />
                    <FormKit type="text" v-model="form.socialMedia.facebook" prefixIcon="facebook" />
                    <FormKit type="text" v-model="form.socialMedia.instagram" prefixIcon="instagram" />
                </div>
            </div>


            <div class="w-full border-b border-dotted px-4 py-2 bg-gray-50">
                <h3 class="text-lg font-semibold">Phone number</h3>
                <div v-if="!isEditing">
                    <p class="text-base text-gray-600">{{ form.phoneNumber }}</p>
                </div>
                <div v-else>
                    <FormKit type="tel" prefixIcon="phone" v-model="form.phoneNumber" />
                </div>
            </div>

            <div class="w-full border-b border-dotted px-4 py-2 bg-white">
                <h3 class="text-lg font-semibold mb-2">Interests</h3>
                <div v-if="!isEditing">
                    <div class="flex flex-wrap gap-2">
                        <button type="button"
                            class="flex items-center bg-gray-100 font-light transition duration-150 rounded-full px-2.5 py-1.5 mb-2"
                            v-for="category in form.interests" :key="category.name">
                            <img :src="`${runtimeConfig.public.baseUrl}storage/categories/${category.icon}`"
                                class="w-5 h-5 sm:w-6 sm:h-6 mr-1.5 sm:mr-2" />
                            {{ category.name }}
                        </button>
                    </div>
                </div>
                <div class="mb-3" v-else>
                    <MultiSelect mode="tags" v-model="selectedInterest"
                        :options="$categories.map((category: Category) => category.name)" label="Select interests"
                        :classes="{ container: 'border bg-white relative' }" />
                </div>
            </div>
        </div>
    </div>
    
    <div v-else key="profile-loading" class="animate-pulse">
        <div class="w-full px-4 py-2 border-b flex justify-end">
            <div class="w-28 h-10 bg-gray-200"></div>
        </div>

        <div>
            <div v-for="n in 5" :key="n" class="w-full border-b border-dotted px-4 py-4 bg-gray-50">
                <div class="h-6 bg-gray-200 w-1/3 mb-2"></div>
                <div class="h-8 bg-gray-300 w-full"></div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import type { PropType } from 'vue';
import type { User } from '@/types/user';
import type { GenericResponse } from '@/types/api';
import type { Category } from '@/types';

const props = defineProps({
    profile: {
        type: Object as PropType<User | null | undefined>,
        default: null
    }
});

const runtimeConfig = useRuntimeConfig();
const { $toast, $categories }: any = useNuxtApp();
const httpClient = useHttpClient();
const loading = ref<boolean>(false);
const isEditing = ref<boolean>(false);

const form = computed(() => ({
    name: props.profile?.name || "",
    about: props.profile?.profile?.about || "",
    socialMedia: {
        facebook: props.profile?.facebook_url || "",
        twitter: props.profile?.twitter_url || "",
        instagram: props.profile?.profile?.instagram || "",
    },
    phoneNumber: props.profile?.phone || "",
    interests: props.profile?.interests || [] as Category[]
}));

const selectedInterest = ref<string[]>([]);

const toggleEdit = (): void => {
    isEditing.value = !isEditing.value;
};

const updateProfile = async (): Promise<void> => {
    if (!props.profile) return;
    try {
        loading.value = true;

        // Map selected interests (names) to category ids
        const getInterestIds: number[] = [];
        $categories.forEach((category: Category) => {
            if (selectedInterest.value.includes(category.name)) {
                getInterestIds.push(category.id);
            }
        });

        const formData = new FormData();
        formData.append('name', form.value.name);
        formData.append('about', form.value.about);
        formData.append('email', props.profile.email);
        formData.append('facebook_url', form.value.socialMedia.facebook);
        formData.append('twitter_url', form.value.socialMedia.twitter);
        formData.append('instagram', form.value.socialMedia.instagram);
        formData.append('phone', form.value.phoneNumber);
        formData.append('interests', JSON.stringify(getInterestIds));

        const response = await httpClient.post<GenericResponse>(ENDPOINTS.PROFILE.UPDATE, formData);
        if (response) {
            $toast.success(response.message);
            toggleEdit();
        }
    } catch (error: any) {
        if (error.errors) {
            const errors = error.errors;
            Object.keys(errors).forEach((key) => {
                if (Array.isArray(errors[key])) {
                    errors[key].forEach((message: string) => {
                        $toast.error(message);
                    });
                } else if (typeof errors[key] === 'string') {
                    $toast.error(errors[key]);
                }
            });
        } else {
            $toast.error(error.message || 'An error occurred');
        }
    } finally {
        loading.value = false;
    }
}

onMounted(() => {
    if (props.profile) {
        selectedInterest.value = props.profile.interests.map((interest: Category) => interest.name);
    }
});
</script>
