<template>
    <div>
        <div>
            <div class="bg-gray-50 border-t border-b border-dotted text-lg font-semibold px-4 py-2">
                Privacy
            </div>
            <p class="px-4 py-2 text-base text-gray-500">Your account will be deactivated for 14 days, after these days
                it'll be automatically removed from EventaHub Malawi</p>
        </div>
        <div>
            <div class="bg-gray-50 border-t border-b border-dotted text-lg font-semibold px-4 py-2">
                Notifications
            </div>
            <div>
                <div class="w-full flex items-center justify-between px-4">
                    <div>
                        <p class="text-base text-gray-500">Allow event Notifications, promotions and others to be sent
                            directly to your mail inbox</p>
                    </div>
                    <div>
                        <Switch v-model="enabled" :class="enabled ? 'bg-sky-500' : 'bg-gray-300'"
                            class="relative inline-flex h-6 w-10 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75">
                            <span class="sr-only">Use setting</span>
                            <span aria-hidden="true" :class="enabled ? 'translate-x-4' : 'translate-x-0'"
                                class="pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-lg ring-0 transition duration-200 ease-in-out" />
                        </Switch>
                    </div>
                </div>
            </div>
        </div>
        <div>
            <div class="bg-gray-50 border-t border-b border-dotted text-lg font-semibold px-4 py-2">
                Change Password
            </div>
            <div class="px-4 py-2 flex flex-col space-y-2">
                <FormKit prefixIcon="password" type="password" label="New password" placeholder="Enter a new password"
                    v-model="passwordValues.newPassword" />
                <FormKit prefixIcon="password" type="password" label="Confirm new password" placeholder="Re-enter a new password"
                    v-model="passwordValues.confirmNewPassword" />
                <CorePrimaryButton text="Submit" />
            </div>
        </div>
        <div>
            <div class="bg-gray-50 border-t border-b border-dotted text-lg font-semibold px-4 py-2">
                Delete Account
            </div>
            <p class="px-4 py-2 text-base text-gray-500">Your account will be deactivated for 14 days, after these days
                it'll be automatically removed from EventaHub Malawi</p>
            <UserDeactivateModal/>
        </div>
    </div>
</template>

<script setup lang="ts">
import { Switch } from '@headlessui/vue'

const enabled = ref<boolean>(false);
const passwordValues = ref<Record<string, string>>({
    newPassword: "",
    confirmNewPassword: ""
})
</script>

<style lang="sass"></style>