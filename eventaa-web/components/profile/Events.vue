<template>
    <div>
        <div class="w-full flex">
            <div class="w-full flex flex-col">
                <EventsBookmark v-for="i in props.events" :key="i.id" :event="i" />
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import type { PropType } from 'vue';
import type { EventItem } from '~/types';

const props = defineProps({
    events: {
        type: Array as PropType<EventItem[]>,
        required: true
    }
})
const activeMiniTab = ref<string>('Liked')
const miniTabs =  ref([
    { name: 'Liked', icon: 'fluent:heart-28-filled' },
    { name: 'Upcoming', icon: 'material-symbols-light:upcoming-sharp' },
    { name: 'Attended', icon: 'fluent-mdl2:event-accepted' },
]);
const setActiveMini = (tab: string) => {
    activeMiniTab.value = tab;
}
const eventsArray: any = [
  {
    id: "2",
    title: "Tech Conference 2024",
    imageUrl: "/assets/images/tech-conf.jpeg",
    startDate: "2024-09-15T10:00:00",
    endDate: "2024-09-17T18:00:00",
    status: "In Progress"
  },
  {
    id: "3",
    title: "Annual Charity Gala",
    imageUrl: "/assets/images/charity-gala.png",
    startDate: "2024-12-05T19:00:00",
    endDate: "2024-12-05T23:00:00",
    status: "Pending"
  },
  {
    id: "4",
    title: "Summer Music Festival",
    imageUrl: "/assets/images/music-fest.jpg",
    startDate: "2024-07-01T12:00:00",
    endDate: "2024-07-03T23:00:00",
    status: "Cancelled"
  }
];
</script>

<style lang="scss"></style>