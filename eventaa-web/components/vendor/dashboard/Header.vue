<template>
  <header class="dashboard-bg-header dashboard-border border-b">
    <div class="flex items-center justify-between px-4 py-3">
      <div class="flex items-center lg:hidden">
        <button @click="toggleSidebar"
          class="p-1 dashboard-text-secondary hover:dashboard-text-primary focus:outline-none">
          <Icon icon="heroicons:bars-3" class="w-6 h-6" />
        </button>
      </div>

      <div class="flex-1 flex items-center lg:justify-between">
        <div class="flex-1 flex">
          <div class="w-full max-w-lg lg:max-w-xs">
            <label for="search" class="sr-only">Search</label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Icon icon="heroicons:magnifying-glass" class="h-5 w-5 dashboard-text-light" />
              </div>
              <input id="search"
                class="block w-full pl-10 pr-3 py-2 dashboard-border dashboard-input dashboard-focus-primary"
                placeholder="Search" type="search" />
            </div>
          </div>
        </div>

        <div class="hidden lg:flex lg:items-center lg:ml-6">
          <div class="flex items-center space-x-4">
            <button @click="toggleDarkMode"
              class="p-1 dashboard-text-secondary hover:dashboard-text-primary focus:outline-none dashboard-transition">
              <span class="sr-only">Toggle dark mode</span>
              <Icon v-if="isDarkMode" icon="line-md:sunny-filled-loop" class="h-6 w-6 text-amber-500" />
              <Icon v-else icon="line-md:moon-filled" class="h-6 w-6" />
            </button>


            <div class="relative">
              <Popover>
                <PopoverButton
                  class="p-1 dashboard-text-secondary hover:dashboard-text-primary focus:outline-none dashboard-transition">
                  <span class="sr-only">View notifications</span>
                  <Icon icon="heroicons:bell" class="h-6 w-6" />
                  <span v-if="unreadNotifications > 0"
                    class="absolute top-0 right-0 block h-2 w-2 dashboard-bg-primary rounded-full"></span>
                </PopoverButton>

                <transition enter-active-class="transition duration-200 ease-out"
                  enter-from-class="translate-y-1 opacity-0" enter-to-class="translate-y-0 opacity-100"
                  leave-active-class="transition duration-150 ease-in" leave-from-class="translate-y-0 opacity-100"
                  leave-to-class="translate-y-1 opacity-0">
                  <PopoverPanel class="absolute right-0 z-10 mt-2 w-80 origin-top-right">
                    <div class="dashboard-bg-card dashboard-shadow-lg dashboard-border">
                      <div class="p-3 dashboard-border border-b flex justify-between items-center">
                        <h3 class="text-sm font-medium dashboard-text-primary">Notifications</h3>
                        <button @click="markAllNotificationsAsRead"
                          class="text-xs dashboard-text-brand hover:text-red-800">
                          Mark all as read
                        </button>
                      </div>

                      <div v-if="loadingNotifications" class="max-h-96 overflow-y-auto">
                        <div v-for="i in 3" :key="i" class="p-4 dashboard-border-light border-b animate-pulse">
                          <div class="flex">
                            <div class="flex-shrink-0 mr-3">
                              <div class="h-8 w-8 dashboard-bg-primary-light rounded-full"></div>
                            </div>
                            <div class="flex-1 min-w-0">
                              <div class="h-4 dashboard-bg-primary-light w-3/4 mb-2"></div>
                              <div class="h-3 dashboard-bg-primary-light w-full mb-2"></div>
                              <div class="h-3 dashboard-bg-primary-light w-1/4"></div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div v-else class="max-h-96 overflow-y-auto">
                        <div v-if="notifications.length === 0" class="p-4 text-center dashboard-text-muted text-sm">
                          No notifications
                        </div>
                        <div v-for="notification in notifications" :key="notification.id"
                          class="p-4 dashboard-border-light border-b hover:dashboard-bg-hover dashboard-transition"
                          :class="{ 'dashboard-bg-primary-light': !notification.read_at }"
                          @click="markNotificationAsRead(notification.id)">
                          <div class="flex">
                            <div class="flex-shrink-0 mr-3">
                              <div
                                class="h-8 w-8 flex items-center justify-center dashboard-bg-primary-light dashboard-text-brand">
                                <Icon
                                  :icon="notificationTypeIcons[notification.data?.type || 'system'] || 'heroicons:bell'"
                                  class="h-5 w-5"
                                />
                              </div>
                            </div>
                            <div class="flex-1 min-w-0">
                              <p class="text-sm font-medium dashboard-text-primary">{{ notification.data?.title || 'Notification' }}</p>
                              <p class="text-sm dashboard-text-secondary truncate">{{ notification.data?.message || notification.data?.line || 'No message' }}</p>
                              <p class="text-xs dashboard-text-light mt-1">{{ formatNotificationTime(notification.created_at) }}</p>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div class="p-2 dashboard-border border-t text-center">
                        <NuxtLink to="/vendor/dashboard/notifications" class="text-sm dashboard-text-brand hover:text-red-800">
                          View all notifications
                        </NuxtLink>
                      </div>
                    </div>
                  </PopoverPanel>
                </transition>
              </Popover>
            </div>

            <div class="relative">
              <Popover>
                <PopoverButton
                  class="p-1 dashboard-text-secondary hover:dashboard-text-primary focus:outline-none dashboard-transition">
                  <span class="sr-only">View messages</span>
                  <Icon icon="heroicons:envelope" class="h-6 w-6" />
                  <span v-if="unreadMessages > 0"
                    class="absolute top-0 right-0 block h-2 w-2 dashboard-bg-primary rounded-full"></span>
                </PopoverButton>

                <transition enter-active-class="transition duration-200 ease-out"
                  enter-from-class="translate-y-1 opacity-0" enter-to-class="translate-y-0 opacity-100"
                  leave-active-class="transition duration-150 ease-in" leave-from-class="translate-y-0 opacity-100"
                  leave-to-class="translate-y-1 opacity-0">
                  <PopoverPanel class="absolute right-0 z-10 mt-2 w-80 origin-top-right">
                    <div class="dashboard-bg-card dashboard-shadow-lg dashboard-border">
                      <div class="p-3 dashboard-border border-b">
                        <h3 class="text-sm font-medium dashboard-text-primary">Recent Messages</h3>
                      </div>
                      <div class="max-h-96 overflow-y-auto">
                        <!-- Loading skeleton -->
                        <div v-if="loadingMessages" class="animate-pulse">
                          <div v-for="i in 3" :key="i" class="p-4 dashboard-border-light border-b">
                            <div class="flex">
                              <div class="flex-shrink-0 mr-3">
                                <div class="h-10 w-10 dashboard-bg-primary-light rounded-full"></div>
                              </div>
                              <div class="flex-1 min-w-0">
                                <div class="flex justify-between mb-2">
                                  <div class="h-4 dashboard-bg-primary-light w-1/3"></div>
                                  <div class="h-3 dashboard-bg-primary-light w-1/6"></div>
                                </div>
                                <div class="h-3 dashboard-bg-primary-light w-2/3"></div>
                              </div>
                            </div>
                          </div>
                        </div>

                        <!-- No messages -->
                        <div v-else-if="recentMessages.length === 0" class="p-4 text-center dashboard-text-muted text-sm">
                          No messages
                        </div>

                        <!-- Messages list -->
                        <NuxtLink v-else v-for="message in recentMessages" :key="message.id"
                          :to="`/vendor/dashboard/messages?conversation=${message.conversationId}`"
                          class="block p-4 dashboard-border-light border-b hover:dashboard-bg-hover dashboard-transition"
                          :class="{ 'dashboard-bg-primary-light': !message.read }">
                          <div class="flex">
                            <div class="flex-shrink-0 mr-3 relative">
                              <img class="h-10 w-10 rounded-full object-cover" :src="message.avatar" alt="" />
                              <div v-if="!message.read" class="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></div>
                            </div>
                            <div class="flex-1 min-w-0">
                              <div class="flex justify-between">
                                <p class="text-sm font-medium dashboard-text-primary">{{ message.sender }}</p>
                                <p class="text-xs dashboard-text-light">{{ message.time }}</p>
                              </div>
                              <p class="text-sm dashboard-text-secondary truncate">{{ message.text }}</p>
                            </div>
                          </div>
                        </NuxtLink>
                      </div>
                      <div class="p-2 dashboard-border border-t text-center">
                        <NuxtLink to="/vendor/dashboard/messages"
                          class="text-sm dashboard-text-brand hover:text-red-800">
                          View all messages
                        </NuxtLink>
                      </div>
                    </div>
                  </PopoverPanel>
                </transition>
              </Popover>
            </div>

            <div class="relative">
              <Menu as="div" class="relative inline-block text-left">
                <div>
                  <MenuButton class="flex items-center space-x-2 focus:outline-none">
                    <img class="h-8 w-8 rounded-full"
                      :src="authStore.user?.avatar ? `${runtimeConfig.public.baseUrl}storage/avatars/${authStore.user.avatar}` : `https://ui-avatars.com/api/?name=${encodeURIComponent(authStore.user?.name || 'Vendor User')}&background=random`"
                      :alt="authStore.user?.name || 'User avatar'" />
                    <span class="font-medium dashboard-text-primary">{{ authStore.user?.name || 'Vendor User'
                      }}</span>
                    <Icon icon="heroicons:chevron-down" class="h-5 w-5 dashboard-text-light" />
                  </MenuButton>
                </div>

                <transition enter-active-class="transition ease-out duration-100"
                  enter-from-class="transform opacity-0 scale-95" enter-to-class="transform opacity-100 scale-100"
                  leave-active-class="transition ease-in duration-75" leave-from-class="transform opacity-100 scale-100"
                  leave-to-class="transform opacity-0 scale-95">
                  <MenuItems
                    class="absolute right-0 z-10 mt-2 w-56 origin-top-right dashboard-bg-card dashboard-shadow-lg dashboard-border">
                    <div class="py-1">
                      <MenuItem v-slot="{ active }">
                      <NuxtLink to="/vendor/dashboard/profile" :class="[
                        active ? 'dashboard-bg-hover dashboard-text-primary' : 'dashboard-text-secondary',
                        'flex items-center px-4 py-2 text-sm dashboard-transition'
                      ]">
                        <Icon icon="heroicons:user-circle" class="mr-3 h-5 w-5 dashboard-text-light" />
                        <span>Your Profile</span>
                      </NuxtLink>
                      </MenuItem>
                      <MenuItem v-slot="{ active }">
                      <NuxtLink to="/vendor/dashboard/settings/account" :class="[
                        active ? 'dashboard-bg-hover dashboard-text-primary' : 'dashboard-text-secondary',
                        'flex items-center px-4 py-2 text-sm dashboard-transition'
                      ]">
                        <Icon icon="heroicons:cog-6-tooth" class="mr-3 h-5 w-5 dashboard-text-light" />
                        <span>Settings</span>
                      </NuxtLink>
                      </MenuItem>
                      <div class="dashboard-border-light border-t"></div>
                      <MenuItem v-slot="{ active }">
                      <button @click="logout" :class="[
                        active ? 'dashboard-bg-hover dashboard-text-primary' : 'dashboard-text-secondary',
                        'flex w-full items-center px-4 py-2 text-sm dashboard-transition'
                      ]">
                        <Icon icon="heroicons:arrow-right-on-rectangle" class="mr-3 h-5 w-5 dashboard-text-light" />
                        <span>Sign out</span>
                      </button>
                      </MenuItem>
                    </div>
                  </MenuItems>
                </transition>
              </Menu>
            </div>
          </div>
        </div>
      </div>

      <div class="flex items-center space-x-3 lg:hidden">
        <button @click="toggleDarkMode"
          class="p-1 dashboard-text-secondary hover:dashboard-text-primary focus:outline-none dashboard-transition">
          <span class="sr-only">Toggle dark mode</span>
          <Icon v-if="isDarkMode" icon="heroicons:sun" class="h-6 w-6" />
          <Icon v-else icon="heroicons:moon" class="h-6 w-6" />
        </button>

        <Popover class="relative">
          <PopoverButton
            class="p-1 dashboard-text-secondary hover:dashboard-text-primary focus:outline-none dashboard-transition">
            <Icon icon="heroicons:bell" class="h-6 w-6" />
            <span v-if="unreadNotifications > 0"
              class="absolute top-0 right-0 block h-2 w-2 dashboard-bg-primary rounded-full"></span>
          </PopoverButton>

          <transition enter-active-class="transition duration-200 ease-out" enter-from-class="translate-y-1 opacity-0"
            enter-to-class="translate-y-0 opacity-100" leave-active-class="transition duration-150 ease-in"
            leave-from-class="translate-y-0 opacity-100" leave-to-class="translate-y-1 opacity-0">
            <PopoverPanel class="absolute right-0 z-10 mt-2 w-80 origin-top-right">
              <div class="bg-white dark:bg-zinc-800 shadow-lg border border-gray-200 dark:border-zinc-700">
                <div class="p-3 border-b border-gray-200 dark:border-zinc-700 flex justify-between items-center">
                  <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100">Notifications</h3>
                  <button @click="markAllNotificationsAsRead" class="text-xs text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300">
                    Mark all as read
                  </button>
                </div>

                <div v-if="loadingNotifications" class="max-h-96 overflow-y-auto">
                  <div v-for="i in 3" :key="i" class="p-4 border-b border-gray-100 dark:border-zinc-700 animate-pulse">
                    <div class="flex">
                      <div class="flex-shrink-0 mr-3">
                        <div class="h-8 w-8 bg-gray-200 dark:bg-zinc-700"></div>
                      </div>
                      <div class="flex-1 min-w-0">
                        <div class="h-4 bg-gray-200 dark:bg-zinc-700 w-3/4 mb-2"></div>
                        <div class="h-3 bg-gray-200 dark:bg-zinc-700 w-full mb-2"></div>
                        <div class="h-3 bg-gray-200 dark:bg-zinc-700 w-1/4"></div>
                      </div>
                    </div>
                  </div>
                </div>
                <div v-else class="max-h-96 overflow-y-auto">
                  <div v-if="notifications.length === 0" class="p-4 text-center text-gray-500 dark:text-gray-400 text-sm">
                    No notifications
                  </div>
                  <div v-for="notification in notifications" :key="notification.id"
                    class="p-4 border-b border-gray-100 dark:border-zinc-700 hover:bg-gray-50 dark:hover:bg-zinc-700 transition-colors duration-150"
                    :class="{ 'bg-red-50 dark:bg-red-900/20': !notification.read_at }"
                    @click="markNotificationAsRead(notification.id)">
                    <div class="flex">
                      <div class="flex-shrink-0 mr-3">
                        <div class="h-8 w-8 flex items-center justify-center bg-red-100 dark:bg-red-900/30 text-red-500 dark:text-red-400">
                          <Icon :icon="notificationTypeIcons[notification.data?.type || 'system'] || 'heroicons:bell'" class="h-5 w-5" />
                        </div>
                      </div>
                      <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ notification.data?.title || 'Notification' }}</p>
                        <p class="text-sm text-gray-500 dark:text-gray-400 truncate">{{ notification.data?.message || notification.data?.line || 'No message' }}</p>
                        <p class="text-xs text-gray-400 dark:text-gray-500 mt-1">{{ formatNotificationTime(notification.created_at) }}</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="p-2 border-t border-gray-200 dark:border-zinc-700 text-center">
                  <NuxtLink to="/vendor/dashboard/notifications" class="text-sm text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300">
                    View all notifications
                  </NuxtLink>
                </div>
              </div>
            </PopoverPanel>
          </transition>
        </Popover>

        <Popover class="relative">
          <PopoverButton
            class="p-1 dashboard-text-secondary hover:dashboard-text-primary focus:outline-none dashboard-transition">
            <Icon icon="heroicons:envelope" class="h-6 w-6" />
            <span v-if="unreadMessages > 0"
              class="absolute top-0 right-0 block h-2 w-2 dashboard-bg-primary ring-2 ring-zinc-800"></span>
          </PopoverButton>

          <transition enter-active-class="transition duration-200 ease-out" enter-from-class="translate-y-1 opacity-0"
            enter-to-class="translate-y-0 opacity-100" leave-active-class="transition duration-150 ease-in"
            leave-from-class="translate-y-0 opacity-100" leave-to-class="translate-y-1 opacity-0">
            <PopoverPanel class="absolute right-0 z-10 mt-2 w-80 origin-top-right">
              <div class="bg-white shadow-lg border border-gray-200">
                <div class="p-3 border-b border-gray-200">
                  <h3 class="text-sm font-medium text-gray-900">Recent Messages</h3>
                </div>
                <div class="max-h-96 overflow-y-auto">
                  <!-- Loading skeleton -->
                  <div v-if="loadingMessages" class="animate-pulse">
                    <div v-for="i in 3" :key="i" class="p-4 border-b border-gray-100 dark:border-zinc-700">
                      <div class="flex">
                        <div class="flex-shrink-0 mr-3">
                          <div class="h-10 w-10 bg-gray-200 dark:bg-zinc-700 rounded-full"></div>
                        </div>
                        <div class="flex-1 min-w-0">
                          <div class="flex justify-between mb-2">
                            <div class="h-4 bg-gray-200 dark:bg-zinc-700 w-1/3"></div>
                            <div class="h-3 bg-gray-200 dark:bg-zinc-700 w-1/6"></div>
                          </div>
                          <div class="h-3 bg-gray-200 dark:bg-zinc-700 w-2/3"></div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- No messages -->
                  <div v-else-if="recentMessages.length === 0" class="p-4 text-center text-gray-500 dark:text-gray-400 text-sm">
                    No messages
                  </div>

                  <!-- Messages list -->
                  <NuxtLink v-else v-for="message in recentMessages" :key="message.id"
                    :to="`/vendor/dashboard/messages?conversation=${message.conversationId}`"
                    class="block p-4 border-b border-gray-100 dark:border-zinc-700 hover:bg-gray-50 dark:hover:bg-zinc-700"
                    :class="{ 'bg-red-50 dark:bg-red-900/20': !message.read }">
                    <div class="flex">
                      <div class="flex-shrink-0 mr-3 relative">
                        <img class="h-10 w-10 rounded-full object-cover" :src="message.avatar" alt="" />
                        <div v-if="!message.read" class="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></div>
                      </div>
                      <div class="flex-1 min-w-0">
                        <div class="flex justify-between">
                          <p class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ message.sender }}</p>
                          <p class="text-xs text-gray-400 dark:text-gray-500">{{ message.time }}</p>
                        </div>
                        <p class="text-sm text-gray-500 dark:text-gray-400 truncate">{{ message.text }}</p>
                      </div>
                    </div>
                  </NuxtLink>
                </div>
                <div class="p-2 border-t border-gray-200 text-center">
                  <NuxtLink to="/vendor/dashboard/messages" class="text-sm text-red-600 hover:text-red-800">
                    View all messages
                  </NuxtLink>
                </div>
              </div>
            </PopoverPanel>
          </transition>
        </Popover>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '@/store/auth';
import { useVendorStore } from '@/store/vendor';
import { useCountersStore } from '@/store/counters';
import { ENDPOINTS } from '@/utils/api';
import type { Notification, NotificationsResponse } from '@/types/api';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';

import {
  Menu,
  MenuButton,
  MenuItem,
  MenuItems,
  Popover,
  PopoverButton,
  PopoverPanel
} from '@headlessui/vue';

dayjs.extend(relativeTime);

interface Message {
  id: number;
  conversationId: number;
  sender: string;
  avatar: string;
  text: string;
  time: string;
  read: boolean;
}

const emit = defineEmits(['toggle-sidebar', 'toggle-dark-mode']);
const router = useRouter();
const authStore = useAuthStore();
const vendorStore = useVendorStore();
const countersStore = useCountersStore();
const httpClient = useHttpClient();
const runtimeConfig = useRuntimeConfig();
const { $toast }: any = useNuxtApp();

const nuxtApp = useNuxtApp();
const $echo = nuxtApp.$echo as any;

const isDarkMode = ref(false);
const notifications = ref<Notification[]>([]);
const loadingNotifications = ref<boolean>(false);
const unreadNotifications = ref<number>(0);
const recentMessages = ref<Message[]>([]);
const unreadMessages = ref<number>(0);
const loadingMessages = ref<boolean>(false);

const notificationTypeIcons: Record<string, string> = {
  booking: 'heroicons:calendar',
  message: 'heroicons:chat-bubble-left-right',
  review: 'heroicons:star',
  payment: 'heroicons:banknotes',
  vendor_request_approved: 'heroicons:check-badge',
  vendor_request: 'heroicons:building-storefront',
  vendor_request_rejected: 'heroicons:x-circle',
  system: 'heroicons:bell'
};

const toggleSidebar = () => {
  emit('toggle-sidebar');
};

const toggleDarkMode = () => {
  isDarkMode.value = !isDarkMode.value;
  emit('toggle-dark-mode');
};

const fetchNotifications = async (): Promise<void> => {
  try {
    loadingNotifications.value = true;
    const response = await httpClient.get<NotificationsResponse>(`${ENDPOINTS.NOTIFICATIONS.GET}?limit=5`);

    if (response) {
      notifications.value = response.data;
      const unreadCount = notifications.value.filter(n => !n.read_at).length;
      unreadNotifications.value = unreadCount;

      // Update the counters store
      countersStore.unreadNotifications = (response as any).total_unread || unreadCount;
    }
  } catch (error: any) {
    console.error('Error fetching notifications:', error);
  } finally {
    loadingNotifications.value = false;
  }
};

const markNotificationAsRead = async (id: number): Promise<void> => {
  try {
    const response = await httpClient.put<{ success: string }>(
      `${ENDPOINTS.NOTIFICATIONS.READ}/${id}`
    );
    if (response) {
      const notification = notifications.value.find(n => n.id === id);
      if (notification && !notification.read_at) {
        notification.read_at = new Date().toISOString();
        unreadNotifications.value = notifications.value.filter(n => !n.read_at).length;

        // Update the counters store
        countersStore.decrementUnreadNotifications();
      }
    }
  } catch (error: any) {
    console.error('Error marking notification as read:', error);
  }
};

const markAllNotificationsAsRead = async (): Promise<void> => {
  try {
    const response = await httpClient.get<{ message: string }>(ENDPOINTS.NOTIFICATIONS.READ_ALL);
    if (response) {
      const unreadCount = notifications.value.filter(n => !n.read_at).length;

      notifications.value.forEach(notification => {
        notification.read_at = new Date().toISOString();
      });
      unreadNotifications.value = 0;

      // Update the counters store
      if (unreadCount > 0) {
        countersStore.unreadNotifications = 0;
      }
    }
  } catch (error: any) {
    console.error('Error marking all notifications as read:', error);
  }
};

const formatNotificationTime = (timestamp: string): string => {
  return dayjs(timestamp).fromNow();
};

const logout = async () => {
  try {
    $toast.info('Logging out...', {
      timeout: false,
      closeOnClick: false,
    });

    // Always clear local auth state regardless of API call success
    const clearLocalState = () => {
      authStore.clearAuth();
      vendorStore.clearPermissions();

      $toast.success('Logged out successfully', {
        timeout: 2000
      });

      setTimeout(() => {
        router.push('/');
      }, 500);
    };

    try {
      // Try to call the logout API endpoint
      await httpClient.post(ENDPOINTS.AUTH.LOGOUT);
      clearLocalState();
    } catch (apiError) {
      console.warn('Logout API call failed, but proceeding with local logout:', apiError);
      // Even if the API call fails, we should still clear local state
      clearLocalState();
    }
  } catch (error) {
    console.error('Logout error:', error);
    $toast.error('Failed to logout. Please try again.', {
      position: 'top-center'
    });
  }
};

const fetchRecentMessages = async (): Promise<void> => {
  try {
    loadingMessages.value = true;
    const response: any = await httpClient.get(`${ENDPOINTS.MESSAGES.GET_CONVERSATIONS}?limit=5`);

    if (response && response.data) {
      // Debug logging
      console.log('Header - Current user ID:', authStore.user?.id);
      console.log('Header - Fetched conversations:', response.data);

      // Transform API conversations to our UI format
      recentMessages.value = response.data.map((conv: any) => {
        // Determine if the current user is acting as a vendor in this conversation
        const isVendor = conv.vendor?.user_id === authStore.user?.id;

        // If user is the vendor, show the user's info, otherwise show the vendor's info
        let otherUser: {
          id?: number;
          name?: string;
          avatar?: string | null;
          logo?: string | null;
        };

        if (isVendor) {
          // Current user is the vendor, so the other party is the regular user
          otherUser = conv.user;
        } else if (conv.user.id === authStore.user?.id) {
          // Current user is the regular user, so the other party is the vendor
          otherUser = {
            id: conv.vendor?.id,
            name: conv.vendor?.name || 'Unknown Vendor',
            logo: conv.vendor?.logo
          };
        } else {
          // Fallback (shouldn't happen in normal cases)
          otherUser = conv.user.id === authStore.user?.id ?
            { id: conv.vendor?.id, name: conv.vendor?.name, logo: conv.vendor?.logo } :
            conv.user;
        }

        return {
          id: conv.id,
          conversationId: conv.id,
          sender: otherUser?.name || 'Unknown User',
          avatar: otherUser?.avatar
            ? `${runtimeConfig.public.baseUrl}storage/avatars/${otherUser.avatar}`
            : (otherUser?.logo
                ? `${runtimeConfig.public.baseUrl}storage/${otherUser.logo}`
                : `https://ui-avatars.com/api/?name=${encodeURIComponent(otherUser?.name || 'User')}&background=random`),
          text: conv.latest_message?.content || 'No messages yet',
          time: conv.latest_message
            ? formatNotificationTime(conv.latest_message.created_at)
            : formatNotificationTime(conv.created_at),
          read: !(conv.unread_count && conv.unread_count > 0)
        };
      });

      // Update unread message count
      unreadMessages.value = response.data.reduce((sum: number, conv: any) => sum + (conv.unread_count || 0), 0);

      // Update the counters store
      countersStore.unreadMessages = unreadMessages.value;
    }
  } catch (error) {
    console.error('Error fetching recent messages:', error);
  } finally {
    loadingMessages.value = false;
  }
};

onMounted(() => {
  const savedDarkMode = localStorage.getItem('darkMode');
  if (savedDarkMode === 'true') {
    isDarkMode.value = true;
  }

  fetchNotifications();
  fetchRecentMessages();

  // Subscribe to real-time notifications if Echo is available
  if ($echo && authStore.user?.id && typeof $echo.private === 'function') {
    try {
      console.log('Subscribing to notifications channel:', `notifications.${authStore.user.id}`);

      $echo.private(`notifications.${authStore.user.id}`)
        .listen('App\\Events\\NotificationCreated', (event: Notification) => {
          console.log('Received real-time notification:', event);
          notifications.value.unshift(event);
          if (!event.read_at) {
            unreadNotifications.value++;
          }

          // Show toast notification
          $toast.info(`New notification: ${event.data?.title || 'Notification received'}`);
        });

      // Also listen for the generic event name format
      $echo.private(`notifications.${authStore.user.id}`)
        .listen('.NotificationCreated', (event: Notification) => {
          console.log('Received real-time notification (generic format):', event);
          notifications.value.unshift(event);
          if (!event.read_at) {
            unreadNotifications.value++;
          }
        });

      // Subscribe to message events for all conversations
      $echo.private(`user.${authStore.user.id}.messages`)
        .listen('.message.sent', (event: any) => {
          console.log('Received message event:', event);

          // Check if this is a new conversation or an existing one
          const existingConversation = recentMessages.value.find(m => m.conversationId === event.message.conversation_id);

          if (existingConversation) {
            // Update existing conversation
            existingConversation.text = event.message.content;
            existingConversation.time = 'Just now';
            existingConversation.read = false;

            // Move to the top of the list
            recentMessages.value = [
              existingConversation,
              ...recentMessages.value.filter(m => m.conversationId !== event.message.conversation_id)
            ];
          } else {
            // Add new conversation if we have sender info
            if (event.user) {
              recentMessages.value.unshift({
                id: Date.now(), // Temporary ID
                conversationId: event.message.conversation_id,
                sender: event.user.name,
                avatar: event.user.avatar
                  ? `${runtimeConfig.public.baseUrl}storage/avatars/${event.user.avatar}`
                  : `https://ui-avatars.com/api/?name=${encodeURIComponent(event.user.name)}&background=random`,
                text: event.message.content,
                time: 'Just now',
                read: false
              });

              // Limit to 5 messages
              if (recentMessages.value.length > 5) {
                recentMessages.value = recentMessages.value.slice(0, 5);
              }
            }
          }

          // Increment unread count if it's not from the current user
          if (event.user.id !== authStore.user?.id) {
            unreadMessages.value++;
            countersStore.incrementUnreadMessages();
          }
        });
    } catch (error) {
      console.error('Failed to subscribe to real-time events:', error);
    }
  } else {
    console.warn('Echo is not available or not properly initialized. Real-time updates will not work.');
  }
});
</script>
