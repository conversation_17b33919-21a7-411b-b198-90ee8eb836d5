<template>
    <div class="w-full border-b cursor-pointer">
        <div class="px-5 py-3">
            <h3 class="text-xl text-black font-semibold">Tutorial on Canvas Painting for Beginners
            </h3>
            <p class="text-lg text-black font-medium">Invoice ID : <span class="text-base text-gray-500 font-normal">BRCCRW-11111111</span>
            </p>
        </div>
        <div class="w-full border-t border-gray-100">
            <div class="w-full grid grid-cols-3 gap-4 px-5 py-3">
                <div class="flex items-center space-x-2">
                    <div class="w-10 h-10 flex items-center justify-center bg-red-100 rounded-full">
                        <Icon icon="game-icons:calculator" class="w-5 h-5 text-red-600" />
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold">Total Tickets</h3>
                        <p class="text-base text-gray-600">3</p>
                    </div>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="w-10 h-10 flex items-center justify-center bg-red-100 rounded-full">
                        <IconsGameIconsCash class="w-5 h-5 text-red-600" />
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold">Amount Payed</h3>
                        <p class="text-base font-medium text-gray-600">MWK5,000</p>
                    </div>
                </div>
                <div class="flex items-center space-x-2 cursor-pointer">
                    <div class="w-10 h-10 flex items-center justify-center bg-red-100 rounded-full">
                        <Icon icon="vscode-icons:file-type-pdf2" class="w-5 h-5 text-red-600" />
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold">Download</h3>
                        <p class="text-base font-medium text-gray-600">Soft Copy</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
</script>

<style lang="sass" scoped></style>