<template>
  <div class="w-full border-b flex">
    <div>
      <img :src="`${runtimeConfig.public.baseUrl}storage/events/${event.cover_art}`" :alt="`${event.title}-image`"
        class="w-36 h-28 object-cover group-hover:opacity-100 transition duration-300 ease-in-out" />
    </div>
    <div class="w-full px-2 py-2">
      <div class="w-full flex items-center justify-between">
        <NuxtLink class="hover:text-sky-500 transition duration-150" :to="`/events/${event.slug}`">
          <h3 class="text-lg font-semibold line-clamp-1">{{ event.title }}</h3>
        </NuxtLink>
        <div class="flex items-center space-x-3">
          <div class="relative" v-if="event.is_liked">
            <button class="bg-gray-50" :class="{ 'bg-red-600 text-white': useLikeTooltip }"
              @mouseenter="likeTooltip = true" @mouseleave="likeTooltip = false" @click="toggleLikeTooltip">
              <Icon icon="fluent:heart-28-filled" class="w-5 h-5"
                :class="event.is_liked ? 'text-red-500' : 'text-gray-500'" />
            </button>
            <div v-if="likeTooltip"
              class="absolute text-sm font-medium left-1/2 transform -translate-x-1/2 -translate-y-full -top-1/2 bg-black bg-opacity-50 p-2 text-gray-100 shadow-md pointer-events-none">
              You hearted this event
              <div
                class="absolute left-1/2 transform -translate-x-1/2 translate-y-full w-0 h-0 border-l-8 border-r-8 border-t-8 border-l-transparent border-r-transparent border-t-black border-opacity-50">
              </div>
            </div>
          </div>
          <div class="relative" v-if="event.is_attendee">
            <button class="bg-gray-50" :class="{ 'bg-red-600 text-white': useAttendeeTooltip }"
              @mouseenter="attendeeTooltip = true" @mouseleave="attendeeTooltip = false" @click="toggleAttendeeTooltip">
              <Icon icon="entypo:add-user" class="w-5 h-5"
                :class="event.is_attendee ? 'text-red-500' : 'text-gray-500'" />
            </button>
            <div v-if="attendeeTooltip"
              class="absolute text-sm font-medium left-1/2 transform -translate-x-1/2 -translate-y-full -top-1/2 bg-black bg-opacity-50 p-2 text-gray-100 shadow-md pointer-events-none">
              You added attendance to this event
              <div
                class="absolute left-1/2 transform -translate-x-1/2 translate-y-full w-0 h-0 border-l-8 border-r-8 border-t-8 border-l-transparent border-r-transparent border-t-black border-opacity-50">
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="w-full flex items-center space-x-2 mb-2">
        <Icon icon="mage:calendar-3" class="w-5 h-5 text-gray-500" />
        <p class="text-base text-gray-500 line-clamp-1">{{ formatDateRange(event.start, event.end) }}</p>
      </div>
      <div class="mb-2">
        <button class="flex text-xs items-center transition duration-150 rounded-full px-1 py-1 bg-opacity-15 hover:bg-opacity-50 hover:text-white
            " :class="statusConfig.background">
          <div class="mr-1 flex p-1 items-center rounded-full" :class="statusConfig.fadeBackground">
            <Icon :icon="statusConfig.icon" class="w-3 h-3" :class="statusConfig.text" />
          </div>
          <p class="text-sm pr-1 font-medium" :class="statusConfig.text">{{ determineStatus }}</p>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import type { EventItem } from '@/types';


const props = defineProps<{
  event: EventItem;
}>();

const runtimeConfig = useRuntimeConfig();
const likeTooltip = ref(false);
const useLikeTooltip = ref(false);
const attendeeTooltip = ref(false);
const useAttendeeTooltip = ref(false);

const toggleLikeTooltip = () => {
  useLikeTooltip.value = !useLikeTooltip.value;
};

const toggleAttendeeTooltip = () => {
  useAttendeeTooltip.value = !useAttendeeTooltip.value;
};

const statusClasses = {
  Completed: {
    fadeBackground: 'bg-green-300',
    background: 'bg-green-500 border border-green-100',
    text: 'text-green-500',
    icon: 'fluent-mdl2:completed-solid',
  },
  'In Progress': {
    fadeBackground: 'bg-yellow-300',
    background: 'bg-yellow-500 border border-yellow-100',
    text: 'text-yellow-500',
    icon: 'eos-icons:three-dots-loading',
  },
  Cancelled: {
    fadeBackground: 'bg-red-300',
    background: 'bg-red-500 border border-red-100',
    text: 'text-red-500',
    icon: 'fluent:calendar-cancel-24-regular',
  },
  Upcoming: {
    fadeBackground: 'bg-gray-300',
    background: 'bg-gray-500 border border-gray-100',
    text: 'text-gray-500',
    icon: 'eos-icons:hourglass',
  },
};

const determineStatus = computed(() => {
  const now = new Date();
  const { start, end } = props.event;
  const statusMap = [
    {
      condition: () => now < new Date(start),
      status: 'Upcoming'
    },
    {
      condition: () => now >= new Date(start) && now <= new Date(end),
      status: 'In Progress'
    },
    {
      condition: () => now > new Date(end),
      status: 'Completed'
    }
  ];

  return statusMap.find(item => item.condition())?.status || 'Unknown';
});

const statusConfig = computed(() => {
  const eventStatus = determineStatus.value;
  return statusClasses[eventStatus as keyof typeof statusClasses] || statusClasses.Upcoming;
});

const formatDateRange = (startDate: string, endDate: string) => {
  const start = new Date(startDate);
  const end = new Date(endDate);
  return `${start.toLocaleString()} - ${end.toLocaleString()}`;
};
</script>