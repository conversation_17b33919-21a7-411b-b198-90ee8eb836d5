<template>
    <TransitionRoot appear :show="isOpen" as="template">
        <Dialog as="div" @close="closeModal" class="relative z-10">
            <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
                leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                <div class="fixed inset-0 bg-black/25" />
            </TransitionChild>

            <div class="fixed inset-0 overflow-y-auto">
                <div class="flex min-h-full items-center justify-center p-4 text-center">
                    <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
                        enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
                        leave-to="opacity-0 scale-95">
                        <DialogPanel
                            class="w-full relative max-w-5xl transform overflow-hidden bg-white text-left align-middle shadow-xl transition-all">
                            <DialogTitle class="w-full flex justify-end p-2">
                                <button @click="closeModal" class="cursor-pointer">
                                    <Icon icon="iconamoon:close" class="h-6 w-6" />
                                </button>
                            </DialogTitle>
                            <CoreBalloons class="absolute top-0 lef-0" />
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-8 p-6 bg-white">
                                <div class="space-y-6">
                                    <div>
                                        <h2 class="text-2xl font-semibold">Your event is published 🎉</h2>
                                        <p class="text-gray-600">Future change will be published automatically.</p>
                                    </div>

                                    <div class="space-y-4">
                                        <div class="flex gap-2">
                                            <input type="text" readonly :value="eventLink"
                                                class="flex-1 px-4 py-2 border bg-gray-50" />
                                            <button @click="copyLink"
                                                class="px-4 py-2 border hover:bg-gray-50 flex items-center gap-2">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor"
                                                    viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2"
                                                        d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                                </svg>
                                                Copy
                                            </button>
                                        </div>
                                    </div>

                                    <div class="space-y-4">
                                        <p class="text-center text-gray-600">Scan to open in app</p>
                                        <div class="flex justify-center">
                                            <div class="border-4 border-red-600 p-4 inline-block">
                                                <qrcode-vue :value="eventLink" class="w-32 h-32" />
                                            </div>

                                        </div>
                                        <p class="text-center text-gray-600">
                                            Scan this code with your phone to<br />
                                            open your event in the app.
                                        </p>
                                    </div>
                                </div>

                                <div class="space-y-6">
                                    <div>
                                        <h2 class="text-2xl font-semibold">Invite people to your event</h2>
                                        <p class="text-gray-600">
                                            We'll email them instructions and a link to create an account.
                                        </p>
                                    </div>

                                    <div class="space-y-4">
                                        <div class="flex gap-2">
                                            <input v-model="newEmail" type="email" placeholder="Enter email address"
                                                class="flex-1 px-4 py-2 border" />
                                            <button @click="sendInvite" :disabled="!isValidEmail"
                                                class="px-6 py-2 bg-red-600 text-white hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed">
                                                Send invite
                                            </button>
                                        </div>
                                    </div>

                                    <div class="space-y-3">
                                        <div v-for="member in teamMembers" :key="member.email"
                                            class="flex items-center gap-3 py-2">
                                            <div v-if="member.avatar" class="w-10 h-10 rounded-full overflow-hidden">
                                                <img :src="member.avatar" :alt="member.name"
                                                    class="w-full h-full object-cover" />
                                            </div>
                                            <div v-else
                                                class="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center text-gray-600">
                                                {{ member.email[0].toUpperCase() }}
                                            </div>
                                            <div class="flex-1">
                                                <div v-if="member.name" class="font-medium">{{ member.name }}</div>
                                                <div class="text-gray-600">{{ member.email }}</div>
                                            </div>
                                            <div :class="{
                                                'text-gray-600': member.status === 'Invite sent',
                                                'text-green-600': member.status === 'Invited accepted'
                                            }">
                                                {{ member.status }}
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mt-6 p-4 bg-gray-50">
                                        <div class="flex justify-between items-center">
                                            <div>
                                                <div class="font-medium">{{ usedSeats }}/{{ totalSeats }} team seats
                                                    used</div>
                                                <div class="text-gray-600">
                                                    You can upgrade your account to add more users to your team.
                                                </div>
                                            </div>
                                            <button class="text-red-600 hover:underline">Manage</button>
                                        </div>
                                        <div class="mt-4 bg-gray-200 rounded-full h-2">
                                            <div class="bg-red-600 rounded-full h-2"
                                                :style="{ width: `${(usedSeats / totalSeats) * 100}%` }"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </DialogPanel>
                    </TransitionChild>
                </div>
            </div>
        </Dialog>
    </TransitionRoot>
</template>

<script setup lang="ts">
import {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
} from '@headlessui/vue'
import QrcodeVue from "qrcode.vue";
import type { PropType } from 'vue';
import type { EventItem } from '@/types';


const props = defineProps({
    event: {
        type: Object as PropType<EventItem>,
        required: true
    }
});

const isOpen = ref<boolean>(false);
const currentUrl = useRequestURL()
const eventLink = ref(`${currentUrl.origin}/events/${props.event?.slug}`)
const newEmail = ref('')
const totalSeats = 10
const usedSeats = 6

const closeModal = (): void => {
    isOpen.value = false
    navigateTo('/dashboard/manage-events');
}
const openModal = (): void => {
    isOpen.value = true
    if (isOpen.value) {
        eventLink.value = `${currentUrl.origin}/events/${props.event?.slug}`
    }
}

const teamMembers = ref([
    { email: '<EMAIL>', status: 'Invite sent' },
    { email: '<EMAIL>', status: 'Invite sent' },
    { email: '<EMAIL>', status: 'Invite sent' },
    { email: '<EMAIL>', status: 'Invite sent' },
    {
        name: 'Amélie Laurent',
        email: '<EMAIL>',
        avatar: '/api/placeholder/32/32',
        status: 'Invited accepted'
    }
])

const isValidEmail = computed(() => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(newEmail.value)
})

const copyLink = (): void => {
    navigator.clipboard.writeText(eventLink.value)
}

const sendInvite = (): void => {
    if (isValidEmail.value && usedSeats < totalSeats) {
        teamMembers.value.push({
            email: newEmail.value,
            status: 'Invite sent'
        })
        newEmail.value = ''
    }
}

defineExpose({
    openModal,
    closeModal
})
</script>