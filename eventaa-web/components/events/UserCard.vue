<template>
    <div>
        <Popover v-slot="{ open }" class="relative">
            <PopoverButton class="w-full focus:outline-none focus:ring-0" :class="open ? 'text-white' : 'text-white/90'">
                <div class="px-4 py-2 flex items-center gap-4 border-t border-b border-gray-100">
                    <div class="relative">
                        <img :src="`${runtimeConfig.public.baseUrl}storage/avatars/${props.user.avatar}`"
                            :alt="props.user.name"
                            class="w-12 h-12 rounded-full object-cover border-2 border-gray-50 bg-white" />
                        <Icon v-show="props.user.is_verified" icon="material-symbols:verified-rounded"
                            class="absolute w-4 h-4 mr-2 text-sky-500 bottom-1 -right-2" />
                    </div>
                    <div class="flex flex-col items-start">
                        <h3 class="text-lg font-semibold text-black">Organizer</h3>
                        <p class="text-gray-600 text-base">{{ props.user.name }}</p>
                    </div>
                </div>
            </PopoverButton>

            <transition enter-active-class="transition duration-200 ease-out" enter-from-class="translate-y-1 opacity-0"
                enter-to-class="translate-y-0 opacity-100" leave-active-class="transition duration-150 ease-in"
                leave-from-class="translate-y-0 opacity-100" leave-to-class="translate-y-1 opacity-0">
                <PopoverPanel
                    class="absolute left-1/2 z-10 w-72 max-w-md -translate-x-1/2 transform px-4 sm:px-0 lg:max-w-3xl">
                    <div class="bg-white overflow-hidden rounded shadow-lg">
                        <div>
                            <div class="px-4 py-2 flex items-center gap-4 border-t border-b border-gray-100">
                                <div class="relative">
                                    <img :src="`${runtimeConfig.public.baseUrl}storage/avatars/${props.user.avatar}`"
                                        :alt="props.user.name"
                                        class="w-12 h-12 rounded-full object-cover border-2 border-gray-50 bg-white" />
                                    <Icon v-show="props.user.is_verified"  icon="material-symbols:verified-rounded"
                                        class="absolute w-4 h-4 mr-2 text-sky-500 bottom-1 -right-2" />
                                </div>
                                <div class="flex flex-col items-start">
                                    <p class="text-gray-600 text-base">{{ props.user.name }}</p>
                                    <CoreStarRating :show-rating="true" v-model="props.user.ratings_avg_rating" :disabled="true"/>
                                </div>
                            </div>
                            <div class="w-full border-b grid grid-cols-2 gap-4 px-4">
                                <div class="flex flex-col items-center py-1 border-r">
                                    <h3 class="text-lg font-semibold">Followers</h3>
                                    <p class="text-base">{{ props.user?.followers_count }}</p>
                                </div>
                                <div class="flex flex-col items-center py-1">
                                    <h3 class="text-lg font-semibold">Following</h3>
                                    <p class="text-base">{{ props.user?.following_count }}</p>
                                </div>
                            </div>
                            <div class="px-4 py-2 flex items-center gap-4">
                                <nuxt-link to="#" class="font-thin hover:text-sky-500 hover:underline transition duration-150">View Organizer Profile &rarr;</nuxt-link>
                            </div>
                        </div>
                    </div>
                </PopoverPanel>
            </transition>
        </Popover>
    </div>
</template>

<script setup lang="ts">
import { Popover, PopoverButton, PopoverPanel } from '@headlessui/vue'
import type { PropType } from 'vue';
import type { User } from '@/types/user';

const props = defineProps({
    user: {
        type: Object as PropType<User>,
        required: true,
    },
});
const runtimeConfig = useRuntimeConfig();
</script>