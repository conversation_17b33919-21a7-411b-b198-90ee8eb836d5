<template>
  <div class="h-full bg-white border border-red-50 rounded-b relative flex flex-col">
    <div class="relative">
      <img :src="`${runtimeConfig.public.baseUrl}storage/events/${event.cover_art}`" alt="event-banner"
        class="w-full h-48 object-cover transition-transform duration-300" />
      <div
        class="absolute top-0 left-0 w-full h-full bg-black bg-opacity-10 group-hover:bg-opacity-25 transition-opacity duration-300 flex items-center justify-center">
      </div>
      <div class="absolute top-0 left-0 shadow flex flex-col  transition-opacity duration-300 group-hover:opacity-0">
        <p class="bg-red-500 px-2 py-2 text-center text-white text-xl font-bold">
          {{ dayjs(props.event.start).format(DATE_FORMAT.SINGLE_DAY) }}<br />

        </p>
        <p class="bg-red-700 px-2 py-2 text-center text-white text-xl font-bold">
          <span class="uppercase text-lg">{{ dayjs(props.event.start).format(DATE_FORMAT.SINGLE_MONTH) }}</span>
        </p>
      </div>
      <div
        class="absolute top-0 left-0 flex flex-col opacity-0 group-hover:opacity-100 transition-opacity duration-300">
        <button @click="toggleLike" class="bg-red-600 text-white px-2 py-2 flex flex-col items-center">
          <Icon :icon="liked ? 'mingcute:heart-fill' : 'mingcute:heart-line'"
            class="w-5 h-5 transition-all duration-300" :class="liked ? 'fill-animation' : ''" />
          <span>{{ props.event.likes_count }}</span>
        </button>
      </div>
    </div>

    <div class="flex flex-col flex-grow">
      <div class="px-3 py-1">
        <NuxtLink :to="`/events/${props.event.slug}`">
          <h3 class="text-xl font-semibold px-1 py-1 line-clamp-2 hover:text-sky-500 transition duration-150">
            {{ props.event.title }}
          </h3>
        </NuxtLink>
        <div class="w-full flex items-center justify-between">
          <div class="w-full flex items-center space-x-2">
            <div class="h-6 p-2 flex items-center">
              <Icon v-if="!props.event.meeting_link" icon="fa6-solid:map-location" class="w-6 h-6" />
              <Icon v-else :icon="getProperRemoteIcon(String(props.event.meeting_link.link))" class="w-6 h-6" />
            </div>
            <p class="text-base">{{ props.event.location }}</p>
          </div>
        </div>
      </div>

      <div class="px-3 py-1">
        <div class="flex items-center space-x-2" v-if="props.event?.tiers?.length > 0">
          <div class="h-6 p-2 flex items-center">
            <Icon icon="dashicons:tickets-alt" class="w-6 h-6" />
          </div>
          <p>Tickets starts MK{{ Math.min(...props.event.tiers.map(tier => Number(tier.price))).toLocaleString() }}</p>
        </div>
        <div class="flex items-center space-x-2" v-else>
          <div class="h-6 p-2 flex items-center">
            <Icon icon="fluent-emoji:free-button" class="w-6 h-6" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { PropType } from 'vue';
import type { EventItem } from '@/types';
import dayjs from 'dayjs';

const props = defineProps({
  event: {
    type: Object as PropType<EventItem>,
    required: true,
  }
});

const runtimeConfig = useRuntimeConfig();
const liked = ref<boolean>(false);
const toggleLike = (): void => {
  liked.value = !liked.value;
  liked.value ? props.event.likes_count++ : props.event.likes_count--;
};

const getProperRemoteIcon = (link: string): string => {
  const iconMap = {
    'zoom.us': 'logos:zoom',
    'teams.microsoft.com': 'logos:microsoft-teams',
    'meet.google.com': 'logos:google-meet',
  };
  let icon = "hugeicons:globe";
  for (const [keyword, mappedIcon] of Object.entries(iconMap)) {
    if (link.includes(keyword)) {
      return mappedIcon;
    }
  }
  return icon;
}
</script>

<style lang="scss" scoped>
.fill-animation {
  animation: fillAnimation 0.3s ease-in-out forwards;
}

@keyframes fillAnimation {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.3);
  }

  100% {
    transform: scale(1);
  }
}
</style>