<template>
  <div>
    <button @click="openDialog()"
      class="bg-gray-100 px-2 py-1.5 font-medium flex items-center hover:bg-gray-200 hover:text-gray-500 transition duration-150">
      <Icon icon="gridicons:filter" class="w-5 h-5 mr-2" />
      Filters
    </button>
    <TransitionRoot as="template" :show="open">
      <Dialog class="relative z-10" @close="open = false">
        <TransitionChild as="template" enter="ease-in-out duration-500" enter-from="opacity-0" enter-to="opacity-100"
          leave="ease-in-out duration-500" leave-from="opacity-100" leave-to="opacity-0">
          <div class="fixed inset-0 bg-red-700 bg-opacity-25 transition-opacity" />
        </TransitionChild>

        <div class="fixed inset-0 overflow-hidden">
          <div class="absolute inset-0 overflow-hidden">
            <div class="pointer-events-none fixed inset-y-0 right-0 flex max-w-full pl-10">
              <TransitionChild as="template" enter="transform transition ease-in-out duration-500 sm:duration-700"
                enter-from="translate-x-full" enter-to="translate-x-0"
                leave="transform transition ease-in-out duration-500 sm:duration-700" leave-from="translate-x-0"
                leave-to="translate-x-full">
                <DialogPanel class="pointer-events-auto relative w-screen max-w-sm">
                  <TransitionChild as="template" enter="ease-in-out duration-500" enter-from="opacity-0"
                    enter-to="opacity-100" leave="ease-in-out duration-500" leave-from="opacity-100"
                    leave-to="opacity-0">
                    <div class="absolute left-0 top-0 -ml-8 flex pt-4 sm:-ml-10">
                      <button type="button"
                        class="relative bg-red-600 text-white focus:ring-none focus:outline-none px-2 py-2"
                        @click="open = false">
                        <span class="absolute -inset-2.5" />
                        <span class="sr-only">Close panel</span>
                        <XMarkIcon class="h-6 w-6" aria-hidden="true" />
                      </button>
                    </div>
                  </TransitionChild>
                  <div class="flex h-full flex-col overflow-y-scroll bg-white shadow-xl">
                    <div class="w-full flex items-center justify-between px-2 border-b py-3">
                      <DialogTitle class="text-xl font-semibold tracking-wide text-gray-900">Events Filters
                      </DialogTitle>
                      <div class="flex space-x-2">
                        <button @click="applyFilters"
                          class="flex items-center bg-red-600 text-white px-2 py-1 hover:bg-red-700 transition">
                          <Icon icon="carbon:checkmark" class="w-5 h-5 mr-2" />
                          Apply
                        </button>
                        <button @click="clearAllFilters"
                          class="flex items-center bg-gray-100 border text-gray-500 px-2 py-1 hover:bg-gray-200 transition">
                          <Icon icon="game-icons:magic-broom" class="w-5 h-5 mr-2" />
                          Clear
                        </button>
                      </div>
                    </div>
                    <div class="relative flex-1">
                      <Disclosure as="template" v-slot="{ open: dateOpen }" :defaultOpen="disclosureStates.date">
                        <DisclosureButton @click="() => {
                          disclosureStates.date = !dateOpen;
                          saveDisclosureStates();
                        }" class="flex w-full bg-gray-100 border-b px-4 py-2 items-center justify-between">
                          <h3 class="text-lg font-medium">Date Range</h3>
                          <ChevronUpIcon :class="dateOpen ? 'rotate-180 transform' : ''"
                            class="h-5 w-5 text-gray-500" />
                        </DisclosureButton>
                        <DisclosurePanel class="px-4 pb-2 pt-4 text-sm text-gray-500">
                          <datepicker @cleared="isCleared" required position="left"
                            placeholder="select start & end date" :range="true" input-class-name="datepicker bg-red-500"
                            format="dd/MM/yyyy HH:mm" v-model="dateRange" />
                        </DisclosurePanel>
                      </Disclosure>
                      <Disclosure as="template" v-slot="{ open: locationOpen }"
                        :defaultOpen="disclosureStates.location">
                        <DisclosureButton @click="() => {
                          disclosureStates.location = !locationOpen;
                          saveDisclosureStates();
                        }" class="flex w-full bg-gray-100 px-4 py-2 items-center justify-between border-b">
                          <h3 class="text-lg font-medium">Nearby Location</h3>
                          <ChevronUpIcon :class="locationOpen ? 'rotate-180 transform' : ''"
                            class="h-5 w-5 text-gray-500" />
                        </DisclosureButton>
                        <DisclosurePanel class="px-4 pb-2 pt-4 text-sm text-gray-500">
                          <div>
                            <div>
                              <h3>Current location: {{ currentLocation }}</h3>
                              <button @click="getCurrentLocation"
                                class="mt-2 w-full flex items-center text-sky-500 px-4 py-2 rounded font-medium transition">
                                <Icon icon="tabler:current-location" class="w-5 h-5 mr-2" />
                                {{ isLocating ? 'Locating...' : 'Get Current Location' }}
                              </button>
                            </div>
                            <div class="bg-gray-50 mt-4">
                              <div class="mb-4 px-4 pt-4">
                                <label class="block text-base mt-2 font-light text-gray-700 mb-2">
                                  Radius: {{ radius }} meters
                                </label>
                                <input v-model.number="radius" type="range" min="100" max="5000" step="100"
                                  class="w-full appearance-none slider" />
                              </div>
                              <div class="text-sm text-gray-600 flex justify-between px-4">
                                <span class="font-light">0</span>
                                <span class="font-light">5000</span>
                              </div>

                              <div v-if="center.lat && center.lng" class="h-64 w-full mt-4">
                                <GoogleMap :api-key="runtimeConfig.public.googleMapsApiKey" :center="center" :zoom="14"
                                  style="width: 100%; height: 100%">
                                  <CustomMarker :options="{
                                    position: center,
                                    anchorPoint: 'BOTTOM_CENTER',
                                  }">
                                    <div style="text-align: center">
                                      <div class="relative shadow-2xl shadow-black animate-bounce">
                                        <button
                                          class="relative inline-flex items-center justify-center p-2 rounded-none cursor-pointer bg-red-600 text-white">
                                          <Icon icon="fa6-solid:map-location-dot" class="w-6 h-6" />
                                        </button>
                                        <span
                                          class="absolute left-1/2 -bottom-1 transform -translate-x-1/2 w-0 h-0 border-l-[6px] border-l-transparent border-r-[6px] border-r-transparent border-t-[6px] border-t-red-600"></span>
                                      </div>
                                    </div>
                                  </CustomMarker>
                                  <Circle :options="{
                                    center: center,
                                    radius: radius,
                                    fillColor: '#4299e1',
                                    fillOpacity: 0.1,
                                    strokeColor: '#4299e1',
                                    strokeOpacity: 0.8,
                                    strokeWeight: 2
                                  }" />
                                </GoogleMap>
                              </div>
                            </div>
                          </div>
                        </DisclosurePanel>
                      </Disclosure>
                      <Disclosure as="template" v-slot="{ open: categoryOpen }"
                        :defaultOpen="disclosureStates.category">
                        <DisclosureButton @click="() => {
                          disclosureStates.category = !categoryOpen;
                          saveDisclosureStates();
                        }" class="flex w-full bg-gray-100 border-b px-4 py-2 items-center justify-between">
                          <h3 class="text-lg font-medium">Category</h3>
                          <ChevronUpIcon :class="categoryOpen ? 'rotate-180 transform' : ''"
                            class="h-5 w-5 text-gray-500" />
                        </DisclosureButton>
                        <DisclosurePanel class="px-4 pb-2 pt-4 text-sm text-gray-500">
                          <div class="flex flex-wrap space-x-2">
                            <button type="button" @click="modifySelectedCategory(category)"
                              class="flex items-center bg-gray-100 font-light hover:bg-red-600 hover:text-white transition duration-150 rounded-full px-1.5 py-1.5 mb-1.5"
                              v-for="category in $categories"
                              :class="selectedCategory.find((cat) => category.name == cat.name) ? 'bg-red-600 text-white' : ''">
                              <img :src="`${runtimeConfig.public.baseUrl}storage/categories/${category.icon}`"
                                class="w-6 h-6 mr-2" />
                              {{ category.name }}
                            </button>
                          </div>
                        </DisclosurePanel>
                      </Disclosure>
                      <Disclosure as="template" v-slot="{ open: priceOpen }" :defaultOpen="disclosureStates.price">
                        <DisclosureButton @click="() => {
                          disclosureStates.price = !priceOpen;
                          saveDisclosureStates();
                        }" class="flex w-full bg-gray-100 border-b px-4 py-2 items-center justify-between">
                          <h3 class="text-lg font-medium">Price Range</h3>
                          <ChevronUpIcon :class="priceOpen ? 'rotate-180 transform' : ''"
                            class="h-5 w-5 text-gray-500" />
                        </DisclosureButton>
                        <DisclosurePanel class="px-4 pb-2 pt-4 text-sm text-gray-500">
                          <CorePriceRange />
                        </DisclosurePanel>
                      </Disclosure>
                    </div>
                  </div>
                </DialogPanel>
              </TransitionChild>
            </div>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script setup lang="ts">
import {
  Dialog,
  DialogPanel,
  DialogTitle,
  TransitionChild,
  TransitionRoot,
} from "@headlessui/vue";
import { XMarkIcon } from "@heroicons/vue/24/outline";
import { Disclosure, DisclosureButton, DisclosurePanel } from '@headlessui/vue'
import { ChevronUpIcon } from '@heroicons/vue/20/solid'
import { GoogleMap, Circle } from 'vue3-google-map'
import type { Category } from "@/types";

interface FilterState {
  dateRange: { start: Date; end: Date };
  center: { lat: number; lng: number };
  radius: number;
  currentLocation: string;
  selectedCategory: Category[];
}

const $emit = defineEmits(["apply-filters", "clear-filters"]);
const runtimeConfig = useRuntimeConfig();
const open = ref(false);
const dateRange = ref({
  start: new Date(),
  end: new Date(),
});
const center = ref<{ lat: number; lng: number }>({
  lat: -13.963280001090016,
  lng: 33.792221716425516,
});
const selectedCategory = ref<Category[]>([]);
const { $toast, $categories }: any = useNuxtApp();
const currentLocation = ref("");
const isLocating = ref(false);
const isCleared = () => { };
const radius = ref(1000);
const minPrice = ref(123);
const maxPrice = ref(1789);

const openDialog = (): void => {
  if (!open.value) {
    open.value = true;
    loadDisclosureStates();
  }
};


const defaultFilterState: FilterState = {
  dateRange: {
    start: new Date(),
    end: new Date()
  },
  center: {
    lat: -13.963280001090016,
    lng: 33.792221716425516
  },
  radius: 1000,
  currentLocation: '',
  selectedCategory: []
};

const disclosureStates = ref<Record<string, boolean>>({
  date: false,
  location: false,
  category: false,
  price: false
});

const saveDisclosureStates = (): void => {
  localStorage.setItem('eventFilterDisclosureStates', JSON.stringify(disclosureStates.value));
};

const loadDisclosureStates = (): void => {
  const savedStates = localStorage.getItem('eventFilterDisclosureStates');
  if (savedStates) {
    disclosureStates.value = JSON.parse(savedStates);
  }
};

const getPersistedFilters = (): FilterState => {
  const savedFilters = localStorage.getItem('eventFilters');
  return savedFilters
    ? JSON.parse(savedFilters)
    : { ...defaultFilterState };
};

const modifySelectedCategory = (category: Category) => {
  if (selectedCategory.value.find(c => c.name === category.name)) {
    selectedCategory.value = selectedCategory.value.filter(
      (c) => c.name !== category.name
    );
  } else {
    selectedCategory.value.push(category);
  }
};

const saveFilters = () => {
  const currentFilters: FilterState = {
    dateRange: dateRange.value,
    center: center.value,
    radius: radius.value,
    currentLocation: currentLocation.value,
    selectedCategory: selectedCategory.value
  };

  localStorage.setItem('eventFilters', JSON.stringify(currentFilters));
};

const applyFilters = (): void => {
  saveFilters();

  const filtersToApply: any = {};

  if (disclosureStates.value.location && center.value.lat && center.value.lng) {
    filtersToApply.location = {
      center: center.value,
      radius: radius.value,
      address: currentLocation.value
    };
  }

  if (disclosureStates.value.date) {
    filtersToApply.dateRange = dateRange.value;
  }

  if (disclosureStates.value.price) {
    filtersToApply.price = {
      min: minPrice.value,
      max: maxPrice.value
    }
  }

  if (disclosureStates.value.category) {
    filtersToApply.categories = selectedCategory.value;
  }

  $toast.success("Filters applied")
  $emit('apply-filters', filtersToApply);
  open.value = false;
};

const clearAllFilters = (): void => {
  dateRange.value = { ...defaultFilterState.dateRange };
  center.value = { ...defaultFilterState.center };
  currentLocation.value = defaultFilterState.currentLocation;
  radius.value = defaultFilterState.radius;
  selectedCategory.value = [];
  localStorage.removeItem('eventFilters');
  $toast.success("All filters cleared");
  $emit('clear-filters');
};

watch([dateRange, center, radius, currentLocation, selectedCategory], () => {
  saveFilters();
}, { deep: true });

const getCurrentLocation = async () => {
  isLocating.value = true;
  if (navigator.geolocation) {
    try {
      const position = await new Promise<GeolocationPosition>((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(resolve, reject)
      });
      const { latitude, longitude } = position.coords;
      center.value = {
        lat: latitude,
        lng: longitude,
      };
      try {
        const response = await fetch(
          `${ENDPOINTS.GOOGLE.GEOCODE}=${latitude},${longitude}&key=${runtimeConfig.public.googleMapsApiKey}`
        );
        const data = await response.json();
        if (data.status === "OK" && data.results.length > 0) {
          const address = data.results[0].formatted_address;
          currentLocation.value = address;
        } else {
          console.error("No address found for the location.");
        }
      } catch (error) {
        console.error("Error fetching geocoding data:", error);
      }
    } catch (error) {
      console.error("Error getting current location:", error);
      currentLocation.value = "Unable to retrieve location";
    } finally {
      isLocating.value = false;
    }
  } else {
    console.error("Geolocation is not supported by this browser.");
    isLocating.value = false;
  }
};

onMounted(() => {
  const persistedFilters = getPersistedFilters();

  dateRange.value = persistedFilters.dateRange;
  center.value = persistedFilters.center;
  radius.value = persistedFilters.radius;
  currentLocation.value = persistedFilters.currentLocation;
  selectedCategory.value = persistedFilters.selectedCategory;

  if (!currentLocation.value) {
    getCurrentLocation();
  }
});
</script>

<style scoped>
.slider {
  @apply h-2 bg-gradient-to-r from-red-200 to-red-600 appearance-none cursor-pointer;
}

.slider::-webkit-slider-thumb {
  @apply w-6 h-6 bg-white shadow-lg hover:bg-red-100 appearance-none;
}
</style>