<template>
    <div class="w-full border-b py-2 px-2">
        <div class="w-full items-center flex justify-between">
            <div class="flex items-center space-x-2">
                <Icon icon="dashicons:tickets-alt" class="h-16 w-16 text-gray-400" />
                <div>
                    <h3 class="line-clamp-1 text-lg font-semibold">{{ props.ticket.event.title }}</h3>
                    <p class="text-base font-medium text-gray-600">{{  props.ticket.tier.name }} ticket (x{{ props.ticket.quantity}})</p>
                </div>
            </div>
            <div class="flex items-center space-x-1">
                <h3 class="text-xl font-semibold">MK{{ props.ticket.quantity * Number(props.ticket.tier.price) }}</h3>
                <div class="bg-gray-100 flex items-center px-2 py-2">
                    <button>
                        <Icon icon="icon-park-outline:delete" class="w-5 h-5 text-gray-500" />
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
const props = defineProps({
    ticket: {
        required: true,
        type: Object
    }
})
</script>