<template>
  <div>
    <button @click="openDialog"
      class="p-2 text-gray-400 dark:text-zinc-100 dark:hover:text-zinc-50 hover:text-gray-500">
      <span class="sr-only">Search</span>
      <MagnifyingGlassIcon class="h-6 w-6" aria-hidden="true" />
    </button>
  </div>
  <TransitionRoot appear :show="isOpen" as="template">
    <Dialog as="div" @close="closeDialog" class="relative z-50">
      <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
        leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
        <div class="fixed inset-0 bg-black bg-opacity-50" />
      </TransitionChild>

      <div class="fixed inset-0 overflow-y-auto">
        <div class="flex min-h-full items-center justify-center p-4 text-center">
          <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
            enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95">
            <DialogPanel
              class="w-full max-w-2xl transform overflow-hidden text-left align-middle shadow-xl transition-all">
              <div class="bg-white dark:bg-zinc-800 ">
                <div class="relative p-4">
                  <div class="flex flex-col">
                    <div class="relative flex items-center">
                      <div class="absolute left-3 top-3">
                        <MagnifyingGlassIcon class="h-6 w-6 text-gray-400 dark:text-zinc-100" />
                      </div>
                      <input v-model="searchTerm" placeholder="Search anything..."
                        class="w-full pl-10 pr-20 py-3 bg-white dark:bg-zinc-800 text-gray-700 dark:text-zinc-100 border border-gray-200 dark:border-zinc-700 focus:outline-none focus:ring-0"
                        @keydown.enter="handleSearch" type="search" autocomplete="off" />
                      <div class="absolute top-2 right-2">
                        <div
                          class="flex items-center justify-center px-2 py-1 bg-gray-100 dark:bg-zinc-700 text-sm text-gray-500 dark:text-zinc-300 border border-gray-200 dark:border-zinc-600">
                          <span class="mr-1">⌘</span>K
                        </div>
                      </div>
                    </div>

                    <div v-if="loading" class="flex justify-center py-8">
                      <CoreLoader />
                    </div>

                    <div v-else>
                      <div v-if="events.length > 0" class="mt-4 max-h-96 overflow-y-auto">
                        <div v-for="event in events" :key="event.id"
                          class="p-3 hover:bg-gray-50 dark:hover:bg-zinc-700 flex items-center cursor-pointer">
                          <div class="text-gray-700 dark:text-zinc-100">
                            <NuxtLink :to="`/events/${event.slug}`" class="font-medium">
                              {{ event.title }}
                            </NuxtLink>
                          </div>
                        </div>
                      </div>

                      <div v-else-if="hasSearched" class="py-12 text-center">
                        <div class="mb-6 flex justify-center">
                          <div class="relative shadow-sm rounded-full">
                            <div class="absolute inset-0 flex items-center justify-center">
                              <MagnifyingGlassIcon class="h-8 w-8 text-gray-500 dark:text-zinc-600" />
                            </div>
                            <div class="h-24 w-24 rounded-full bg-gray-100 dark:bg-zinc-700 opacity-80"></div>
                          </div>
                        </div>
                        <h3 class="text-xl font-medium text-gray-700 dark:text-zinc-100">Sorry, no results!</h3>
                        <p class="mt-2 text-gray-500 dark:text-zinc-400">We couldn't find any matches.</p>
                        <p class="text-gray-500 dark:text-zinc-400">Please try again</p>

                        <button @click="clearSearch"
                          class="mt-6 px-4 rounded-full p-1 bg-red-600 dark:bg-zinc-700 text-gray-100 shadow dark:text-zinc-100  hover:bg-red-700 dark:hover:bg-zinc-600 transition-colors">
                          Clear search
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
} from '@headlessui/vue'
import { MagnifyingGlassIcon } from '@heroicons/vue/24/outline'
import { ref } from 'vue'

interface EventItem {
  id: string | number;
  title: string;
  [key: string]: any;
}

interface EventsResponse {
  events: {
    data: EventItem[];
    [key: string]: any;
  };
  [key: string]: any;
}

const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

const isOpen = ref<boolean>(false)
const loading = ref<boolean>(false)
const searchTerm = ref<string>('')
const events = ref<EventItem[]>([])
const hasSearched = ref<boolean>(false)

const openDialog = (): void => {
  isOpen.value = true
}

const closeDialog = (): void => {
  isOpen.value = false
}

const clearSearch = (): void => {
  searchTerm.value = ''
  events.value = []
  hasSearched.value = false
}

const handleSearch = async (): Promise<void> => {
  if (!searchTerm.value.trim()) return

  loading.value = true
  hasSearched.value = true

  try {
    const formData = new FormData();
    formData.append('query', searchTerm.value);
    const response = await httpClient.get<EventsResponse>(`${ENDPOINTS.EVENTS.SEARCH}?title=${searchTerm.value}`);
    if (response) {
      events.value = response.events.data;
    }
  } catch (error: any) {
    if (error.errors) {
      const errors = error.errors;
      Object.keys(errors).forEach((key) => {
        if (Array.isArray(errors[key])) {
          errors[key].forEach((message: string) => {
            $toast.error(message);
          });
        } else if (typeof errors[key] === 'string') {
          $toast.error(errors[key]);
        }
      });
    } else {
      $toast.error('An error occurred during search');
    }
  } finally {
    loading.value = false
  }
}
</script>