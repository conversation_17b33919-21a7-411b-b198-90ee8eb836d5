<template>
  <div class="relative bg-white">
    <input type="text" :value="modelValue"
      @input="$emit('update:modelValue', ($event.target as HTMLInputElement).value)"
      class="w-full py-1.5 pl-8 pr-4 border focus:outline-none focus:border-gray-200 text-gray-600"
      :placeholder="placeholder ?? 'Search...'" />
    <div class="absolute inset-y-0 left-0 flex items-center pl-2 text-gray-500">
      <MagnifyingGlassIcon class="h-6 w-6" aria-hidden="true" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { MagnifyingGlassIcon } from '@heroicons/vue/24/outline';

defineProps<{
  modelValue: string;
  placeholder?: string;
}>();

defineEmits<{
  'update:modelValue': [value: string];
}>();
</script>