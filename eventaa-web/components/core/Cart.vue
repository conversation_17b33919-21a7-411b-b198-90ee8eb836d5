<template>
    <CorePrimaryButton @click="open = true" text="Checkout Cart" startIcon="emojione-monotone:shopping-cart" />
    <TransitionRoot as="template" :show="open">
        <Dialog class="relative z-10" @close="open = false">
            <TransitionChild as="template" enter="ease-in-out duration-500" enter-from="opacity-0"
                enter-to="opacity-100" leave="ease-in-out duration-500" leave-from="opacity-100" leave-to="opacity-0">
                <div class="fixed inset-0 bg-red-500 bg-opacity-25 transition-opacity" />
            </TransitionChild>

            <div class="fixed inset-0 overflow-hidden">
                <div class="absolute inset-0 overflow-hidden">
                    <div class="pointer-events-none fixed inset-y-0 right-0 flex max-w-full pl-10">
                        <TransitionChild as="template"
                            enter="transform transition ease-in-out duration-500 sm:duration-700"
                            enter-from="translate-x-full" enter-to="translate-x-0"
                            leave="transform transition ease-in-out duration-500 sm:duration-700"
                            leave-from="translate-x-0" leave-to="translate-x-full">
                            <DialogPanel class="pointer-events-auto relative w-screen max-w-md">
                                <TransitionChild as="template" enter="ease-in-out duration-500" enter-from="opacity-0"
                                    enter-to="opacity-100" leave="ease-in-out duration-500" leave-from="opacity-100"
                                    leave-to="opacity-0">
                                    <div class="absolute left-0 top-0 -ml-8 flex pt-4 sm:-ml-10">
                                        <button type="button"
                                            class="relative bg-red-600 text-white focus:ring-none focus:outline-none px-2 py-2"
                                            @click="open = false">
                                            <span class="absolute -inset-2.5" />
                                            <span class="sr-only">Close panel</span>
                                            <XMarkIcon class="h-6 w-6" aria-hidden="true" />
                                        </button>
                                    </div>
                                </TransitionChild>
                                <div class="flex relative h-full flex-col overflow-y-scroll bg-white shadow-xl">
                                    <div class="w-full flex items-center px-4 py-3 border-b justify-between">
                                        <DialogTitle class="text-xl font-semibold leading-6 text-gray-900">
                                            Checkout Cart
                                        </DialogTitle>
                                        <div class="flex bg-red-600 text-white pr-1">
                                            <div class="bg-red-300 flex items-center p-1 mr-1">
                                                <Icon icon="noto-v1:shopping-cart" class="w-5 h-5" />
                                            </div>
                                            {{ props.items.length }} items
                                        </div>
                                    </div>
                                    <div>
                                        <div v-for="ticket in items" v-bind:key="ticket.id">
                                            <CartTicketCheckout  :ticket="ticket"/>
                                        </div>
                                    </div>
                                    <div class="absolute bottom-0 w-full bg-white p-4 border-t">
                                        <div class="w-full flex flex-col space-y-1 py-2">
                                            <h3 class="text-xl font-semibold">Total: MK{{ totalPrice.toLocaleString() }}</h3>
                                            <p class="text-base text-gray-500">Yes, there are no extra fees</p>
                                        </div>
                                        <div class="w-full flex justify-end">
                                            <CorePrimaryButton :loading="processing" text="Complete Payment" @click="completeTicketPurchase" />
                                        </div>
                                    </div>
                                </div>
                            </DialogPanel>
                        </TransitionChild>
                    </div>
                </div>
            </div>
        </Dialog>
    </TransitionRoot>
</template>

<script setup lang="ts">
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue'
import { XMarkIcon } from '@heroicons/vue/24/outline'
import type { PropType } from 'vue';
import { ENDPOINTS } from '../../utils/api';

const open = ref<boolean>(false);
const emit = defineEmits(['update']);
const processing = ref<boolean>(false);
const httpClient = useHttpClient();
const { $toast } : any = useNuxtApp();
const props = defineProps({
    items: {
    type: Array as PropType<any[]>,
        required: true  
    }
});

const totalPrice = computed(() => {
    return props.items.reduce((total, item) => total + item.quantity * item.tier.price, 0);
});

const errorHandlers = {
  getErrorMessages: (obj: any): string[] => {
    if (!obj) return [];

    return Object.values(obj).flatMap(value => {
      if (Array.isArray(value)) return value;
      if (typeof value === 'string') return [value];
      if (typeof value === 'object' && value !== null) return errorHandlers.getErrorMessages(value);
      return [];
    });
  },

  formatErrors: (messages: string[]): string =>
    messages.length > 0 ? messages.join('\n') : 'An unexpected error occurred',

  parseError: (error: any): string[] => {
    if (error.message?.message) return errorHandlers.getErrorMessages(error.message.message);
    if (error.message?.email) return Array.isArray(error.message.email) ? error.message.email : [error.message.email];
    return [];
  }
};

const handleError = (error: any, toast: any): void => {
  const messages = errorHandlers.parseError(error);
  toast.error(errorHandlers.formatErrors(messages));
};

const completeTicketPurchase = async(): Promise<void> => {
    processing.value = true;
    try{
        const response: any = await httpClient.post(ENDPOINTS.TICKETS.BUY, {
            tickets: props.items.map(item => ({
                tier_id: item.tier.id,
                quantity: item.quantity,
            })),
            event_id: props.items[0].event.id
        });
        if(response){
            $toast.success(response.message);
            open.value = false;
            emit('update', true)
        }
    }catch(error: any){
        handleError(error, $toast);
        console.error(error);
    }finally{
        open.value = false;
        processing.value = false;
    }
}
</script>