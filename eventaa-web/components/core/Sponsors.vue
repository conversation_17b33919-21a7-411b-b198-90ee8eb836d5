<template>
    <div class="bg-white py-10 mt-4">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="mt-12 relative">
                <button @click="handleScrollLeft"
                    class="absolute left-0 top-1/2 -translate-y-1/2 bg-red-600 text-white p-2 z-10 h">
                    <Icon icon="uiw:left" class="h-6 w-6" />
                </button>

                <div id="sponsors-container" class="overflow-x-hidden flex space-x-8 no-scrollbar scroll-smooth"
                    style="scroll-behavior: smooth">
                    <div v-for="(sponsor, index) in sponsors" :key="index" class="flex-none w-64">
                        <div class="bg-white p-6 transition-shadow duration-300">
                            <img :src="`${runtimeConfig.public.baseUrl}storage/sponsors/${sponsor.sponsor.logo}`" :alt="`${sponsor.sponsor.name} logo`"
                                class="w-full h-32 object-contain mb-4" />
                            <div class="text-center">
                                <h3 class="text-lg font-semibold text-gray-900">
                                    {{ sponsor.sponsor.name }}
                                </h3>
                            </div>
                        </div>
                    </div>
                </div>

                <button @click="handleScrollRight"
                    class="absolute right-0 top-1/2 -translate-y-1/2 bg-red-600 text-white p-2 z-10">
                    <Icon icon="uiw:right" class="h-5 w-5" />
                </button>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, type PropType } from 'vue';
import type { SponsorItem } from '@/types';

const props = defineProps({
    sponsors: {
        type: Array as PropType<SponsorItem[]>,
        required: true,
    },
});

const runtimeConfig = useRuntimeConfig();

const handleScrollLeft = (): void => {
    const container = document.getElementById('sponsors-container');
    if (container) {
        container.scrollLeft -= 200;
    }
};

const handleScrollRight = (): void => {
    const container = document.getElementById('sponsors-container');
    if (container) {
        container.scrollLeft += 200;
    }
};
</script>