/**
 * Utility function to handle API errors and display appropriate toast messages
 * @param error - The error object from the API call
 * @param toast - The toast instance from useNuxtApp().$toast
 * @param defaultMessage - Optional default message to show if no specific error message is found
 */
export const handleError = (error: any, toast: any, defaultMessage: string = 'An error occurred. Please try again.') => {
  console.error('API Error:', error);
  
  // If the error has a response with data and message
  if (error.response?.data?.message) {
    toast.error(error.response.data.message);
    return;
  }
  
  // If the error has a response with validation errors
  if (error.response?.data?.errors) {
    const errors = error.response.data.errors;
    const firstError = Object.values(errors)[0];
    
    if (Array.isArray(firstError) && firstError.length > 0) {
      toast.error(firstError[0]);
    } else if (typeof firstError === 'string') {
      toast.error(firstError);
    } else {
      toast.error('Validation error. Please check your input.');
    }
    return;
  }
  
  // If the error has a message property
  if (error.message) {
    toast.error(error.message);
    return;
  }
  
  // Default error message
  toast.error(defaultMessage);
};
