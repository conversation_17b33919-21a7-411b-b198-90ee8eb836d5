export default defineNuxtConfig({
  compatibilityDate: '2024-04-03',
  devtools: { enabled: true },
  ssr: false,
  modules: ['@nuxtjs/tailwindcss', '@formkit/nuxt', '@pinia/nuxt', '@pinia-plugin-persistedstate/nuxt', '@vueform/nuxt', '@nuxtjs/color-mode'],
  build: {
    transpile: ["oh-vue-icons"]
  },
  formkit: {
    autoImport: true,
  },
  colorMode: {
    classSuffix: ''
  },
  css: [
    "~/assets/sass/fonts.scss",
    "~/assets/sass/slider.scss",
    "~/assets/sass/scheduler.scss",
    "~/assets/css/dashboard-theme.css",
  ],
  runtimeConfig: {
    public: {
      googleMapsApiKey: process.env.GOOGLE_MAPS_API_KEY,
      apiBaseUrl: process.env.API_BASE_URL,
      baseUrl: process.env.BASE_URL,
    },
  },
  devServer: {
    port: 4000,
    host: "0.0.0.0"
  }
})