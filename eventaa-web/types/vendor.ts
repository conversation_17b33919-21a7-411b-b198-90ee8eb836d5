import type { User } from "./user";

interface ExpandedState {
    services: boolean;
    priceRange: boolean;
    location: boolean;
    rating: boolean;
    availability: boolean;
}

interface FilterOptions {
    services: ServiceOption[];
    price_range: {
        min: number;
        max: number;
    };
    locations: string[];
    rating_range: {
        min: number;
        max: number;
    };
}

interface ServiceOption {
    id: number;
    name: string;
    description: string;
}

interface Service {
    id: number;
    vendor_id: number;
    service_id: number;
    created_at: string;
    updated_at: string;
    service: {
        id: number;
        name: string;
        description: string;
        created_at: string;
        updated_at: string;
    }
}

interface Media {
    id: number;
    vendor_id: number;
    type: string;
    path: string;
    title: string;
    created_at: string;
    updated_at: string;
}

interface Price {
    id: number;
    vendor_id: number;
    currency_id: number;
    price: string;
    currency: { id: number, name: string };
    description: string | null;
    created_at: string;
    updated_at: string;
}

interface Rating {
    id: number;
    user_id: number;
    vendor_id: number;
    rating: number;
    comment: string;
    created_at: string;
    updated_at: string;
    user: User;
    response?: string;
    response_date?: string;
}

interface RatingStats {
    average: number;
    total: number;
    distribution: {
        1: number;
        2: number;
        3: number;
        4: number;
        5: number;
    };
    categories?: {
        [key: string]: number;
    };
}

interface RatingResponse {
    current_page: number;
    data: Rating[];
    first_page_url: string;
    from: number;
    last_page: number;
    last_page_url: string;
    links: PaginationLink[];
    next_page_url: string | null;
    path: string;
    per_page: number;
    prev_page_url: string | null;
    to: number;
    total: number;
}

interface ApiVendor {
    id: number;
    user_id: number;
    name: string;
    slug: string;
    logo: string;
    bio: string;
    location: string;
    languages: string;
    phone: string;
    business_email: string;
    website: string | null;
    facebook: string | null;
    instagram: string | null;
    twitter: string | null;
    likes_count: number;
    is_verified: number;
    is_available: number;
    created_at: string;
    updated_at: string;
    ratings_avg_rating: number | null;
    ratings_count: number | null;
    services: Service[];
    media: Media[];
    prices: Price[];
    user: User;
    ratings: Rating[];
    status?: 'pending' | 'approved' | 'rejected' | 'suspended';
}

interface PaginationLink {
    url: string | null;
    label: string;
    active: boolean;
}

interface Pagination {
    current_page: number;
    first_page_url: string;
    from: number;
    last_page: number;
    last_page_url: string;
    links: PaginationLink[];
    next_page_url: string | null;
    path: string;
    per_page: number;
    prev_page_url: string | null;
    to: number;
    total: number;
}

interface ApiResponse {
    current_page: number;
    data: ApiVendor[];
    first_page_url: string;
    from: number;
    last_page: number;
    last_page_url: string;
    links: PaginationLink[];
    next_page_url: string | null;
    path: string;
    per_page: number;
    prev_page_url: string | null;
    to: number;
    total: number;
}

interface WorkImage {
    url: string;
    alt?: string;
}

interface Vendor {
    id: number;
    name: string;
    slug: string;
    avatar: string;
    isVerified: boolean;
    location: string;
    rating: number;
    reviewCount: number;
    serviceTypes: string[];
    description: string;
    workImages: WorkImage[];
    startingPrice: number;
    priceCurrency: string;
}

export type {
    ExpandedState,
    FilterOptions,
    ServiceOption,
    Service,
    Media,
    Price,
    Rating,
    RatingStats,
    RatingResponse,
    ApiVendor,
    ApiResponse,
    Pagination,
    Vendor,
    WorkImage,
}
