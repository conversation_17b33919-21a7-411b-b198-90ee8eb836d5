import type { EventItem } from ".";
import type { User } from "./user";

export interface ApiResponse<T> {
    data: T;
    status: number;
    message?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T> {
    meta: {
        currentPage: number;
        totalPages: number;
        totalItems: number;
        itemsPerPage: number;
    };
}

export interface ApiError {
    message: string;
    status: number;
    errors?: Record<string, string[]>;
}

export interface EventsResponse {
    events: {
        data: EventItem[];
        total: number;
        per_page: number;
        current_page: number;
        last_page: number;
    }
}

export interface AuthResponse {
    message: string
    user: User
    token: string
}

export interface AuthState {
    user: User | null
    token: string | null
    message: string | null
    isAuthenticated: boolean
}

export interface GenericResponse {
    message: string;
}

export interface Notification {
    id: number;
    type: string;
    data: any;
    read_at: string | null;
    created_at: string;
    updated_at: string;
}

export interface NotificationsResponse {
    data: Notification[];
    total: number;
    per_page: number;
    current_page: number;
    last_page: number;
    prev_page_url: string;
    next_page_url: string;
}