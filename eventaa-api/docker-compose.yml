version: "3"
services:
    #PHP Service
    app:
        build:
            context: .
            dockerfile: Dockerfile
        image: digitalocean.com/php
        container_name: app
        restart: unless-stopped
        tty: true
        environment:
            SERVICE_NAME: app
            SERVICE_TAGS: dev
        working_dir: /var/www
        volumes:
            - ./:/var/www
            - /var/www/vendor
        networks:
            - app-network

    #Nginx Service
    webserver:
        image: nginx:alpine
        container_name: webserver
        restart: unless-stopped
        tty: true
        ports:
            - "9000:80"
            - "443:443"
        volumes:
            - ./:/var/www
            - ./nginx/conf.d/:/etc/nginx/conf.d/
        networks:
            - app-network

    #MySQL Service
    db:
        image: mysql:5.7.22
        container_name: db
        restart: unless-stopped
        tty: true
        ports:
            - "3306:3306"
        environment:
            MYSQL_DATABASE: eventaadb
            MYSQL_ROOT_PASSWORD: MAkc-2023
            SERVICE_TAGS: dev
            SERVICE_NAME: mysql
        volumes:
            - dbdata:/var/lib/mysql/
        networks:
            - app-network

#Docker Networks
networks:
    app-network:
        driver: bridge
#Volumes
volumes:
    dbdata:
        driver: local
