<?php

use Illuminate\Support\Facades\Broadcast;

/*
|--------------------------------------------------------------------------
| Broadcast Channels
|--------------------------------------------------------------------------
|
| Here you may register all of the event broadcasting channels that your
| application supports. The given channel authorization callbacks are
| used to check if an authenticated user can listen to the channel.
|
*/

Broadcast::channel('App.Models.User.{id}', function ($user, $id) {
    return (int) $user->id === (int) $id;
});

Broadcast::channel('notifications.{id}', function ($user, $id) {
    return (int) $user->id === (int) $id;
});

Broadcast::channel('conversation.{conversationId}', function ($user, $conversationId) {
    $conversation = \App\Models\Conversation::find($conversationId);

    if (!$conversation) {
        return false;
    }

    $vendor = \App\Models\Vendor::where('user_id', $user->id)->first();

    return $conversation->user_id === $user->id ||
           ($vendor && $conversation->vendor_id === $vendor->id);
});

Broadcast::channel('user-status', function ($user) {
    return true;
});

Broadcast::channel('vendor-analytics.{vendorId}', function ($user, $vendorId) {
    $vendor = \App\Models\Vendor::find($vendorId);

    if (!$vendor) {
        return false;
    }

    return (int) $user->id === (int) $vendor->user_id || $user->hasRole('admin');
});
