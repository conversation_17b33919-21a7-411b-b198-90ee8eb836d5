<?php

use App\Events\TestEvent;
use App\Http\Controllers\ArticleController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\BookingController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\EventController;
use App\Http\Controllers\FollowerController;
use App\Http\Controllers\InterestController;
use App\Http\Controllers\InvoiceController;
use App\Http\Controllers\MetadataController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\PermissionController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\RatingController;
use App\Http\Controllers\RetailerController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\ServicesController;
use App\Http\Controllers\SettingController;
use App\Http\Controllers\SponsorController;
use App\Http\Controllers\SubscriptionController;
use App\Http\Controllers\MessageController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\TicketController;
use App\Http\Controllers\TierController;
use App\Http\Controllers\TwitterController;
use App\Http\Controllers\TwoFactorAuthController;
use App\Http\Controllers\VendorAnalyticsController;
use App\Http\Controllers\VendorBookingController;
use App\Http\Controllers\VendorController;
use App\Http\Controllers\VendorRatingController;
use App\Http\Controllers\VendorServicesController;
use App\Http\Controllers\VenuesController;
use App\Http\Controllers\VerificationController;
use App\Http\Controllers\VerifyEmailController;
use App\Models\Currency;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\UserController;
use App\Http\Controllers\DashboardController;

Route::get('/test-websockets', function () {
    event(new TestEvent('Hello WebSockets!'));
    return response()->json(['message' => 'Test event sent']);
});

Route::middleware(['auth:sanctum', 'reset.deactivation'])->prefix('profile')->group(function () {
    Route::get('user', [ProfileController::class, 'getProfile'])->name('profile.user');
    Route::post('/update-bio', [ProfileController::class, 'updateBio'])->name('profile.update-bio');
    Route::post('change-photo', [ProfileController::class, 'changeProfilePhoto'])->name('profile.photo');
    Route::post('update', [ProfileController::class, 'updateProfile'])->name('profile.update');
    Route::post('firebase-token', [ProfileController::class, 'saveFirebaseToken'])->name('firebase.token');
});

Route::middleware(['auth:sanctum', 'reset.deactivation'])->group(function () {
    Route::post('/user/deactivate', [AuthController::class, 'deactivateUser']);
});

Route::get('/currency', function () {
    return response()->json([
        'currencies' => Currency::all()
    ]);
})->middleware('auth:sanctum');

Route::get('/email/verify/{id}/{hash}', [VerifyEmailController::class, '__invoke'])
    ->middleware(['signed', 'throttle:6,1'])
    ->name('verification.verify');

Route::post('/email/verify/resend', function (Request $request) {
    $request->user()->sendEmailVerificationNotification();
    return back()->with('message', 'Verification link sent!');
})->middleware(['auth:api', 'throttle:6,1'])->name('verification.send');

Route::group(['prefix' => 'auth'], function () {
    Route::post('google/login', [AuthController::class, 'handleGoogleCallback'])->name('auth.google.login');
    Route::post('google/web-login', [AuthController::class, 'googleLogin'])->name('auth.google.web-login');
    Route::post('login', [AuthController::class, 'login'])->name('auth.login');
    Route::post('register', [AuthController::class, 'register'])->name('auth.register');
    Route::post('forgot-password', [AuthController::class, 'forgotPassword'])->name('password.reset');
    Route::post('reset-password', [AuthController::class, 'resetPassword'])->name('auth.resetPassword');
    Route::post('logout', [AuthController::class, 'logout'])->name('auth.logout');
});

Route::middleware('auth:sanctum')->post('/auth/reset-password-a', [AuthController::class, 'authResetPassword'])->name('auth.authResetPassword');

Route::middleware(['auth:sanctum', 'reset.deactivation'])->prefix('settings')->group(function () {
    Route::get('get', [SettingController::class, 'getUserSettings'])->name('settings.get');
    Route::post('create', [SettingController::class, 'addUserSetting'])->name('settings.create');
    Route::post('delete', [SettingController::class, 'deleteUserSettings'])->name('settings.delete');
    Route::get('sessions', [SettingController::class, 'getSessions'])->name('settings.sessions');
    Route::post('revoke-session', [SettingController::class, 'revokeSession'])->name('settings.revoke-session');
});

Route::prefix('2fa')->group(function () {
    Route::post('verify', [TwoFactorAuthController::class, 'verifyCode'])->name('2fa.verify');
    Route::post('send-code', [TwoFactorAuthController::class, 'sendCode'])->name('2fa.send-code');

    Route::middleware(['auth:sanctum', 'reset.deactivation'])->group(function () {
        Route::get('status', [TwoFactorAuthController::class, 'status'])->name('2fa.status');
        Route::post('enable', [TwoFactorAuthController::class, 'enable'])->name('2fa.enable');
        Route::post('disable', [TwoFactorAuthController::class, 'disable'])->name('2fa.disable');
    });
});

Route::prefix('payments')->group(function () {
    Route::post('webhook', [PaymentController::class, 'handleWebhook'])->name('payments.webhook');
    Route::get('verify', [PaymentController::class, 'verifyPayment'])->name('payments.verify');

    Route::middleware(['auth:sanctum', 'reset.deactivation'])->group(function () {
        Route::post('initialize', [PaymentController::class, 'initializeBookingPayment'])->name('payments.initialize');
        Route::get('methods/{vendor_id}', [PaymentController::class, 'getVendorPaymentMethods'])->name('payments.methods');
        Route::get('user-transactions', [PaymentController::class, 'getUserTransactions'])->name('payments.user-transactions');
        Route::get('vendor-transactions', [PaymentController::class, 'getVendorTransactions'])->name('payments.vendor-transactions');
    });
});

Route::group(['prefix' => 'interest'], function () {
    Route::middleware('auth:sanctum')->post('create', [InterestController::class, 'create'])->name('interest.create');
});

Route::middleware(['auth:sanctum', 'reset.deactivation'])->prefix('notifications')->group(function () {
    Route::get('get', [NotificationController::class, 'index'])->name('notifications.get');
    Route::put('mark-as-read/{id}', [NotificationController::class, 'markAsRead'])->name('notifications.mark-as-read');
    Route::get('mark-all-as-read', [NotificationController::class, 'markAllAsRead'])->name('notifications.mark-all-as-read');
    Route::delete('delete/{id}', [NotificationController::class, 'deleteNotification'])->name('notifications.delete');
});


Route::prefix('events')->group(function () {
    Route::get('get', [EventController::class, 'index'])->name('event.get');
    Route::get('search', [EventController::class, 'search'])->name('event.search');
    Route::middleware('auth:sanctum')->get('read/{id}', [EventController::class, 'read'])->name('event.read');
    Route::middleware('auth:sanctum')->get('tickets/{id}', [EventController::class, 'eventTickets'])->name('event.read');
    Route::get('trending', [EventController::class, 'getTrending'])->name('event.trending');
    Route::get('slug/{slug}', [EventController::class, 'readBySlug'])->name('event.slug');
    Route::get('category/{id}', [EventController::class, 'byCategory'])->name('event.category');
    Route::post('nearby', [EventController::class, 'getNearbyEvents'])->name('event.nearby');
});

Route::prefix('venues')->group(function () {
    Route::get('get', [VenuesController::class, 'index'])->name('venues.get');
    Route::middleware(['auth:sanctum', 'reset.deactivation'])->get('user', [VenuesController::class, 'user'])->name('event.user');
    Route::middleware(['auth:sanctum', 'reset.deactivation'])->post('create', [VenuesController::class, 'create'])->name('venues.create');
    Route::get('read/{slug}', [VenuesController::class, 'show'])->name('venues.read');
    Route::middleware(['auth:sanctum', 'reset.deactivation'])->post('update/{slug}', [VenuesController::class, 'update'])->name('venues.update');
    Route::middleware(['auth:sanctum', 'reset.deactivation'])->post('update-status/{id}', [VenuesController::class, 'updateStatus'])->name('venues.updateStatus');
    Route::get('bookings/{id}', [VenuesController::class, 'bookings'])->name('venues.bookings');
    Route::middleware(['auth:sanctum', 'reset.deactivation'])->delete('delete/{id}', [VenuesController::class, 'destroy'])->name('venues.delete');
    Route::middleware(['auth:sanctum', 'reset.deactivation'])->post('rate', [VenuesController::class, 'rate'])->name('venues.rate');
    Route::get('locations', [VenuesController::class, 'locations'])->name('venues.locations');
});

Route::middleware('auth:sanctum')->group(function () {
    Route::get('venue-bookings', [BookingController::class, 'index']);
    Route::post('venue-bookings', [BookingController::class, 'store']);
    Route::get('venue-bookings/{id}', [BookingController::class, 'show']);
    Route::post('venue-bookings/create-or-update', [BookingController::class, 'createOrUpdate']);
    Route::delete('venue-bookings/{id}', [BookingController::class, 'destroy']);
    Route::patch('venue-bookings/{id}/status', [BookingController::class, 'updateStatus']);
});

Route::prefix('vendors')->group(function () {
    Route::get('get', [VendorController::class, 'index'])->name('vendors.get');
    Route::get('read/{slug}', [VendorController::class, 'read'])->name('vendors.read');

    Route::middleware(['auth:sanctum', 'reset.deactivation'])->group(function () {
        Route::get('profile', [VendorController::class, 'profile'])->name('vendors.profile');
        Route::post('create', [VendorController::class, 'create'])->name('vendors.create');
        Route::post('update/{id}', [VendorController::class, 'update'])->name('vendors.update');
        Route::delete('delete/{id}', [VendorController::class, 'destroy'])->name('vendors.delete');
        Route::get('pending', [VendorController::class, 'pendingRequests'])->name('vendors.pending');
        Route::post('approve/{id}', [VendorController::class, 'approve'])->name('vendors.approve');
        Route::post('reject/{id}', [VendorController::class, 'reject'])->name('vendors.reject');
        Route::post('like', [VendorController::class, 'likeVendor'])->name('vendors.like');
        Route::get('like-status/{vendorId}', [VendorController::class, 'checkLikeStatus'])->name('vendors.like.status');
    });

    Route::middleware(['auth:sanctum', 'reset.deactivation'])->get('analytics/dashboard', [VendorAnalyticsController::class, 'getDashboardAnalytics'])->name('vendors.analytics.dashboard');

    Route::prefix('services')->group(function () {
        Route::get('get/{vendor_id}', [VendorServicesController::class, 'index'])->name('vendor.services.get');
        Route::post('create', [VendorServicesController::class, 'store'])->name('vendor.services.create');
        Route::get('read/{id}', [VendorServicesController::class, 'show'])->name('vendor.services.read');
        Route::put('update/{id}', [VendorServicesController::class, 'update'])->name('vendor.services.update');
        Route::delete('delete/{id}', [VendorServicesController::class, 'destroy'])->name('vendor.services.delete');
    });

    Route::prefix('prices')->middleware(['auth:sanctum', 'reset.deactivation'])->group(function () {
        Route::get('{vendor_id}', [App\Http\Controllers\VendorPriceController::class, 'index'])->name('vendor.prices.get');
        Route::post('create', [App\Http\Controllers\VendorPriceController::class, 'store'])->name('vendor.prices.create');
        Route::get('read/{id}', [App\Http\Controllers\VendorPriceController::class, 'show'])->name('vendor.prices.read');
        Route::put('update/{id}', [App\Http\Controllers\VendorPriceController::class, 'update'])->name('vendor.prices.update');
        Route::delete('delete/{id}', [App\Http\Controllers\VendorPriceController::class, 'destroy'])->name('vendor.prices.delete');
    });

    Route::prefix('custom-pricing')->middleware(['auth:sanctum', 'reset.deactivation'])->group(function () {
        Route::get('{vendor_id}', [App\Http\Controllers\VendorCustomPricingController::class, 'show'])->name('vendor.custom-pricing.get');
        Route::post('create', [App\Http\Controllers\VendorCustomPricingController::class, 'store'])->name('vendor.custom-pricing.create');
        Route::put('update/{vendor_id}', [App\Http\Controllers\VendorCustomPricingController::class, 'update'])->name('vendor.custom-pricing.update');
    });

    Route::get('filter-options', [VendorController::class, 'getFilterOptions'])->name('vendors.filter.options');

    Route::prefix('bookings')->group(function () {
        Route::middleware(['auth:sanctum'])->group(function () {
            Route::get('get', [VendorBookingController::class, 'index'])->name('vendor.bookings.get');
            Route::get('user', [VendorBookingController::class, 'userBookings'])->name('vendor.bookings.user');
            Route::get('vendor', [VendorBookingController::class, 'vendorBookings'])->name('vendor.bookings.vendor');
            Route::post('create', [VendorBookingController::class, 'store'])->name('vendor.bookings.create');
            Route::get('read/{id}', [VendorBookingController::class, 'show'])->name('vendor.bookings.read');
            Route::put('update/{id}', [VendorBookingController::class, 'update'])->name('vendor.bookings.update');
            Route::put('status/{id}', [VendorBookingController::class, 'updateStatus'])->name('vendor.bookings.status');
            Route::delete('delete/{id}', [VendorBookingController::class, 'destroy'])->name('vendor.bookings.delete');
            Route::get('pending-count', [VendorBookingController::class, 'getPendingCount'])->name('vendor.bookings.pending-count');
        });

        Route::get('available-slots', [VendorBookingController::class, 'getAvailableTimeSlots'])->name('vendor.bookings.available-slots');
    });

    Route::prefix('ratings')->group(function () {
        Route::get('get/{vendor_id}', [VendorRatingController::class, 'index'])->name('vendor.ratings.get');
        Route::post('create', [VendorRatingController::class, 'store'])->name('vendor.ratings.create');
        Route::get('read/{id}', [VendorRatingController::class, 'show'])->name('vendor.ratings.read');
        Route::put('update/{id}', [VendorRatingController::class, 'update'])->name('vendor.ratings.update');
        Route::delete('delete/{id}', [VendorRatingController::class, 'destroy'])->name('vendor.ratings.delete');
        Route::get('user/{vendor_id}', [VendorRatingController::class, 'getUserRating'])->name('vendor.ratings.user');
        Route::get('pending-count', [VendorRatingController::class, 'getPendingCount'])->name('vendor.ratings.pending-count');
        Route::get('can-rate/{vendor_id}', [VendorRatingController::class, 'canRateVendor'])->name('vendor.ratings.can-rate');
    });

    Route::prefix('portfolio')->middleware(['auth:sanctum', 'reset.deactivation'])->group(function () {
        Route::get('{vendor_id}', [App\Http\Controllers\VendorPortfolioController::class, 'index'])->name('vendor.portfolio.get');
        Route::post('create', [App\Http\Controllers\VendorPortfolioController::class, 'store'])->name('vendor.portfolio.create');
        Route::get('read/{id}', [App\Http\Controllers\VendorPortfolioController::class, 'show'])->name('vendor.portfolio.read');
        Route::post('update/{id}', [App\Http\Controllers\VendorPortfolioController::class, 'update'])->name('vendor.portfolio.update');
        Route::delete('delete/{id}', [App\Http\Controllers\VendorPortfolioController::class, 'destroy'])->name('vendor.portfolio.delete');
    });

    Route::prefix('share')->group(function () {
        Route::post('/', [App\Http\Controllers\VendorShareController::class, 'share'])->name('vendors.share');
        Route::get('stats/{vendor_id}', [App\Http\Controllers\VendorShareController::class, 'getShareStats'])
            ->middleware(['auth:sanctum', 'reset.deactivation'])
            ->name('vendors.share.stats');
    });
});

Route::middleware(['auth:sanctum'])->prefix('messages')->group(function () {
    Route::get('conversations', [MessageController::class, 'getConversations'])->name('messages.conversations');
    Route::post('conversations', [MessageController::class, 'getOrCreateConversation'])->name('messages.conversations.create');
    Route::get('conversations/{conversationId}', [MessageController::class, 'getMessages'])->name('messages.get');
    Route::post('send', [MessageController::class, 'sendMessage'])->name('messages.send');
    Route::put('read/{conversationId}', [MessageController::class, 'markConversationAsRead'])->name('messages.read');
    Route::delete('{messageId}', [MessageController::class, 'deleteMessage'])->name('messages.delete');
    Route::post('status', [MessageController::class, 'updateOnlineStatus'])->name('messages.status.update');
    Route::get('status/{userId}', [MessageController::class, 'getUserStatus'])->name('messages.status.get');
});

Route::middleware(['auth:sanctum'])->prefix('vendors/messages')->group(function () {
    Route::get('unread-count', [MessageController::class, 'getUnreadCount'])->name('vendors.messages.unread-count');
});

Route::middleware(['auth:sanctum', 'reset.deactivation'])->prefix('events')->group(function () {
    Route::post('create', [EventController::class, 'create'])->name('event.create');
    Route::post('update/{id}', [EventController::class, 'update'])->name('event.update');
    Route::delete('delete/{id}', [EventController::class, 'delete'])->name('event.delete');
    Route::post('add-attendance', [EventController::class, 'addAttendee'])->name('event.addAttendance');
    Route::post('like', [EventController::class, 'likeEvent'])->name('event.like');
    Route::get('user', [EventController::class, 'userEvents'])->name('event.user');
    Route::post('share', [EventController::class, 'share'])->name('event.share');
    Route::get('calendar', [EventController::class, 'calendar'])->name('event.calendar');
    Route::post('enable-notifications', [EventController::class, 'allowNotifications'])->name('event.enableNotification');
    Route::post('publish', [EventController::class, 'publish'])->name('event.publish');
    Route::post('add-sponsors', [EventController::class, 'addEventSponsors'])->name('event.addEventSponsors');
});

Route::group(['prefix' => 'category'], function () {
    Route::get('index', [CategoryController::class, 'index'])->name('category.index');
    Route::post('create', [CategoryController::class, 'create'])->name('category.create');
    Route::get('read/{id}', [CategoryController::class, 'read'])->name('category.read');
    Route::post('update/{id}', [CategoryController::class, 'update'])->name('category.update');
    Route::delete('delete/{id}', [CategoryController::class, 'delete'])->name('category.delete');
});

Route::group(['prefix' => 'rating'], function () {
    Route::get('index', [RatingController::class, 'index'])->name('rating.index');
    Route::middleware('auth:sanctum')->post('create', [RatingController::class, 'create'])->name('rating.create');
    Route::put('read/{id}', [RatingController::class, 'read'])->name('rating.read');
    Route::delete('delete/{id}', [RatingController::class, 'delete'])->name('rating.delete');
});

Route::group(['prefix' => 'followers'], function () {
    Route::post('create', [FollowerController::class, 'create'])->name('followe.create');
    Route::get('read/{id}', [FollowerController::class, 'read'])->name('followe.read');
    Route::put('read/{id}', [FollowerController::class, 'read'])->name('followe.read');
    Route::delete('delete/{id}', [FollowerController::class, 'delete'])->name('followe.delete');
});


Route::prefix('subscription')->group(function () {
    Route::get('index', [SubscriptionController::class, 'index'])->name('subscription.index');
    Route::middleware('auth:sanctum')->group(function () {
        Route::post('create', [SubscriptionController::class, 'create'])->name('subscription.create');
        Route::get('read', [SubscriptionController::class, 'read'])->name('subscription.read');
    });
});

Route::prefix('subscriptions')->group(function () {
    Route::middleware('auth:sanctum')->group(function () {
        Route::get('current', [SubscriptionController::class, 'current'])->name('subscriptions.current');
        Route::post('change-plan', [SubscriptionController::class, 'changePlan'])->name('subscriptions.change-plan');
    });
});

Route::prefix('payments')->group(function () {
    Route::middleware('auth:sanctum')->group(function () {
        Route::get('invoices', [InvoiceController::class, 'index'])->name('payments.invoices');
        Route::get('invoices/{id}', [InvoiceController::class, 'show'])->name('payments.invoice.show');
    });
});

Route::middleware(['auth:sanctum', 'reset.deactivation'])->prefix('tickets')->group(function () {
    Route::post('generate', [TicketController::class, 'generate'])->name('tickets.generate');
    Route::get('generate-qr-code/{id}', [TicketController::class, 'generateQRCodes'])->name('tickets.generateQRCodes');
    Route::post('scan', [TicketController::class, 'scan'])->name('tickets.scan');
    Route::get('buy', [TicketController::class, 'assignUserTickets'])->name('tickets.buy');

    // New purchase flow routes
    Route::get('event/{eventId}', [TicketController::class, 'getEventTickets'])->name('tickets.event');
    Route::post('purchase', [TicketController::class, 'purchaseTickets'])->name('tickets.purchase');
    Route::post('confirm-purchase', [TicketController::class, 'confirmPurchase'])->name('tickets.confirm-purchase');
    Route::get('my-purchases', [TicketController::class, 'getUserPurchases'])->name('tickets.my-purchases');
    Route::post('request-refund', [TicketController::class, 'requestRefund'])->name('tickets.request-refund');
});

Route::middleware('auth:sanctum')->prefix('tiers')->group(function () {
    Route::get('read/{id}', [TierController::class, 'read'])->name('tickets.read/{id}');
    Route::post('create', [TierController::class, 'create'])->name('tickets.create');
    Route::put('update/{id}', [TierController::class, 'update'])->name('tickets.update');
    Route::delete('delete/{id}', [TierController::class, 'delete'])->name('tickets.delete');
});

Route::middleware('auth:sanctum')->prefix('sponsors')->group(function () {
    Route::get('read', [SponsorController::class, 'read'])->name('sponsors.read/{id}');
    Route::post('create', [SponsorController::class, 'create'])->name('sponsors.create');
    Route::put('update/{id}', [SponsorController::class, 'update'])->name('sponsors.update');
    Route::delete('delete/{id}', [SponsorController::class, 'delete'])->name('sponsors.delete');
});

Route::middleware('auth:sanctum')->prefix('retailers')->group(function () {
    Route::get('read', [RetailerController::class, 'read'])->name('retailers.read/{id}');
    Route::post('create', [RetailerController::class, 'create'])->name('retailers.create');
    Route::put('update/{id}', [RetailerController::class, 'update'])->name('retailers.update');
    Route::delete('delete/{id}', [RetailerController::class, 'delete'])->name('retailers.delete');
});

Route::middleware('auth:sanctum')->prefix('verification')->group(function () {
    Route::get('request', [VerificationController::class, 'request'])->name('verification.request');
    Route::get('verify', [VerificationController::class, 'adminVerify'])->name('verification.verify');
    Route::get('check-status', [VerificationController::class, 'checkStatus'])->name('verification.check-status');
});

Route::middleware(['auth:sanctum'])->prefix('roles')->group(function () {
    Route::get('/', [RoleController::class, 'index'])->name('roles.index');
    Route::get('/{id}', [RoleController::class, 'show'])->name('roles.show');
    Route::get('/{id}/users', [RoleController::class, 'users'])->name('roles.users');
    Route::post('/', [RoleController::class, 'store'])->name('roles.store');
    Route::put('/{id}', [RoleController::class, 'update'])->name('roles.update');
    Route::delete('/{id}', [RoleController::class, 'destroy'])->name('roles.destroy');
    Route::post('/{id}/permissions', [RoleController::class, 'assignPermissions'])->name('roles.permissions.assign');
    Route::get('/{id}/permissions', [RoleController::class, 'getRolePermissions'])->name('roles.permissions.get');

});

Route::middleware(['auth:sanctum'])->prefix('permissions')->group(function () {
    Route::get('/', [PermissionController::class, 'index'])->name('permissions.index');
    Route::get('/categories', [PermissionController::class, 'getByCategory'])->name('permissions.categories');
    Route::get('/{id}', [PermissionController::class, 'show'])->name('permissions.show');
    Route::get('/{id}/roles', [PermissionController::class, 'roles'])->name('permissions.roles');
    Route::post('/', [PermissionController::class, 'store'])->name('permissions.store');
    Route::put('/{id}', [PermissionController::class, 'update'])->name('permissions.update');
    Route::delete('/{id}', [PermissionController::class, 'destroy'])->name('permissions.destroy');
    Route::post('/{id}/roles', [PermissionController::class, 'assignToRoles'])->name('permissions.roles.assign');
});


Route::prefix('metadata')->group(function () {
    Route::post('location', [MetadataController::class, 'getLocationDetails'])->name('verification.location');
});

Route::prefix('blog')->group(function(){
    Route::get('tweets', [TwitterController::class, 'fetchTweetsByHashtag'])->name('blog.twitter');
    Route::get('articles', [ArticleController::class, 'index'])->name('blog.articles');
    Route::get('articles/{id}', [ArticleController::class, 'show'])->name('blog.article');
    Route::middleware('auth:sanctum')->post('articles/create', [ArticleController::class, 'store'])->name('blog.articles.category');
    Route::get('articles/search', [ArticleController::class, 'searchArticles'])->name('blog.articles.search');
    Route::middleware('auth:sanctum')->post('articles/{id}', [ArticleController::class, 'update'])->name('blog.articles.update');
    Route::middleware('auth:sanctum')->delete('articles/delete/{id}', [ArticleController::class, 'destroy'])->name('blog.articles.delete');
});

Route::middleware('auth:sanctum')->prefix('services')->group(function () {
    Route::get('get', [ServicesController::class, 'index'])->name('services.get');
    Route::post('create', [ServicesController::class, 'store'])->name('services.create');
    Route::get('read/{id}', [ServicesController::class, 'show'])->name('services.read');
    Route::put('update/{id}', [ServicesController::class, 'update'])->name('services.update');
    Route::delete('delete/{id}', [ServicesController::class, 'destroy'])->name('services.delete');
});

Route::prefix('users')->group(function () {
    Route::get('/', [UserController::class, 'index']);
    Route::get('/stats', [UserController::class, 'stats']);
    Route::put('/{id}/status', [UserController::class, 'updateStatus']);
    Route::delete('delete/{id}', [UserController::class, 'destroy']);
});

Route::middleware(['auth:sanctum'])->group(function () {
    Route::prefix('analytics')->group(function () {
        Route::get('/stats', [DashboardController::class, 'getAnalyticsStats']);
        Route::get('/events', [DashboardController::class, 'getTopEvents']);
        Route::get('/revenue', [DashboardController::class, 'getRevenueAnalytics']);
        Route::get('/attendees', [DashboardController::class, 'getAttendeesAnalytics']);
    });

    Route::prefix('reports')->group(function () {
        Route::get('/stats', [DashboardController::class, 'getReportsStats']);
        Route::post('/generate', [DashboardController::class, 'generateReport']);
        Route::get('/export/{reportId}', [DashboardController::class, 'exportReport']);
    });

    Route::prefix('payments')->group(function () {
        Route::get('/stats', [PaymentController::class, 'getPaymentStats']);
        Route::post('/refund/{transactionId}', [PaymentController::class, 'processRefund']);
        Route::get('/receipt/{transactionId}', [PaymentController::class, 'downloadReceipt']);
    });

    Route::prefix('dashboard')->group(function () {
        Route::get('/admin/stats', [DashboardController::class, 'getAdminStats']);
        Route::get('/admin/activities', [DashboardController::class, 'getAdminActivities']);
        Route::get('/admin/revenue-chart', [DashboardController::class, 'getAdminRevenueChart']);
        Route::get('/admin/user-growth-chart', [DashboardController::class, 'getAdminUserGrowthChart']);

        Route::get('/host/stats', [DashboardController::class, 'getHostStats']);
        Route::get('/host/activities', [DashboardController::class, 'getHostActivities']);
        Route::get('/host/revenue-chart', [DashboardController::class, 'getHostRevenueChart']);
        Route::get('/host/ticket-sales-chart', [DashboardController::class, 'getHostTicketSalesChart']);
    });
});
