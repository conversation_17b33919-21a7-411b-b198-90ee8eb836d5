<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $user = User::updateOrCreate([
            'name'=> 'EventaHub Administrator',
            'email' => '<EMAIL>',
            'password'=> bcrypt('password')
        ]);

        // Make sure roles exist before assigning them
        $user->assignRole(['user', 'host', 'admin', 'vendor']);
    }
}
