<?php

namespace Database\Seeders;

use App\Models\Plan;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class PlansSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Plan::truncate();
        $plans = [
            [
                'name' => 'Trial',
                'limit' => 20,
                'price' => 0.00,
                'attributes' => [
                    'duration' => '7 days',
                    'custom_branding' => false,
                    'featured_events' => false,
                    'analytics' => false,
                    'support' => 'basic',
                    'sms_notifications' => false,
                    'multiple_organizers' => false,
                    'ticket_scanning' => false,
                    'payment_gateways' => ['paychangu'],
                    'event_page_customization' => false,
                    'vendor_services_limit' => 1,
                    'vendor_bookings_limit' => 5,
                    'vendor_featured' => false,
                    'vendor_analytics' => false,
                    'vendor_support' => 'basic',
                    'vendor_payment_methods' => ['mpamba'],
                ],
            ],
            [
                'name' => 'Basic',
                'limit' => 100,
                'price' => 5000.00,
                'attributes' => [
                    'duration' => '1 month',
                    'custom_branding' => false,
                    'featured_events' => false,
                    'analytics' => true,
                    'support' => 'standard',
                    'sms_notifications' => true,
                    'multiple_organizers' => false,
                    'ticket_scanning' => true,
                    'payment_gateways' => ['paychangu'],
                    'event_page_customization' => true,
                    'vendor_services_limit' => 5,
                    'vendor_bookings_limit' => 50,
                    'vendor_featured' => false,
                    'vendor_analytics' => true,
                    'vendor_support' => 'standard',
                    'vendor_payment_methods' => ['mpamba', 'airtel_money'],
                ],
            ],
            [
                'name' => 'Professional',
                'limit' => 1000,
                'price' => 15000.00,
                'attributes' => [
                    'duration' => '1 month',
                    'custom_branding' => true,
                    'featured_events' => true,
                    'analytics' => true,
                    'support' => 'priority',
                    'sms_notifications' => true,
                    'multiple_organizers' => true,
                    'ticket_scanning' => true,
                    'payment_gateways' => ['paychangu'],
                    'event_page_customization' => true,
                    'vendor_services_limit' => 15,
                    'vendor_bookings_limit' => 200,
                    'vendor_featured' => true,
                    'vendor_analytics' => true,
                    'vendor_support' => 'priority',
                    'vendor_payment_methods' => ['mpamba', 'airtel_money', 'visa'],
                ],
            ],
            [
                'name' => 'Premium',
                'limit' => 1000000,
                'price' => 30000.00,
                'attributes' => [
                    'duration' => '1 year',
                    'custom_branding' => true,
                    'featured_events' => true,
                    'analytics' => true,
                    'support' => '24/7 priority',
                    'sms_notifications' => true,
                    'multiple_organizers' => true,
                    'ticket_scanning' => true,
                    'payment_gateways' => ['paychangu'],
                    'event_page_customization' => true,
                    'vendor_services_limit' => -1,
                    'vendor_bookings_limit' => -1,
                    'vendor_featured' => true,
                    'vendor_analytics' => true,
                    'vendor_support' => '24/7 priority',
                    'vendor_payment_methods' => ['mpamba', 'airtel_money', 'visa'],
                ],
            ],
        ];

        foreach ($plans as $plan) {
            Plan::create([
                'name' => $plan['name'],
                'tickets_limit' => $plan['limit'],
                'price' => $plan['price'],
                'currency' => 'MWK',
                'attributes' => $plan['attributes'],
            ]);
        }
    }
}
