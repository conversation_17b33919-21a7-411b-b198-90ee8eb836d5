<?php

namespace Database\Seeders;

use App\Models\PaymentGateway;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class PaymentGatewaySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $gateways = [
            [
                'name' => 'PayChangu',
                'slug' => 'paychangu',
                'description' => 'PayChangu is a secure payment gateway supporting Mpamba, Airtel Money, and bank Visa payments.',
                'is_active' => true,
                'config' => [
                    'api_key' => env('PAYCHANGU_API_KEY', 'test_api_key'),
                    'secret_key' => env('PAYCHANGU_SECRET_KEY', 'test_secret_key'),
                    'merchant_id' => env('PAYCHANGU_MERCHANT_ID', 'test_merchant_id'),
                    'payment_methods' => [
                        'mpamba' => [
                            'name' => 'TNM Mpamba',
                            'logo' => 'mpamba-logo.png',
                            'is_active' => true
                        ],
                        'airtel_money' => [
                            'name' => 'Airtel Money',
                            'logo' => 'airtel-money-logo.png',
                            'is_active' => true
                        ],
                        'visa' => [
                            'name' => 'Bank Visa',
                            'logo' => 'visa-logo.png',
                            'is_active' => true
                        ]
                    ]
                ],
                'logo' => 'paychangu-logo.png',
                'supported_currencies' => ['MWK', 'USD', 'ZAR'],
                'test_mode' => true
            ]
        ];

        foreach ($gateways as $gateway) {
            PaymentGateway::updateOrCreate(
                ['slug' => $gateway['slug']],
                $gateway
            );
        }
    }
}
