<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // First create roles and permissions
        $this->call(RolesSeeder::class);
        $this->call(PermissionsSeeder::class);

        // Then create users (so they can be assigned roles)
        $this->call(UserSeeder::class);

        // Then create other data
        $this->call(TypeSeeder::class);
        $this->call(CategorySeeder::class);
        $this->call(PlansSeeder::class);
        $this->call(StatusSeeder::class);
        $this->call(SettingsSeeder::class);
        $this->call(LevenshteinSeeder::class);
        $this->call(CurrencySeeder::class);
        $this->call(ServicesSeeder::class);
        $this->call(PaymentGatewaySeeder::class);
        $this->call(VendorPaymentMethodSeeder::class);
    }
}
