<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Test</title>
    <script src="https://cdn.jsdelivr.net/npm/pusher-js@7.0.3/dist/web/pusher.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        #messages {
            border: 1px solid #ccc;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            margin-bottom: 20px;
        }
        .message {
            margin-bottom: 10px;
            padding: 10px;
            background-color: #f5f5f5;
            border-radius: 5px;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <h1>WebSocket Test</h1>
    <div id="messages"></div>
    <button id="testButton">Send Test Event</button>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const messagesContainer = document.getElementById('messages');
            const testButton = document.getElementById('testButton');

            // Initialize Pusher
            const pusher = new Pusher('c8ddef6745c937660552', {
                wsHost: '127.0.0.1',
                wsPort: 6001,
                forceTLS: false,
                disableStats: true,
                enabledTransports: ['ws', 'wss'],
                cluster: 'us2'
            });

            // Subscribe to the test channel
            const channel = pusher.subscribe('test-channel');

            // Listen for the test event
            channel.bind('test-event', function(data) {
                const messageElement = document.createElement('div');
                messageElement.className = 'message';
                messageElement.innerHTML = `
                    <strong>Message:</strong> ${data.message}<br>
                    <strong>Time:</strong> ${data.time}
                `;
                messagesContainer.appendChild(messageElement);
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
            });

            // Add connection status messages
            pusher.connection.bind('connected', function() {
                const messageElement = document.createElement('div');
                messageElement.className = 'message';
                messageElement.innerHTML = '<strong>Status:</strong> Connected to WebSocket server';
                messagesContainer.appendChild(messageElement);
            });

            pusher.connection.bind('disconnected', function() {
                const messageElement = document.createElement('div');
                messageElement.className = 'message';
                messageElement.innerHTML = '<strong>Status:</strong> Disconnected from WebSocket server';
                messagesContainer.appendChild(messageElement);
            });

            pusher.connection.bind('error', function(error) {
                const messageElement = document.createElement('div');
                messageElement.className = 'message';
                messageElement.innerHTML = `<strong>Error:</strong> ${JSON.stringify(error)}`;
                messagesContainer.appendChild(messageElement);
            });

            // Add click event to the test button
            testButton.addEventListener('click', function() {
                fetch('/api/test-websockets')
                    .then(response => response.json())
                    .then(data => {
                        console.log('Test event sent:', data);
                    })
                    .catch(error => {
                        console.error('Error sending test event:', error);
                    });
            });
        });
    </script>
</body>
</html>
