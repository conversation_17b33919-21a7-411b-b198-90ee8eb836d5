<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Event Tickets - {{ $purchaseReference }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #007bff;
            margin: 0;
        }
        .purchase-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 30px;
        }
        .ticket {
            margin-bottom: 40px;
            page-break-inside: avoid;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        .ticket-header {
            background: #007bff;
            color: white;
            padding: 15px;
            text-align: center;
        }
        .ticket-body {
            padding: 20px;
            text-align: center;
        }
        .ticket-image {
            max-width: 100%;
            height: auto;
            border-radius: 5px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .attendee-info {
            margin-top: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 12px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 20px;
        }
        @media print {
            .ticket {
                page-break-after: always;
            }
            .ticket:last-child {
                page-break-after: auto;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>EventaHub Tickets</h1>
        <p>Purchase Reference: {{ $purchaseReference }}</p>
    </div>

    @if(isset($purchaseInfo))
    <div class="purchase-info">
        <h3>Purchase Information</h3>
        <p><strong>Event:</strong> {{ $purchaseInfo['event_name'] ?? 'Event' }}</p>
        <p><strong>Purchase Date:</strong> {{ now()->format('M d, Y H:i') }}</p>
        <p><strong>Total Tickets:</strong> {{ count($ticketImages) }}</p>
    </div>
    @endif

    @foreach($ticketImages as $index => $ticketData)
    <div class="ticket">
        <div class="ticket-header">
            <h2>{{ $ticketData['ticket_name'] ?? 'Event Ticket' }}</h2>
            <p>Ticket #{{ $index + 1 }}</p>
        </div>
        <div class="ticket-body">
            <img src="{{ $ticketData['image'] }}" alt="Ticket {{ $index + 1 }}" class="ticket-image">
            <div class="attendee-info">
                <strong>Attendee:</strong> {{ $ticketData['attendee_name'] ?? 'Guest' }}
            </div>
        </div>
    </div>
    @endforeach

    <div class="footer">
        <p>Generated on {{ now()->format('F d, Y \a\t H:i') }}</p>
        <p>Please present this ticket (QR code) at the event entrance.</p>
        <p>For support, contact <NAME_EMAIL></p>
    </div>
</body>
</html>
