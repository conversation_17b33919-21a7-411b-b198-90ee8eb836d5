<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ $subject ?? 'EventaHub Notification' }}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background-color: #ffffff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .content {
            margin-bottom: 30px;
        }
        .greeting {
            font-size: 18px;
            color: #2c3e50;
            margin-bottom: 20px;
        }
        .message {
            font-size: 16px;
            line-height: 1.8;
            margin-bottom: 20px;
        }
        .footer {
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
            font-size: 14px;
            color: #666;
        }
        .button {
            display: inline-block;
            padding: 12px 24px;
            background-color: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 0;
        }
        .button:hover {
            background-color: #2980b9;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">EventaHub</div>
            <p>Your Event Management Platform</p>
        </div>

        <div class="content">
            <div class="greeting">Hello {{ $user->name ?? 'There' }}!</div>

            <div class="message">
                {{ $message }}
            </div>

            @if(isset($data['download_link']))
                <p style="text-align: center;">
                    <a href="{{ $data['download_link'] }}" class="button">Download</a>
                </p>
            @endif

            @if(isset($data) && count($data) > 0)
                <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                    <h4 style="margin-top: 0;">Additional Information:</h4>
                    @foreach($data as $key => $value)
                        @if(!in_array($key, ['download_link']))
                            <p><strong>{{ ucfirst(str_replace('_', ' ', $key)) }}:</strong>
                            @if(is_array($value))
                                {{ implode(', ', $value) }}
                            @else
                                {{ $value }}
                            @endif
                            </p>
                        @endif
                    @endforeach
                </div>
            @endif
        </div>

        <div class="footer">
            <p>&copy; {{ date('Y') }} EventaHub. All rights reserved.</p>
            <p>You're receiving this email because you have an account with EventaHub.</p>
        </div>
    </div>
</body>
</html>
