<?php

namespace App\Console\Commands;

use App\Models\Event;
use App\Models\Tier;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Ladumor\OneSignal\OneSignal;

class SendEventNotification extends Command
{
    protected $signature = 'notifications:send';

    protected $description = 'Send notifications to event attendees 30 minutes before event starts';

    public function handle()
    {
        $currentTime = Carbon::now();
        $thirtyMinutesLater = $currentTime->copy()->addMinutes(30);

        $events = Event::where('start', '>', $currentTime)
            ->where('start', '<=', $thirtyMinutesLater)
            ->get();

        foreach ($events as $event) {
            $attendees = $event->attendees()->pluck('email');

            foreach ($attendees as $attendeeEmail) {
                $this->sendNotification($attendeeEmail, $event);
            }
        }

        $this->info('Notifications sent successfully.');
    }

    private function sendNotification($email, $event)
    {

        Tier::create([
            'event_id' => 1,
            'name' => 'Standard',
            'price' => 2000
        ]);
        // $fields = [
        //     'title' => 'Event Notification'
        // ];
        // $notificationMsg = '';
        // Replace this with your actual notification sending logic
        // For example, sending an email
        // Mail::to($email)->send(new EventNotification($event));
        // $this->info("Notification sent to $email for event: $event->title");
        // OneSignal::sendPush($fields, 'Something');
    }
}
