<?php

namespace App\Console\Commands;

use App\Models\Subscription;
use Illuminate\Console\Command;

class RemoveExpiredSubscriptions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:remove-expired-subscriptions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $expiredSubscriptions = Subscription::where('end_date', '<', now())->get();

        foreach ($expiredSubscriptions as $subscription) {
            $subscription->delete();
            $user = $user::find($subscription->user_id);
            $user->assignRole('user');
        }

        $this->info('Expired subscriptions removed successfully!');
    }
}
