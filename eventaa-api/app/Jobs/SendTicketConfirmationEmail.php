<?php

namespace App\Jobs;

use App\Models\User;
use App\Notifications\UserNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class SendTicketConfirmationEmail implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $user;
    protected $purchases;
    protected $generatedTickets;

    /**
     * Create a new job instance.
     */
    public function __construct(User $user, array $purchases, array $generatedTickets)
    {
        $this->user = $user;
        $this->purchases = $purchases;
        $this->generatedTickets = $generatedTickets;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::info('Sending ticket confirmation email', [
                'user_id' => $this->user->id,
                'user_email' => $this->user->email,
                'ticket_count' => count($this->generatedTickets)
            ]);

            // Send notification through the existing notification system
            $eventName = $this->purchases[0]->event->name ?? 'Event';
            $ticketCount = count($this->generatedTickets);

            $notificationData = [
                'greeting' => 'Hello ' . $this->user->name . '!',
                'title' => 'Ticket Purchase Confirmed',
                'line' => "Your purchase of {$ticketCount} ticket(s) for '{$eventName}' has been confirmed. You can download your tickets from your account.",
                'action' => 'View My Tickets',
                'actionURL' => '/tickets/my-purchases'
            ];

            $this->user->notify(new UserNotification($notificationData));

            // TODO: Implement actual email with PDF tickets attachment
            // You can implement a dedicated Mailable class here for better email formatting

            Log::info('Ticket confirmation email sent successfully', [
                'user_id' => $this->user->id,
                'user_email' => $this->user->email
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to send ticket confirmation email', [
                'user_id' => $this->user->id,
                'user_email' => $this->user->email,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Ticket confirmation email job failed permanently', [
            'user_id' => $this->user->id,
            'user_email' => $this->user->email,
            'error' => $exception->getMessage()
        ]);
    }
}
