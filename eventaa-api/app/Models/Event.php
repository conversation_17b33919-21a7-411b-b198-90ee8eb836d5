<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Event extends Model
{
    use HasFactory;

    protected $fillable = ['title', 'description', 'user_id', 'category_id', 'location', 'slug', 'date', 'type_id', 'visibility_id', 'latitude', 'longitude', 'cover_art', 'start', 'end'];

    public function user(): BelongsTo
    {

        return $this->belongsTo(User::class);
    }

    public function tiers(): HasMany
    {

        return $this->hasMany(Tier::class);
    }
    public function type(): BelongsTo
    {

        return $this->belongsTo(Type::class);
    }

    public function statuses(): HasMany
    {

        return $this->hasMany(EventStatus::class);
    }

    public function category(): BelongsTo
    {

        return $this->belongsTo(Category::class);
    }

    public function visibility(): BelongsTo
    {

        return $this->belongsTo(Visibility::class);
    }


    public function ratings(): HasMany
    {

        return $this->hasMany(Rating::class);
    }

    public function attendees()
    {
        return $this->hasMany(EventAttendee::class);
    }


    public function likes(): HasMany
    {
        return $this->hasMany(EventLike::class);
    }

    public function shares(): HasMany
    {
        return $this->hasMany(EventShare::class);
    }

    public function notification(): HasMany
    {
        return $this->hasMany(EventNotification::class);
    }

    public function sponsors(): HasMany
    {
        return $this->hasMany(EventSponsor::class);
    }

    public function meeting_link(): HasOne
    {
        return $this->hasOne(MeetingLink::class);
    }

    public function tickets(): HasOne
    {
        return $this->hasOne(Ticket::class);
    }

}
