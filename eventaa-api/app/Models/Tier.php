<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Tier extends Model
{
    use HasFactory;

    protected $fillable = ['event_id', 'name', 'banner', 'price', 'description', 'is_refundable', 'seat'];

    public function event() : BelongsTo{
        return $this->belongsTo(Event::class);
    }
}
