<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Subscription extends Model
{
    use HasFactory;

    protected $fillable = ['user_id', 'plan_id', 'start_date', 'end_date'];

    public function user(): BelongsT<PERSON>{

        return $this->belongsTo(User::class);
    }

    public function plan(): BelongsTo{
        return $this->BelongsTo(Plan::class);
    }
}
