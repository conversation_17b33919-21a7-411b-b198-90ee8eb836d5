<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Plan extends Model
{
    use HasFactory;

    protected $fillable = ['name', 'tickets_limit', 'price', 'currency', 'attributes'];

    protected $casts = [
        'price' => 'decimal:2',
        'attributes' => 'array'
    ];

    /**
     * Get a specific attribute from the attributes array
     */
    public function getAttribute($key)
    {
        // First check if the attribute exists in the model
        if (array_key_exists($key, $this->attributes)) {
            return parent::getAttribute($key);
        }

        // If not, check if it's in the attributes JSON field
        $attributes = $this->attributes['attributes'] ?? null;
        if ($attributes && is_array($attributes) && array_key_exists($key, $attributes)) {
            return $attributes[$key];
        }

        return parent::getAttribute($key);
    }

    /**
     * Get all subscriptions for this plan
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class);
    }
}
