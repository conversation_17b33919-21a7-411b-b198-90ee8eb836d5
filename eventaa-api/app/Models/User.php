<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use App\Services\RatingService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;



class User extends Authenticatable
{
    use HasRoles, HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'phone',
        'avatar',
        'role',
        'status',
        'last_login',
        'google_id',
        'verification_document',
        'verification_status',
        'verification_notes',
        'last_active_at'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_verified' => 'boolean',
        'verified_at' => 'datetime',
        'email_verified_at' => 'datetime',
        'last_login' => 'datetime',
        'last_active_at' => 'datetime',
        'password' => 'hashed',
    ];

    protected $appends = [
        'joined_date',
    ];

    public function getJoinedDateAttribute()
    {
        return $this->created_at->toDateString();
    }

    public function followers(): HasMany
    {
        return $this->hasMany(Follower::class, 'followee_id');
    }

    public function following(): HasMany
    {
        return $this->hasMany(Follower::class, 'follower_id');
    }

    public function profile(): HasOne
    {
        return $this->hasOne(Profile::class, 'user_id');
    }

    public function interests(): BelongsToMany
    {
        return $this->belongsToMany(Category::class, 'interests', 'user_id', 'category_id');
    }

    public function subscription()
    {
        return $this->hasOne(Subscription::class)
            ->where(function ($query) {
                $query->whereNull('end_date')
                    ->orWhere('end_date', '>', now());
            });
    }

    public function events()
    {
        return $this->hasMany(Event::class);
    }

    public function likedEvents()
    {
        return $this->belongsToMany(Event::class, 'event_likes', 'user_id', 'event_id');
    }

    public function likedVendors()
    {
        return $this->belongsToMany(Vendor::class, 'vendor_likes', 'user_id', 'vendor_id');
    }

    public function attendedEvents()
    {
        return $this->belongsToMany(Event::class, 'event_attendees', 'user_id', 'event_id');
    }

    public function ratings()
    {
        return $this->hasManyThrough(Rating::class, Event::class);
    }
    public function sendPasswordResetNotification($token)
    {
        $this->notify(new \App\Notifications\UserResetPasswordNotification($token, $this->email));
    }

    public function isVerified()
    {
        return $this->is_verified;
    }

    public function verificationStatus()
    {
        return $this->verification_status;
    }

    /**
     * Get the user's status.
     *
     * @return HasOne
     */
    public function status(): HasOne
    {
        return $this->hasOne(UserStatus::class);
    }

    /**
     * Get the conversations for the user.
     *
     * @return HasMany
     */
    public function conversations(): HasMany
    {
        return $this->hasMany(Conversation::class);
    }

    /**
     * Get the messages sent by the user.
     *
     * @return HasMany
     */
    public function messages(): HasMany
    {
        return $this->hasMany(Message::class);
    }

    /**
     * Get the user's settings.
     *
     * @return BelongsToMany
     */
    public function settings(): BelongsToMany
    {
        return $this->belongsToMany(Setting::class, 'user_settings');
    }

    /**
     * Get the vendor associated with the user.
     *
     * @return HasOne
     */
    public function vendor(): HasOne
    {
        return $this->hasOne(Vendor::class);
    }

    /**
     * Get the vendor ratings submitted by the user.
     *
     * @return HasMany
     */
    public function vendorRatings(): HasMany
    {
        return $this->hasMany(VendorRating::class);
    }
}
