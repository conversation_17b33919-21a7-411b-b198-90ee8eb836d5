<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Service extends Model
{
    protected $fillable = [
        'name',
        'description',
    ];

    /**
     * Get the vendor services for this service.
     *
     * @return HasMany
     */
    public function vendorServices(): HasMany
    {
        return $this->hasMany(VendorService::class);
    }
}
