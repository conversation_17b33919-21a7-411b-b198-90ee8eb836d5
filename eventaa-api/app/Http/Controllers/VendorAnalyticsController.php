<?php

namespace App\Http\Controllers;

use App\Events\VendorAnalyticsUpdated;
use App\Models\User;
use App\Models\Vendor;
use App\Models\VendorBooking;
use App\Models\VendorRating;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class VendorAnalyticsController extends Controller
{
    /**
     * Get dashboard analytics for a vendor
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getDashboardAnalytics(Request $request): JsonResponse
    {
        $vendor = Vendor::where('user_id', $request->user()->id)->first();

        if (!$vendor) {
            return response()->json(['message' => 'Vendor not found for this user'], 404);
        }

        $period = $request->input('period', '30days');
        $startDate = $this->getStartDateFromPeriod($period);
        $endDate = Carbon::now();

        $bookingsData = $this->getBookingsAnalytics($vendor->id, $startDate, $endDate);
        $revenueData = $this->getRevenueAnalytics($vendor->id, $startDate, $endDate);
        $ratingsData = $this->getRatingsAnalytics($vendor->id, $startDate, $endDate);
        $profileViewsData = $this->getProfileViewsAnalytics($vendor->id, $startDate, $endDate);

        $analytics = [
            'bookings' => $bookingsData,
            'revenue' => $revenueData,
            'ratings' => $ratingsData,
            'profile_views' => $profileViewsData,
            'period' => $period,
            'start_date' => $startDate->toDateString(),
            'end_date' => $endDate->toDateString(),
        ];

        broadcast(new VendorAnalyticsUpdated($vendor->id, $analytics))->toOthers();

        return response()->json($analytics);
    }

    /**
     * Get bookings analytics
     *
     * @param int $vendorId
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @return array
     */
    private function getBookingsAnalytics(int $vendorId, Carbon $startDate, Carbon $endDate): array
    {
        $totalBookings = VendorBooking::where('vendor_id', $vendorId)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();

        $pendingBookings = VendorBooking::where('vendor_id', $vendorId)
            ->where('status', 'pending')
            ->count();

        $previousStartDate = (clone $startDate)->subDays($endDate->diffInDays($startDate));
        $previousEndDate = (clone $startDate)->subDay();
        
        $previousBookings = VendorBooking::where('vendor_id', $vendorId)
            ->whereBetween('created_at', [$previousStartDate, $previousEndDate])
            ->count();

        $growth = $previousBookings > 0 
            ? round((($totalBookings - $previousBookings) / $previousBookings) * 100) 
            : ($totalBookings > 0 ? 100 : 0);

        return [
            'total' => $totalBookings,
            'pending_approval' => $pendingBookings,
            'growth_percentage' => $growth,
        ];
    }

    /**
     * Get revenue analytics
     *
     * @param int $vendorId
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @return array
     */
    private function getRevenueAnalytics(int $vendorId, Carbon $startDate, Carbon $endDate): array
    {
        $totalRevenue = VendorBooking::where('vendor_id', $vendorId)
            ->where('status', 'approved')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->sum('total_price');

        $pendingRevenue = VendorBooking::where('vendor_id', $vendorId)
            ->where('status', 'pending')
            ->sum('total_price');

        $previousStartDate = (clone $startDate)->subDays($endDate->diffInDays($startDate));
        $previousEndDate = (clone $startDate)->subDay();
        
        $previousRevenue = VendorBooking::where('vendor_id', $vendorId)
            ->where('status', 'approved')
            ->whereBetween('created_at', [$previousStartDate, $previousEndDate])
            ->sum('total_price');

        $growth = $previousRevenue > 0 
            ? round((($totalRevenue - $previousRevenue) / $previousRevenue) * 100) 
            : ($totalRevenue > 0 ? 100 : 0);

        return [
            'total' => $totalRevenue,
            'pending' => $pendingRevenue,
            'growth_percentage' => $growth,
        ];
    }

    /**
     * Get ratings analytics
     *
     * @param int $vendorId
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @return array
     */
    private function getRatingsAnalytics(int $vendorId, Carbon $startDate, Carbon $endDate): array
    {
        $averageRating = VendorRating::where('vendor_id', $vendorId)
            ->avg('rating') ?? 0;

        $totalReviews = VendorRating::where('vendor_id', $vendorId)
            ->count();

        $previousStartDate = (clone $startDate)->subDays($endDate->diffInDays($startDate));
        $previousEndDate = (clone $startDate)->subDay();
        
        $previousAverageRating = VendorRating::where('vendor_id', $vendorId)
            ->whereBetween('created_at', [$previousStartDate, $previousEndDate])
            ->avg('rating') ?? 0;

        $growth = $previousAverageRating > 0 
            ? round(($averageRating - $previousAverageRating), 1) 
            : ($averageRating > 0 ? $averageRating : 0);

        return [
            'average' => round($averageRating, 1),
            'total_reviews' => $totalReviews,
            'growth' => $growth,
        ];
    }

    /**
     * Get profile views analytics
     *
     * @param int $vendorId
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @return array
     */
    private function getProfileViewsAnalytics(int $vendorId, Carbon $startDate, Carbon $endDate): array
    {

        $vendor = Vendor::find($vendorId);
        $totalViews = $vendor->views ?? 0;

        $growth = 18;

        return [
            'total' => $totalViews,
            'period' => $endDate->diffInDays($startDate) . ' days',
            'growth_percentage' => $growth,
        ];
    }

    /**
     * Get start date based on period
     *
     * @param string $period
     * @return Carbon
     */
    private function getStartDateFromPeriod(string $period): Carbon
    {
        $now = Carbon::now();
        
        return match ($period) {
            '7days' => $now->copy()->subDays(7),
            '30days' => $now->copy()->subDays(30),
            '90days' => $now->copy()->subDays(90),
            'year' => $now->copy()->subYear(),
            'month' => $now->copy()->subMonth(),
            default => $now->copy()->subDays(30),
        };
    }
}
