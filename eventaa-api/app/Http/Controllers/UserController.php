<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class UserController extends Controller
{
    public function index(Request $request)
    {
        $query = User::query();

        if ($search = $request->input('search')) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%")
                  ->orWhere('id', 'like', "%{$search}%");
            });
        }

        if ($role = $request->input('role')) {
            if ($role !== 'all') {
                $query->where(function ($q) use ($role) {
                    $q->where('role', $role)
                      ->orWhereNull('role');
                });
            }
        }

        if ($status = $request->input('status')) {
            if ($status !== 'all') {
                $query->where(function ($q) use ($status) {
                    $q->where('status', $status)
                      ->orWhereNull('status');
                });
            }
        }

        if ($dateFilter = $request->input('dateFilter')) {
            $now = Carbon::now();
            switch ($dateFilter) {
                case 'today':
                    $query->whereDate('created_at', $now);
                    break;
                case 'week':
                    $query->whereBetween('created_at', [$now->startOfWeek(), $now->endOfWeek()]);
                    break;
                case 'month':
                    $query->whereMonth('created_at', $now->month)
                          ->whereYear('created_at', $now->year);
                    break;
                case 'year':
                    $query->whereYear('created_at', $now->year);
                    break;
            }
        }

        $perPage = $request->input('limit', 10);
        $users = $query->paginate($perPage);

        $users->through(function ($user) {
            $user->role = $user->role ?? 'customer';
            $user->status = $user->status ?? 'pending';
            return $user;
        });

        return response()->json([
            'data' => [
                'users' => $users->items(),
                'total' => $users->total()
            ]
        ]);
    }

    public function stats()
    {
        $now = Carbon::now();
        $lastMonth = Carbon::now()->subMonth();
        $startOfMonth = $now->startOfMonth()->toDateTimeString();
        $lastMonthStart = $lastMonth->startOfMonth()->toDateTimeString();
        $lastMonthEnd = $lastMonth->endOfMonth()->toDateTimeString();

        $currentStats = DB::table('users')
            ->select([
                DB::raw('COUNT(*) as total'),
                DB::raw("COUNT(CASE WHEN created_at >= '{$startOfMonth}' THEN 1 END) as new_users"),
                DB::raw('COUNT(CASE WHEN COALESCE(role, "") = "vendor" THEN 1 END) as vendors'),
                DB::raw('COUNT(CASE WHEN COALESCE(status, "") = "inactive" THEN 1 END) as inactive_users')
            ])
            ->first();

        $lastMonthStats = DB::table('users')
            ->select([
                DB::raw('COUNT(*) as total'),
                DB::raw("COUNT(CASE WHEN created_at >= '{$lastMonthStart}' AND created_at <= '{$lastMonthEnd}' THEN 1 END) as new_users"),
                DB::raw('COUNT(CASE WHEN COALESCE(role, "") = "vendor" THEN 1 END) as vendors'),
                DB::raw('COUNT(CASE WHEN COALESCE(status, "") = "inactive" THEN 1 END) as inactive_users')
            ])
            ->where('created_at', '<', $startOfMonth)
            ->first();

        $calculateGrowth = function($current, $previous) {
            if ($previous == 0) return 0;
            return round((($current - $previous) / $previous) * 100, 1);
        };

        return response()->json([
            'data' => [
                'totalUsers' => $currentStats->total,
                'usersGrowth' => $calculateGrowth($currentStats->total, $lastMonthStats->total),
                'newUsers' => $currentStats->new_users,
                'newUsersGrowth' => $calculateGrowth($currentStats->new_users, $lastMonthStats->new_users),
                'vendors' => $currentStats->vendors,
                'vendorsGrowth' => $calculateGrowth($currentStats->vendors, $lastMonthStats->vendors),
                'inactiveUsers' => $currentStats->inactive_users,
                'inactiveGrowth' => $calculateGrowth($currentStats->inactive_users, $lastMonthStats->inactive_users)
            ]
        ]);
    }

    public function updateStatus(Request $request, $id)
    {
        $user = User::find($id);

        if (!$user) {
            return response()->json(['message' => 'User not found'], 404);
        }

        $request->validate([
            'status' => 'required|in:active,inactive,suspended,pending'
        ]);

        $user->update([
            'status' => $request->status
        ]);

        return response()->json([
            'message' => 'User status updated successfully',
            'data' => $user
        ]);
    }

    public function destroy($id)
    {
        $user = User::find($id);

        if (!$user) {
            return response()->json(['message' => 'User not found'], 404);
        }

        $user->delete();

        return response()->json([
            'message' => 'User deleted successfully'
        ]);
    }
}
