<?php

namespace App\Http\Controllers;

use App\Models\PaymentGateway;
use App\Models\PaymentTransaction;
use App\Models\VendorBooking;
use App\Models\VendorPaymentMethod;
use App\Services\PayChanguService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class PaymentController extends Controller
{
    /**
     * Initialize a payment for a booking
     */
    public function initializeBookingPayment(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'booking_id' => 'required|integer|exists:vendor_bookings,id',
            'payment_method_id' => 'required|integer|exists:vendor_payment_methods,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $booking = VendorBooking::findOrFail($request->booking_id);
        $paymentMethod = VendorPaymentMethod::with('paymentGateway')->findOrFail($request->payment_method_id);

        if ($booking->vendor_id !== $paymentMethod->vendor_id) {
            return response()->json([
                'message' => 'The payment method does not belong to the vendor of this booking'
            ], 403);
        }

        if ($booking->is_paid) {
            return response()->json([
                'message' => 'This booking is already paid'
            ], 400);
        }

        if (!$paymentMethod->paymentGateway->is_active) {
            return response()->json([
                'message' => 'The selected payment gateway is currently unavailable'
            ], 400);
        }

        if ($paymentMethod->paymentGateway->slug === 'paychangu') {
            $payChanguService = new PayChanguService($paymentMethod->paymentGateway);

            $callbackUrl = route('payments.verify');

            $result = $payChanguService->processBookingPayment(
                $booking,
                $paymentMethod,
                $request->user()->id,
                $callbackUrl
            );

            if ($result['status']) {
                return response()->json([
                    'message' => 'Payment initialized successfully',
                    'data' => $result['data']
                ]);
            }

            return response()->json([
                'message' => $result['message'],
                'data' => $result['data'] ?? null
            ], 400);
        }

        return response()->json([
            'message' => 'Unsupported payment gateway'
        ], 400);
    }

    /**
     * Verify a payment
     */
    public function verifyPayment(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'reference' => 'required|string|exists:payment_transactions,transaction_id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $transaction = PaymentTransaction::where('transaction_id', $request->reference)
            ->with(['paymentMethod.paymentGateway', 'bookable'])
            ->first();

        if (!$transaction) {
            return response()->json([
                'message' => 'Transaction not found'
            ], 404);
        }

        if ($transaction->paymentMethod->paymentGateway->slug === 'paychangu') {
            $payChanguService = new PayChanguService($transaction->paymentMethod->paymentGateway);

            $result = $payChanguService->verifyPayment($transaction->transaction_id);

            if ($result['status'] && isset($result['data']['status']) && $result['data']['status'] === 'success') {
                $transaction->status = 'completed';
                $transaction->payment_date = now();
                $transaction->gateway_response = array_merge(
                    $transaction->gateway_response ?? [],
                    ['verification' => $result]
                );
                $transaction->save();

                if ($transaction->bookable_type === VendorBooking::class) {
                    $booking = $transaction->bookable;
                    $booking->is_paid = true;
                    $booking->save();
                }

                return response()->json([
                    'message' => 'Payment verified successfully',
                    'data' => [
                        'transaction' => $transaction,
                        'booking' => $transaction->bookable
                    ]
                ]);
            }

            $transaction->status = 'failed';
            $transaction->gateway_response = array_merge(
                $transaction->gateway_response ?? [],
                ['verification' => $result]
            );
            $transaction->save();

            return response()->json([
                'message' => 'Payment verification failed',
                'data' => $result['data'] ?? null
            ], 400);
        }

        return response()->json([
            'message' => 'Unsupported payment gateway'
        ], 400);
    }

    /**
     * Get payment methods for a vendor
     *
     * @param Request $request
     * @param int $vendor_id
     * @return JsonResponse
     */
    public function getVendorPaymentMethods(Request $request, $vendor_id): JsonResponse
    {
        $validator = Validator::make(['vendor_id' => $vendor_id], [
            'vendor_id' => 'required|integer|exists:vendors,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $paymentMethods = VendorPaymentMethod::where('vendor_id', $vendor_id)
            ->with('paymentGateway')
            ->where('status', 'active')
            ->get();

        return response()->json([
            'data' => $paymentMethods
        ]);
    }

    /**
     * Get payment transactions for a user
     */
    public function getUserTransactions(Request $request): JsonResponse
    {
        $transactions = PaymentTransaction::where('user_id', $request->user()->id)
            ->with(['paymentMethod.paymentGateway', 'bookable'])
            ->orderBy('created_at', 'desc')
            ->paginate($request->per_page ?? 10);

        return response()->json($transactions);
    }

    /**
     * Get payment transactions for a vendor
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getVendorTransactions(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'vendor_id' => 'required|integer|exists:vendors,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $paymentMethodIds = VendorPaymentMethod::where('vendor_id', $request->vendor_id)
            ->pluck('id');

        $transactions = PaymentTransaction::whereIn('vendor_payment_method_id', $paymentMethodIds)
            ->with(['paymentMethod.paymentGateway', 'bookable', 'user'])
            ->orderBy('created_at', 'desc')
            ->paginate($request->per_page ?? 10);

        return response()->json($transactions);
    }

    /**
     * Handle payment webhook
     */
    public function handleWebhook(Request $request): JsonResponse
    {
        $request->header('X-PayChangu-Signature');

        $data = $request->all();

        if (isset($data['reference'])) {
            $transaction = PaymentTransaction::where('transaction_id', $data['reference'])->first();

            if ($transaction) {
                $transaction->status = $data['status'] === 'success' ? 'completed' : 'failed';
                $transaction->payment_date = now();
                $transaction->gateway_response = array_merge(
                    $transaction->gateway_response ?? [],
                    ['webhook' => $data]
                );
                $transaction->save();

                if ($data['status'] === 'success' && $transaction->bookable_type === VendorBooking::class) {
                    $booking = $transaction->bookable;
                    $booking->is_paid = true;
                    $booking->save();
                }
            }
        }

        return response()->json(['status' => 'success']);
    }

    /**
     * Get payment statistics
     */
    public function getPaymentStats(Request $request): JsonResponse
    {
        try {
            $period = $request->query('period', '30d');
            $days = match($period) {
                '7d' => 7,
                '30d' => 30,
                '90d' => 90,
                'year' => 365,
                default => 30
            };

            $stats = [
                'totalRevenue' => PaymentTransaction::where('status', 'completed')->sum('amount'),
                'revenueGrowth' => $this->calculatePaymentGrowth('payment_transactions', 'amount', $days),
                'totalTransactions' => PaymentTransaction::count(),
                'transactionsGrowth' => $this->calculatePaymentGrowth('payment_transactions', null, $days),
                'ticketsSold' => PaymentTransaction::where('status', 'completed')
                    ->whereHas('bookable', function($query) {
                        $query->where('bookable_type', 'App\Models\Ticket');
                    })->count(),
                'ticketsGrowth' => $this->calculatePaymentGrowth('payment_transactions', null, $days, 'completed'),
                'totalRefunds' => PaymentTransaction::where('status', 'refunded')->sum('amount'),
                'refundsGrowth' => $this->calculatePaymentGrowth('payment_transactions', 'amount', $days, 'refunded'),
            ];

            return response()->json($stats);
        } catch (\Exception $e) {
            Log::error('Error fetching payment stats: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to fetch payment stats'], 500);
        }
    }

    /**
     * Process a refund
     */
    public function processRefund(Request $request, $transactionId): JsonResponse
    {
        try {
            $transaction = PaymentTransaction::findOrFail($transactionId);

            if ($transaction->status !== 'completed') {
                return response()->json(['error' => 'Transaction cannot be refunded'], 400);
            }

            $transaction->status = 'refunded';
            $transaction->refunded_at = now();
            $transaction->save();

            if ($transaction->bookable_type === VendorBooking::class) {
                $booking = $transaction->bookable;
                $booking->is_paid = false;
                $booking->status = 'cancelled';
                $booking->save();
            }

            return response()->json([
                'message' => 'Refund processed successfully',
                'transaction' => $transaction
            ]);
        } catch (\Exception $e) {
            Log::error('Error processing refund: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to process refund'], 500);
        }
    }

    /**
     * Download receipt
     */
    public function downloadReceipt(Request $request, $transactionId): JsonResponse
    {
        try {
            $transaction = PaymentTransaction::with(['paymentMethod', 'bookable'])->findOrFail($transactionId);

            // This would typically generate and return a PDF receipt
            // For now, return receipt data
            return response()->json([
                'receipt_data' => [
                    'transaction_id' => $transaction->transaction_id,
                    'amount' => $transaction->amount,
                    'status' => $transaction->status,
                    'payment_date' => $transaction->payment_date,
                    'payment_method' => $transaction->paymentMethod->name ?? 'N/A',
                    'booking_details' => $transaction->bookable
                ],
                'download_url' => route('payments.receipt.download', $transactionId)
            ]);
        } catch (\Exception $e) {
            Log::error('Error downloading receipt: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to download receipt'], 500);
        }
    }

    /**
     * Calculate payment growth
     */
    private function calculatePaymentGrowth(string $table, ?string $sumColumn = null, int $days = 30, ?string $status = null): float
    {
        $query = PaymentTransaction::query();

        if ($status) {
            $query->where('status', $status);
        }

        $currentPeriod = clone $query;
        $previousPeriod = clone $query;

        if ($sumColumn) {
            $current = $currentPeriod->whereBetween('created_at', [now()->subDays($days), now()])->sum($sumColumn);
            $previous = $previousPeriod->whereBetween('created_at', [now()->subDays($days * 2), now()->subDays($days)])->sum($sumColumn);
        } else {
            $current = $currentPeriod->whereBetween('created_at', [now()->subDays($days), now()])->count();
            $previous = $previousPeriod->whereBetween('created_at', [now()->subDays($days * 2), now()->subDays($days)])->count();
        }

        if ($previous == 0) {
            return $current > 0 ? 100 : 0;
        }

        return round((($current - $previous) / $previous) * 100, 1);
    }
}
