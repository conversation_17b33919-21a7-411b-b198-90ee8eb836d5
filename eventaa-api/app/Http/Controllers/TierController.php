<?php

namespace App\Http\Controllers;

use App\Models\Tier;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class TierController extends Controller
{
    public function create(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string',
            'price' => 'required|integer',
            'seat' => 'required|string',
            'description' => 'required|string',
            'is_refundable' => 'required|boolean',
            'event_id' => 'required|integer',
            'banner' => 'required|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'errors' => $validator->errors()
            ], 422);
        }

        $exists = Tier::where('event_id', $request->event_id)
            ->where('name', $request->name)
            ->where('price', $request->price)
            ->exists();

        if ($exists) {
            return response()->json([
                'message' => 'A package with the same event name, and price already exists!'
            ], 409);
        }

        $coverImage = 'default_banner.png';

        if ($request->file('banner')) {
            $image = $request->file('banner');
            $coverImage = time() . '.' . $image->getClientOriginalExtension();
            $image->move(public_path('storage/banners'), $coverImage);
        }

        Tier::create([
            'event_id' => $request->event_id,
            'name' => $request->name,
            'price' => $request->price,
            'seat' => $request->seat,
            'banner' => $coverImage,
            'description' => $request->description,
            'is_refundable' => $request->is_refundable
        ]);

        return response()->json([
            'message' => 'Ticket tier created successfully!'
        ], 201);
    }


    public function update(Request $request, $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'string',
            'price' => 'integer',
            'event_id' => 'integer'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'errors' => $validator->errors()
            ], 422);
        }

        $tier = Tier::find($id);

        if (!$tier) {
            return response()->json([
                'message' => 'Tier not found!'
            ], 404);
        }

        $tier->update($request->all());

        return response()->json([
            'message' => 'Ticket tier updated successfully!'
        ], 200);
    }

    public function delete($id): JsonResponse
    {
        $tier = Tier::find($id);

        if (!$tier) {
            return response()->json([
                'message' => 'Tier not found!'
            ], 404);
        }

        $tier->delete();

        return response()->json([
            'message' => 'Ticket tier deleted successfully!'
        ], 200);
    }

    public function read(Request $request, $event_id): JsonResponse
    {

        $tiers = Tier::where('event_id', $event_id)->get();
        return response()->json([
            'tiers' => $tiers
        ]);
    }
}
