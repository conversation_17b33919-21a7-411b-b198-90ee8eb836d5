<?php

namespace App\Http\Controllers;

use App\Models\Event;
use App\Models\Ticket;
use App\Models\TicketPurchase;
use App\Models\User;
use App\Models\UserTicket;
use App\Models\Payment;
use App\Models\PaymentTransaction;
use App\Notifications\UserNotification;
use App\Services\TicketService;
use App\Services\PayChanguService;
use App\Jobs\ProcessTicketPurchase;
use App\Jobs\SendTicketConfirmationEmail;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Storage;

class TicketController extends Controller
{
    /**
     * Get available tickets for an event
     */
    public function getEventTickets(Request $request, $eventId): JsonResponse
    {
        $event = Event::with(['tiers' => function($query) {
            $query->orderBy('price', 'asc');
        }])->find($eventId);

        if (!$event) {
            return response()->json(['message' => 'Event not found'], 404);
        }

        $tickets = Ticket::with('tier')
            ->where('event_id', $eventId)
            ->available()
            ->get()
            ->map(function ($ticket) {
                return [
                    'id' => $ticket->id,
                    'name' => $ticket->name,
                    'price' => $ticket->price,
                    'description' => $ticket->description,
                    'banner' => $ticket->banner,
                    'quantity_available' => $ticket->quantity_available,
                    'quantity_sold' => $ticket->quantity_sold,
                    'remaining_quantity' => $ticket->remaining_quantity,
                    'sale_start_date' => $ticket->sale_start_date,
                    'sale_end_date' => $ticket->sale_end_date,
                    'is_refundable' => $ticket->is_refundable,
                    'refund_fee_percentage' => $ticket->refund_fee_percentage,
                    'tier' => $ticket->tier,
                    'is_available' => $ticket->isAvailable(),
                ];
            });

        return response()->json([
            'message' => 'Tickets retrieved successfully',
            'data' => [
                'event' => $event,
                'tickets' => $tickets
            ]
        ]);
    }

    /**
     * Purchase tickets
     */
    public function purchaseTickets(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'event_id' => 'required|integer|exists:events,id',
            'tickets' => 'required|array|min:1',
            'tickets.*.ticket_id' => 'required|integer|exists:tickets,id',
            'tickets.*.quantity' => 'required|integer|min:1|max:10',
            'attendee_details' => 'sometimes|array',
            'attendee_details.*.name' => 'required_with:attendee_details|string|max:255',
            'attendee_details.*.email' => 'required_with:attendee_details|email|max:255',
            'attendee_details.*.phone' => 'sometimes|string|max:20',
            'payment_method' => 'required|string|in:paychangu,stripe,paypal'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = $request->user();
        $event = Event::findOrFail($request->event_id);

        // Check if event is accepting ticket purchases
        if ($event->status !== 'published') {
            return response()->json(['message' => 'Event is not available for ticket purchase'], 400);
        }

        return DB::transaction(function () use ($request, $user, $event) {
            $totalAmount = 0;
            $purchaseData = [];
            $ticketIds = [];

            // Validate and calculate total amount
            foreach ($request->tickets as $index => $ticketData) {
                $ticket = Ticket::with('tier')->findOrFail($ticketData['ticket_id']);

                // Verify ticket belongs to the event
                if ($ticket->event_id !== $event->id) {
                    throw new \Exception("Ticket {$ticket->id} does not belong to event {$event->id}");
                }

                // Check availability
                if (!$ticket->isAvailable()) {
                    throw new \Exception("Ticket '{$ticket->name}' is not available for purchase");
                }

                // Check quantity availability
                if ($ticket->remaining_quantity < $ticketData['quantity']) {
                    throw new \Exception("Only {$ticket->remaining_quantity} tickets remaining for '{$ticket->name}'");
                }

                $subtotal = $ticket->price * $ticketData['quantity'];
                $totalAmount += $subtotal;

                $attendeeDetails = $request->attendee_details[$index] ?? [
                    'name' => $user->name,
                    'email' => $user->email,
                    'phone' => $user->phone ?? null
                ];

                $purchaseData[] = [
                    'ticket' => $ticket,
                    'quantity' => $ticketData['quantity'],
                    'unit_price' => $ticket->price,
                    'subtotal' => $subtotal,
                    'attendee_details' => $attendeeDetails
                ];

                $ticketIds[] = $ticket->id;
            }

            // Calculate fees and taxes (you can customize this logic)
            $fees = $totalAmount * 0.03; // 3% service fee
            $taxes = $totalAmount * 0.05; // 5% tax
            $finalAmount = $totalAmount + $fees + $taxes;

            // Create purchase reference
            $purchaseReference = 'PUR-' . strtoupper(Str::random(12)) . '-' . time();

            // Create payment record
            $payment = Payment::create([
                'user_id' => $user->id,
                'amount' => $finalAmount,
                'status' => 'pending',
                'type' => 'ticket_purchase',
                'payment_method' => $request->payment_method,
                'transaction_id' => $purchaseReference,
                'metadata' => [
                    'event_id' => $event->id,
                    'ticket_ids' => $ticketIds,
                    'breakdown' => [
                        'subtotal' => $totalAmount,
                        'fees' => $fees,
                        'taxes' => $taxes,
                        'total' => $finalAmount
                    ]
                ]
            ]);

            // Create ticket purchase records
            $purchases = [];
            foreach ($purchaseData as $data) {
                $purchase = TicketPurchase::create([
                    'user_id' => $user->id,
                    'event_id' => $event->id,
                    'ticket_id' => $data['ticket']->id,
                    'payment_id' => $payment->id,
                    'purchase_reference' => $purchaseReference,
                    'quantity' => $data['quantity'],
                    'unit_price' => $data['unit_price'],
                    'total_amount' => $data['subtotal'],
                    'fees' => ($fees * $data['subtotal']) / $totalAmount, // Proportional fees
                    'taxes' => ($taxes * $data['subtotal']) / $totalAmount, // Proportional taxes
                    'status' => 'pending',
                    'attendee_name' => $data['attendee_details']['name'],
                    'attendee_email' => $data['attendee_details']['email'],
                    'attendee_phone' => $data['attendee_details']['phone'] ?? null,
                    'attendee_details' => $data['attendee_details']
                ]);

                $purchases[] = $purchase;

                // Reserve tickets (reduce available quantity)
                $data['ticket']->increment('quantity_sold', $data['quantity']);
            }

            // Process payment based on method
            $paymentResult = $this->processPayment($payment, $request->payment_method);

            if (!$paymentResult['success']) {
                // Rollback ticket reservations
                foreach ($purchaseData as $data) {
                    $data['ticket']->decrement('quantity_sold', $data['quantity']);
                }

                throw new \Exception($paymentResult['message']);
            }

            return response()->json([
                'message' => 'Ticket purchase initiated successfully',
                'data' => [
                    'purchase_reference' => $purchaseReference,
                    'payment' => $payment,
                    'purchases' => $purchases,
                    'payment_details' => $paymentResult['data'] ?? null,
                    'total_amount' => $finalAmount,
                    'breakdown' => [
                        'subtotal' => $totalAmount,
                        'fees' => $fees,
                        'taxes' => $taxes,
                        'total' => $finalAmount
                    ]
                ]
            ]);
        });
    }

    /**
     * Confirm ticket purchase after payment
     */
    public function confirmPurchase(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'purchase_reference' => 'required|string|exists:ticket_purchases,purchase_reference',
            'payment_confirmation' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        return DB::transaction(function () use ($request) {
            $purchases = TicketPurchase::with(['ticket', 'event', 'payment', 'user'])
                ->where('purchase_reference', $request->purchase_reference)
                ->where('user_id', $request->user()->id)
                ->get();

            if ($purchases->isEmpty()) {
                return response()->json(['message' => 'Purchase not found'], 404);
            }

            $firstPurchase = $purchases->first();
            $payment = Payment::find($firstPurchase->payment_id);
            $user = User::find($firstPurchase->user_id);

            // Verify payment with payment gateway
            $verified = $this->verifyPayment($payment, $request->payment_confirmation);

            if (!$verified) {
                return response()->json(['message' => 'Payment verification failed'], 400);
            }

            // Update payment status
            $payment->update(['status' => 'completed']);

            // Generate tickets and update purchase status
            $generatedTickets = [];
            foreach ($purchases as $purchase) {
                $purchase->update([
                    'status' => 'completed',
                    'purchased_at' => now()
                ]);

                // Generate individual ticket instances for each quantity
                for ($i = 0; $i < $purchase->quantity; $i++) {
                    $ticketUuid = Str::uuid()->toString();

                    $userTicket = UserTicket::create([
                        'user_id' => $purchase->user_id,
                        'ticket_id' => $purchase->ticket_id
                    ]);

                    // Generate QR code and ticket
                    $ticketService = new TicketService();
                    $qrCode = $ticketService->generateTicketImage($userTicket->id, $purchase->ticket->tier);

                    $generatedTickets[] = [
                        'user_ticket_id' => $userTicket->id,
                        'ticket_uuid' => $ticketUuid,
                        'qr_code' => $qrCode,
                        'attendee_name' => $purchase->attendee_name,
                        'ticket_name' => $purchase->ticket->name
                    ];
                }
            }

            // Send confirmation email
            $this->sendTicketConfirmationEmail($user, $purchases, $generatedTickets);

            return response()->json([
                'message' => 'Ticket purchase confirmed successfully',
                'data' => [
                    'purchase_reference' => $request->purchase_reference,
                    'tickets' => $generatedTickets,
                    'event' => $purchases->first()->event
                ]
            ]);
        });
    }

    /**
     * Get user's ticket purchases
     */
    public function getUserPurchases(Request $request): JsonResponse
    {
        $user = $request->user();

        $purchases = TicketPurchase::with(['event', 'ticket', 'payment'])
            ->where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return response()->json([
            'message' => 'User purchases retrieved successfully',
            'data' => $purchases
        ]);
    }

    /**
     * Request ticket refund
     */
    public function requestRefund(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'purchase_id' => 'required|integer|exists:ticket_purchases,id',
            'reason' => 'required|string|max:500'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $purchase = TicketPurchase::with(['ticket', 'event', 'payment'])
            ->where('id', $request->purchase_id)
            ->where('user_id', $request->user()->id)
            ->firstOrFail();

        if (!$purchase->canBeRefunded()) {
            return response()->json(['message' => 'This purchase cannot be refunded'], 400);
        }

        $refundAmount = $purchase->calculateRefundAmount();

        return DB::transaction(function () use ($purchase, $request, $refundAmount) {
            $purchase->update([
                'status' => 'refunded',
                'cancellation_reason' => $request->reason,
                'cancelled_at' => now(),
                'refund_amount' => $refundAmount,
                'refunded_at' => now()
            ]);

            // Return tickets to available pool
            $purchase->ticket->decrement('quantity_sold', $purchase->quantity);

            // Process actual refund through payment gateway
            $payment = Payment::find($purchase->payment_id);
            $this->processRefund($payment, $refundAmount);

            return response()->json([
                'message' => 'Refund processed successfully',
                'data' => [
                    'refund_amount' => $refundAmount,
                    'original_amount' => $purchase->total_amount,
                    'refund_fee' => $purchase->total_amount - $refundAmount
                ]
            ]);
        });
    }

    // ... [Previous methods remain the same: generate, scan, generateQRCodes] ...

    public function generate(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'event_id' => 'required|integer',
            'tickets' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = User::with('subscription.plan')->find($request->user()->id);

        $packages = json_decode($request->tickets);
        $ticketIds = [];

        foreach ($packages as $package) {
            for ($limit = 0; $limit < $package->quantity; $limit++) {
                $ticket_uuid = Str::uuid()->toString();
                $ticket = Ticket::create([
                    "event_id" => $request->event_id,
                    "uuid" => $ticket_uuid,
                    "name" => $package->tier->name,
                    "price" => $package->tier->price,
                    "banner" => $package->tier->banner,
                ]);
                $id = $this->generateQRCodes($ticket->id, $package->tier);
                array_push($ticketIds, $id);
            }
        }

        $data = [
            'greeting' => 'Hello ' . $user->name . '!',
            'title' => 'Event Ticket Generation',
            'line' => 'You have successfully generated tickets for your event.',
            'action' => 'Download Tickets PDF',
            'actionURL' => '/tickets/download'
        ];

        $ticketService = new TicketService();

        return response()->json(['message' => 'Tickets generated successfully', 'pdf' => $ticketService->generateTicketsPDF($request->event_id, $ticketIds)]);
    }

    public function scan(Request $request): JsonResponse
    {
        $ticketService = new TicketService();
        return
            $ticketService->verifyAndUpdate($request->ticketUUID)
            ? response()->json(['message' => 'Ticket scanned successfully'], 200)
            : response()->json(['message' => 'Ticket already used']);
    }

    public function generateQRCodes($ticketId, $tier)
    {
        $ticketService = new TicketService();
        $id = $ticketService->generateTicketImage($ticketId, $tier);
        return $id;
    }

    public function assignUserTickets(Request $request): JsonResponse
    {
        // This method is now deprecated in favor of the new purchase flow
        return response()->json([
            'message' => 'This method is deprecated. Please use the new purchase flow.',
            'redirect' => '/api/tickets/purchase'
        ], 410);
    }

    /**
     * Private helper methods
     */
    private function processPayment(Payment $payment, string $method): array
    {
        switch ($method) {
            case 'paychangu':
                return $this->processPayChanguPayment($payment);
            case 'stripe':
                return $this->processStripePayment($payment);
            case 'paypal':
                return $this->processPayPalPayment($payment);
            default:
                return ['success' => false, 'message' => 'Unsupported payment method'];
        }
    }

    private function processPayChanguPayment(Payment $payment): array
    {
        try {
            // Get payment gateway for PayChangu
            $gateway = \App\Models\PaymentGateway::where('slug', 'paychangu')->first();
            if (!$gateway) {
                throw new \Exception('PayChangu gateway not configured');
            }

            $payChanguService = new PayChanguService($gateway);

            $result = $payChanguService->initializePayment(
                (float) $payment->amount,
                'MWK', // Currency - you can make this configurable
                $payment->transaction_id,
                route('payments.verify'), // Callback URL
                $payment->metadata ?? []
            );

            return [
                'success' => true,
                'data' => $result
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    private function processStripePayment(Payment $payment): array
    {
        // Implement Stripe payment processing
        return ['success' => true, 'data' => ['payment_intent' => 'pi_test_123']];
    }

    private function processPayPalPayment(Payment $payment): array
    {
        // Implement PayPal payment processing
        return ['success' => true, 'data' => ['order_id' => 'ORDER_123']];
    }

    private function verifyPayment(Payment $payment, string $confirmation): bool
    {
        // Implement payment verification logic based on payment method
        switch ($payment->payment_method) {
            case 'paychangu':
                return $this->verifyPayChanguPayment($payment, $confirmation);
            case 'stripe':
                return $this->verifyStripePayment($payment, $confirmation);
            case 'paypal':
                return $this->verifyPayPalPayment($payment, $confirmation);
            default:
                return false;
        }
    }

    private function verifyPayChanguPayment(Payment $payment, string $confirmation): bool
    {
        // Implement PayChangu verification
        return true; // Placeholder
    }

    private function verifyStripePayment(Payment $payment, string $confirmation): bool
    {
        // Implement Stripe verification
        return true; // Placeholder
    }

    private function verifyPayPalPayment(Payment $payment, string $confirmation): bool
    {
        // Implement PayPal verification
        return true; // Placeholder
    }

    private function processRefund(Payment $payment, float $amount): void
    {
        // Implement refund processing based on payment method
        switch ($payment->payment_method) {
            case 'paychangu':
                $this->processPayChanguRefund($payment, $amount);
                break;
            case 'stripe':
                $this->processStripeRefund($payment, $amount);
                break;
            case 'paypal':
                $this->processPayPalRefund($payment, $amount);
                break;
        }
    }

    private function processPayChanguRefund(Payment $payment, float $amount): void
    {
        // Implement PayChangu refund
    }

    private function processStripeRefund(Payment $payment, float $amount): void
    {
        // Implement Stripe refund
    }

    private function processPayPalRefund(Payment $payment, float $amount): void
    {
        // Implement PayPal refund
    }

    private function sendTicketConfirmationEmail(User $user, $purchases, array $tickets): void
    {
        // Queue email job
        // SendTicketConfirmationEmail::dispatch($user, $purchases, $tickets);

        // For now, send notification
        $data = [
            'greeting' => 'Hello ' . $user->name . '!',
            'title' => 'Ticket Purchase Confirmed',
            'line' => 'Your ticket purchase has been confirmed. You can download your tickets from your account.',
            'action' => 'View Tickets',
            'actionURL' => '/tickets/my-tickets'
        ];

        $user->notify(new UserNotification($data));
    }
}
