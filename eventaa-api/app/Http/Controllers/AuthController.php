<?php

namespace App\Http\Controllers;

use App\Http\Traits\ProfileTrait;
use Carbon\Carbon;
use Illuminate\Auth\Events\PasswordReset;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Models\User;
use Illuminate\Auth\Events\Registered;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Laravel\Socialite\Facades\Socialite;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Facades\Storage;

class AuthController extends Controller
{
    use ProfileTrait;

    public function register(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required',
            'email' => 'string|required|unique:users',
            'password' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'errors' => $validator->errors()
            ], 422);
        }

        $password = Hash::make($request->password);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => $password,
            'avatar' => 'user.png'
        ]);

        $user->assignRole('user');
        $this->createProfile($user->id);

        event(new Registered($user));

        return response()->json([
            'message' => 'Your account has been created successfully',
            'token' => $user->createToken(env('APP_NAME', 'EventaaHub'))->plainTextToken,
            'user' => $user
        ]);
    }

    public function login(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => $validator->errors()
            ], 422);
        }

        $user = User::where("email", $request->email)->first();
        if (!$user || !Hash::check($request->password, $user->password)) {
            return response()->json([
                'email' => ['The provided credentials are incorrect.'],
            ], 422);
        }

        if ($user->two_factor_enabled) {
            $userAgent = $request->header('User-Agent');
            $ipAddress = $request->ip();

            $recentSession = DB::table('sessions')
                ->where('user_id', $user->id)
                ->where('ip_address', $ipAddress)
                ->where('user_agent', $userAgent)
                ->where('last_activity', '>', now()->subDays(30)->timestamp)
                ->first();

            if (!$recentSession) {
                return response()->json([
                    'message' => 'Two-factor authentication required',
                    'requires_2fa' => true,
                    'email' => $user->email
                ], 200);
            }
        }

        $roles = $user->getRoleNames();

        $userData = $user->toArray();
        $userData['roles'] = $roles;

        return response()->json([
            'message' => 'Login successful',
            'user' => $userData,
            'token' => $user->createToken(env('APP_NAME', 'EventaaHub'))->plainTextToken
        ], 200);
    }
    public function refreshToken(Request $request): JsonResponse
    {
        $user = $request->user();

        $user->tokens()->delete();

        $accessToken = $user->createToken(config('app.name', 'EventaaHub') . '-access')->plainTextToken;
        $refreshToken = $user->createToken(config('app.name', 'EventaaHub') . '-refresh')->plainTextToken;

        return response()->json(['refresh' => $refreshToken, 'access' => $accessToken]);
    }


    public function forgotPassword(Request $request): JsonResponse
    {
        $request->validate(['email' => 'required|email']);

        $status = Password::sendResetLink(
            $request->only('email')
        );

        return $status === Password::RESET_LINK_SENT
            ? response()->json(['message' => 'Reset password link has been sent to your email, please check your inbox or junk mail', 'status' => true])
            : response()->json(['message' => 'Could not send password reset link, please try again', 'status' => false]);
    }

    public function resetPassword(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'token' => 'required',
            'email' => 'required|email',
            'password' => 'required|min:8',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'errors' => $validator->errors(),
            ], 422);
        }

        $status = Password::reset(
            $request->only('email', 'password', 'password_confirmation', 'token'),
            function ($user, $password) {
                $user->forceFill([
                    'password' => bcrypt($password)
                ])->save();

                event(new PasswordReset($user));
            }
        );

        return $status === Password::PASSWORD_RESET
            ? response()->json(['message' => 'Password reset successfully', 'status' => true])
            : response()->json(['message' => 'Unable to reset password', 'status' => false]);
    }

    public function authResetPassword(Request $request) : JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'password' => 'required|min:8',
            'confirm_password' => 'required|same:password',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'errors' => $validator->errors(),
            ], 422);
        }

        $user = $request->user();
        $user->password = Hash::make($request->password);
        $user->save();

        return response()->json([
            'message' => 'Password reset successfully, please login with your new password',
            'status' => true
        ], 200);
    }

    public function handleGoogleCallback(Request $request): JsonResponse
    {

        if (!$request->session()) {
            return response()->json([
                'message' => 'Session error'
            ], 422);
        }

        if (!$request->has('code')) {
            return response()->json(['error' => 'Authorization code not found'], 400);
        }

        $user = Socialite::driver('google')->stateless()->user();


        $existingUser = User::where('google_id', $user->id)->first();

        if (!$existingUser) {
            $existingUser = User::create([
                'name' => $user->name,
                'email' => $user->email,
                'google_id' => $user->id,
                'password' => Hash::make($user->id)
            ]);
        }

        // Get user roles
        $roles = $existingUser->getRoleNames();

        // Add roles to user object for the response
        $userData = $existingUser->toArray();
        $userData['roles'] = $roles;

        return response()->json([
            'mesage' => 'Login successfull',
            'user' => $userData,
            'token' => $existingUser->createToken(env('APP_NAME', 'EventaaHub'))->plainTextToken
        ], 200);
    }

    public function googleLogin(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'username' => 'required|string',
            'email_verified' => 'required',
            'sub' => 'required|string',
            'picture' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'errors' => $validator->errors()
            ], 422);
        }

        $existingUser = User::where('google_id', $request->input('sub'))
            ->orWhere('email', $request->input('email'))
            ->first();

        if (!$existingUser) {
            $fileName = 'avatar.png';
            if ($request->input('picture')) {
                $imageContent = file_get_contents($request->input('picture'));
                $fileName = time() . '.png';
                Storage::disk('public')->put('avatars/' . $fileName, $imageContent);
            }
            $existingUser = User::create([
                'name' => $request->input('username'),
                'email' => $request->input('email'),
                'google_id' => $request->input('sub'),
                'password' => Hash::make($request->input('sub')),
                'avatar' => $fileName,
                'email_verified_at' => $request->input('email_verified') ? Carbon::now() : null,
            ]);
        }

        $token = $existingUser->createToken(env('APP_NAME', 'EventaaHub'))->plainTextToken;
        $roles = $existingUser->getRoleNames();

        $userData = $existingUser->toArray();
        $userData['roles'] = $roles;

        return response()->json([
            'message' => 'Login successful',
            'user' => $userData,
            'token' => $token
        ], 200);
    }

    public function redirectToGoogle()
    {
        return Socialite::driver('google')->setScopes(['openid', 'email'])->redirect();
    }

    public function deactivateUser(Request $request): JsonResponse
    {
        $user = $request->user();
        $user->deactivation_date = Carbon::now()->addDays(14);
        $user->save();

        return response()->json(['message' => 'User account scheduled for deactivation in 14 days'], 200);
    }

    public function resetPasswordRedirection(Request $request)
    {
        return view("reset-password");
    }

    public function logout(Request $request)
    {
        $user = $request->user();

        if ($user) {
            try {
                $user->currentAccessToken()->delete();
            } catch (\Exception $e) {
                Log::warning('Failed to delete access token during logout: ' . $e->getMessage());
            }
        }

        return response()->json(['message' => 'Logged out successfully'], 201);
    }
}
