<?php

namespace App\Http\Controllers;

use App\Models\Follower;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class FollowerController extends Controller
{
    public function create(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            "followee_id" => "required|integer",
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => $validator->errors(),
            ], 400);
        }

        Follower::create([
            'follower_id' => $request->user()->id,
            'followee_id' => $request->followee_id,
        ]);

        return response()->json([
            'message' => 'Following successful',
        ]);
    }

    public function read($followerId): JsonResponse
    {
        $follower = Follower::find($followerId);

        if (!$follower) {
            return response()->json([
                'error' => 'Follower not found',
            ], 404);
        }

        return response()->json($follower);
    }

    public function update(Request $request, $followerId): JsonResponse
    {
        $follower = Follower::find($followerId);

        if (!$follower) {
            return response()->json([
                'error' => 'Follower not found',
            ], 404);
        }

        $follower->update($request->all());

        return response()->json([
            'message' => 'Follower updated successfully',
        ]);
    }

    public function delete($followerId): JsonResponse
    {
        $follower = Follower::find($followerId);

        if (!$follower) {
            return response()->json([
                'error' => 'Follower not found',
            ], 404);
        }

        $follower->delete();

        return response()->json([
            'message' => 'Follower deleted successfully',
        ]);
    }
}
