<?php

namespace App\Http\Controllers;

use App\Models\Event;
use App\Models\Ticket;
use App\Models\TicketPurchase;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AttendeeController extends Controller
{
    /**
     * Get paginated attendees with filtering and search
     */
    public function index(Request $request): JsonResponse
    {
        $query = TicketPurchase::with([
            'user:id,name,email,avatar',
            'event:id,title,start,end',
            'ticket:id,name,price'
        ])->where('status', 'completed');

        if ($request->filled('event_id')) {
            $query->where('event_id', $request->event_id);
        }

        if ($request->filled('status')) {
            $statusMap = [
                'confirmed' => 'completed',
                'checked-in' => 'completed',
                'cancelled' => 'cancelled',
                'no-show' => 'completed'
            ];

            if (isset($statusMap[$request->status])) {
                $query->where('status', $statusMap[$request->status]);
            }
        }

        if ($request->filled('ticket_type')) {
            $query->whereHas('ticket', function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->ticket_type . '%');
            });
        }


        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('attendee_name', 'like', "%{$search}%")
                  ->orWhere('attendee_email', 'like', "%{$search}%")
                  ->orWhereHas('user', function ($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%");
                  });
            });
        }

        $page = $request->get('page', 1);
        $limit = $request->get('limit', 10);

        $attendees = $query->orderBy('purchased_at', 'desc')
                          ->paginate($limit, ['*'], 'page', $page);

        $transformedData = $attendees->getCollection()->map(function ($purchase) {
            return [
                'id' => $purchase->id,
                'name' => $purchase->attendee_name ?: $purchase->user->name,
                'email' => $purchase->attendee_email ?: $purchase->user->email,
                'avatar' => $purchase->user->avatar ?? null,
                'event' => [
                    'id' => $purchase->event->id,
                    'title' => $purchase->event->title,
                    'date' => $purchase->event->start,
                ],
                'ticket' => [
                    'type' => $purchase->ticket->name,
                    'price' => $purchase->unit_price,
                ],
                'purchaseDate' => $purchase->purchased_at,
                'status' => $this->determineAttendeeStatus($purchase),
            ];
        });

        return response()->json([
            'data' => $transformedData,
            'total' => $attendees->total(),
            'current_page' => $attendees->currentPage(),
            'per_page' => $attendees->perPage(),
            'last_page' => $attendees->lastPage(),
        ]);
    }

    /**
     * Create a new attendee
     */
    public function create(Request $request): JsonResponse
    {
        $request->validate([
            'event_id' => 'required|exists:events,id',
            'ticket_id' => 'required|exists:tickets,id',
            'attendee_name' => 'required|string|max:255',
            'attendee_email' => 'required|email|max:255',
            'attendee_phone' => 'nullable|string|max:20',
        ]);

        $ticket = Ticket::findOrFail($request->ticket_id);

        $purchase = TicketPurchase::create([
            'user_id' => auth()->id(),
            'event_id' => $request->event_id,
            'ticket_id' => $request->ticket_id,
            'purchase_reference' => 'MANUAL-' . strtoupper(uniqid()),
            'quantity' => 1,
            'unit_price' => $ticket->price,
            'total_amount' => $ticket->price,
            'status' => 'completed',
            'purchased_at' => now(),
            'attendee_name' => $request->attendee_name,
            'attendee_email' => $request->attendee_email,
            'attendee_phone' => $request->attendee_phone,
        ]);

        return response()->json([
            'message' => 'Attendee created successfully',
            'data' => $purchase->load(['user', 'event', 'ticket'])
        ], 201);
    }

    /**
     * Update an attendee
     */
    public function update(Request $request, $id): JsonResponse
    {
        $purchase = TicketPurchase::findOrFail($id);

        $request->validate([
            'attendee_name' => 'sometimes|string|max:255',
            'attendee_email' => 'sometimes|email|max:255',
            'attendee_phone' => 'sometimes|nullable|string|max:20',
            'status' => 'sometimes|in:completed,cancelled,refunded',
        ]);

        $purchase->update($request->only([
            'attendee_name',
            'attendee_email',
            'attendee_phone',
            'status'
        ]));

        return response()->json([
            'message' => 'Attendee updated successfully',
            'data' => $purchase->load(['user', 'event', 'ticket'])
        ]);
    }

    /**
     * Delete an attendee
     */
    public function destroy($id): JsonResponse
    {
        $purchase = TicketPurchase::findOrFail($id);
        $purchase->delete();

        return response()->json([
            'message' => 'Attendee deleted successfully'
        ]);
    }

    /**
     * Check in an attendee
     */
    public function checkIn($id): JsonResponse
    {
        $purchase = TicketPurchase::findOrFail($id);

        $metadata = $purchase->metadata ?? [];
        $metadata['checked_in'] = true;
        $metadata['checked_in_at'] = now()->toISOString();

        $purchase->update(['metadata' => $metadata]);

        return response()->json([
            'message' => 'Attendee checked in successfully',
            'data' => $purchase->load(['user', 'event', 'ticket'])
        ]);
    }

    /**
     * Undo check-in for an attendee
     */
    public function undoCheckIn($id): JsonResponse
    {
        $purchase = TicketPurchase::findOrFail($id);

        $metadata = $purchase->metadata ?? [];
        $metadata['checked_in'] = false;
        unset($metadata['checked_in_at']);

        $purchase->update(['metadata' => $metadata]);

        return response()->json([
            'message' => 'Check-in undone successfully',
            'data' => $purchase->load(['user', 'event', 'ticket'])
        ]);
    }

    /**
     * Scan QR code for check-in
     */
    public function scanQR(Request $request): JsonResponse
    {
        $request->validate([
            'qr_code' => 'required|string',
            'event_id' => 'required|exists:events,id'
        ]);

        try {
            $qrData = json_decode($request->qr_code, true);

            if (!$qrData || !isset($qrData['attendee_id'])) {
                return response()->json([
                    'valid' => false,
                    'message' => 'Invalid QR code format'
                ]);
            }

            $purchase = TicketPurchase::where('id', $qrData['attendee_id'])
                                   ->where('event_id', $request->event_id)
                                   ->where('status', 'completed')
                                   ->first();

            if (!$purchase) {
                return response()->json([
                    'valid' => false,
                    'message' => 'Attendee not found or not registered for this event'
                ]);
            }

            $metadata = $purchase->metadata ?? [];
            if (isset($metadata['checked_in']) && $metadata['checked_in']) {
                return response()->json([
                    'valid' => false,
                    'message' => 'Attendee already checked in'
                ]);
            }

            $metadata['checked_in'] = true;
            $metadata['checked_in_at'] = now()->toISOString();
            $purchase->update(['metadata' => $metadata]);

            return response()->json([
                'valid' => true,
                'message' => 'Check-in successful',
                'attendee' => [
                    'id' => $purchase->id,
                    'name' => $purchase->attendee_name ?: $purchase->user->name,
                    'email' => $purchase->attendee_email ?: $purchase->user->email,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'valid' => false,
                'message' => 'Error processing QR code'
            ]);
        }
    }

    /**
     * Export attendees
     */
    public function export(Request $request)
    {
        $query = TicketPurchase::with([
            'user:id,name,email',
            'event:id,title,start',
            'ticket:id,name,price'
        ])->where('status', 'completed');

        if ($request->filled('event_id')) {
            $query->where('event_id', $request->event_id);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('attendee_name', 'like', "%{$search}%")
                  ->orWhere('attendee_email', 'like', "%{$search}%")
                  ->orWhereHas('user', function ($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%");
                  });
            });
        }

        $attendees = $query->orderBy('purchased_at', 'desc')->get();

        $csvData = [];
        $csvData[] = ['Name', 'Email', 'Event', 'Ticket Type', 'Price', 'Purchase Date', 'Status'];

        foreach ($attendees as $purchase) {
            $csvData[] = [
                $purchase->attendee_name ?: $purchase->user->name,
                $purchase->attendee_email ?: $purchase->user->email,
                $purchase->event->title,
                $purchase->ticket->name,
                $purchase->unit_price,
                $purchase->purchased_at->format('Y-m-d H:i:s'),
                $this->determineAttendeeStatus($purchase),
            ];
        }

        $filename = 'attendees-' . date('Y-m-d') . '.csv';
        $handle = fopen('php://temp', 'w+');

        foreach ($csvData as $row) {
            fputcsv($handle, $row);
        }

        rewind($handle);
        $csvContent = stream_get_contents($handle);
        fclose($handle);

        return response($csvContent)
            ->header('Content-Type', 'text/csv')
            ->header('Content-Disposition', 'attachment; filename="' . $filename . '"');
    }

    /**
     * Get attendee statistics
     */
    public function stats(Request $request): JsonResponse
    {
        $query = TicketPurchase::where('status', 'completed');

        if ($request->filled('event_id')) {
            $query->where('event_id', $request->event_id);
        }

        $totalAttendees = $query->count();
        $checkedInCount = $query->whereJsonContains('metadata->checked_in', true)->count();
        $noShowCount = $query->whereJsonContains('metadata->no_show', true)->count();
        $expectedCount = $totalAttendees - $checkedInCount - $noShowCount;
        $totalRevenue = $query->sum('total_amount');

        $checkInRate = $totalAttendees > 0 ? round(($checkedInCount / $totalAttendees) * 100, 1) : 0;

        return response()->json([
            'total_attendees' => $totalAttendees,
            'checked_in' => $checkedInCount,
            'expected' => $expectedCount,
            'no_show' => $noShowCount,
            'not_checked_in' => $totalAttendees - $checkedInCount,
            'check_in_rate' => $checkInRate,
            'total_revenue' => $totalRevenue,
        ]);
    }

    /**
     * Get available ticket types
     */
    public function ticketTypes(Request $request): JsonResponse
    {
        $query = Ticket::select('name')->distinct();

        if ($request->filled('event_id')) {
            $query->where('event_id', $request->event_id);
        }

        $ticketTypes = $query->pluck('name')->filter()->values();

        return response()->json([
            'ticket_types' => $ticketTypes
        ]);
    }

    /**
     * Mark attendee as no-show
     */
    public function markNoShow($id): JsonResponse
    {
        $purchase = TicketPurchase::findOrFail($id);

        $metadata = $purchase->metadata ?? [];
        $metadata['no_show'] = true;
        $metadata['no_show_at'] = now()->toISOString();

        $purchase->update(['metadata' => $metadata]);

        return response()->json([
            'message' => 'Attendee marked as no-show',
            'data' => $purchase->load(['user', 'event', 'ticket'])
        ]);
    }

    /**
     * Determine attendee status based on purchase data
     */
    private function determineAttendeeStatus($purchase): string
    {
        if ($purchase->status === 'cancelled') {
            return 'cancelled';
        }

        $metadata = $purchase->metadata ?? [];

        if (isset($metadata['checked_in']) && $metadata['checked_in']) {
            return 'checked-in';
        }

        if (isset($metadata['no_show']) && $metadata['no_show']) {
            return 'no-show';
        }

        return 'confirmed';
    }
}
