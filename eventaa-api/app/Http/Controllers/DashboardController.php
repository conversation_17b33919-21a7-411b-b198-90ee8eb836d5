<?php

namespace App\Http\Controllers;

use App\Events\DashboardStatsUpdated;
use App\Models\Event;
use App\Models\User;
use App\Models\Vendor;
use App\Models\Payment;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    /**
     * Get admin dashboard statistics
     */
    public function getAdminStats(): JsonResponse
    {
        try {
            $stats = [
                'totalRevenue' => Payment::sum('amount'),
                'grossSales' => Payment::where('status', 'completed')->sum('amount'),
                'revenueGrowth' => $this->calculateGrowth('payments', 'amount'),
                'ticketsSold' => DB::table('tickets')->where('status', 'sold')->count(),
                'ticketsTotal' => DB::table('tickets')->count(),
                'paidTickets' => DB::table('tickets')
                    ->join('ticket_tiers', 'tickets.id', '=', 'ticket_tiers.ticket_id')
                    ->join('tiers', 'ticket_tiers.tier_id', '=', 'tiers.id')
                    ->where('tickets.status', 'sold')
                    ->where('tiers.price', '>', 0)
                    ->count(),
                'freeTickets' => DB::table('tickets')
                    ->join('ticket_tiers', 'tickets.id', '=', 'ticket_tiers.ticket_id')
                    ->join('tiers', 'ticket_tiers.tier_id', '=', 'tiers.id')
                    ->where('tickets.status', 'sold')
                    ->where('tiers.price', 0)
                    ->count(),
                'ticketsGrowth' => $this->calculateGrowth('tickets', 'status', 'sold'),
                'pageViews' => DB::table('page_views')->count() ?? 0,
                'socialViews' => DB::table('page_views')->where('source', 'social')->count() ?? 0,
                'viewsGrowth' => $this->calculateGrowth('page_views') ?? 0,
                'activeUsers' => User::where('last_active_at', '>=', now()->subDays(7))->count(),
                'newUsers' => User::where('created_at', '>=', now()->subDays(7))->count(),
                'usersGrowth' => $this->calculateGrowth('users'),
                'adminStats' => [
                    'totalVendors' => Vendor::count(),
                    'pendingVendors' => Vendor::where('status', 'pending')->count(),
                    'vendorsGrowth' => $this->calculateGrowth('vendors'),
                    'totalVenues' => DB::table('venues')->count(),
                    'activeVenues' => DB::table('venues')
                        ->join('venue_bookings', 'venues.id', '=', 'venue_bookings.venue_id')
                        ->where('venue_bookings.booking_from', '>', now())
                        ->where('venue_bookings.status', 'confirmed')
                        ->distinct()
                        ->count('venues.id'),
                    'venuesGrowth' => $this->calculateGrowth('venues'),
                    'totalEvents' => Event::count(),
                    'upcomingEvents' => Event::where('start', '>', now())->count(),
                    'eventsGrowth' => $this->calculateGrowth('events'),
                    'platformRevenue' => Payment::where('type', 'platform_fee')->sum('amount'),
                    'subscriptions' => DB::table('subscriptions')
                        ->where(function ($query) {
                            $query->whereNull('end_date')
                                ->orWhere('end_date', '>', now());
                        })
                        ->where('start_date', '<=', now())
                        ->count(),
                    'revenueGrowth' => $this->calculateGrowth('payments', 'amount', null, 'platform_fee')
                ]
            ];

            try {
                if (config('broadcasting.default') !== 'null') {
                    broadcast(new DashboardStatsUpdated('admin', $stats))->toOthers();
                }
            } catch (\Exception $e) {
                \Log::error('Broadcasting error: ' . $e->getMessage());
            }

            return response()->json($stats);
        } catch (\Exception $e) {
            \Log::error('Error fetching admin stats: ' . $e->getMessage());

            return response()->json([
                'totalRevenue' => 0,
                'grossSales' => 0,
                'revenueGrowth' => 0,
                'ticketsSold' => 0,
                'ticketsTotal' => 0,
                'paidTickets' => 0,
                'freeTickets' => 0,
                'ticketsGrowth' => 0,
                'pageViews' => 0,
                'socialViews' => 0,
                'viewsGrowth' => 0,
                'activeUsers' => 0,
                'newUsers' => 0,
                'usersGrowth' => 0,
                'adminStats' => [
                    'totalVendors' => 0,
                    'pendingVendors' => 0,
                    'vendorsGrowth' => 0,
                    'totalVenues' => 0,
                    'activeVenues' => 0,
                    'venuesGrowth' => 0,
                    'totalEvents' => 0,
                    'upcomingEvents' => 0,
                    'eventsGrowth' => 0,
                    'platformRevenue' => 0,
                    'subscriptions' => 0,
                    'revenueGrowth' => 0
                ],
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get host dashboard statistics
     */
    public function getHostStats(Request $request): JsonResponse
    {
        try {
            $userId = $request->user()->id;

            $stats = [
                'totalRevenue' => Payment::where('user_id', $userId)->sum('amount'),
                'grossSales' => Payment::where('user_id', $userId)->where('status', 'completed')->sum('amount'),
                'revenueGrowth' => $this->calculateGrowth('payments', 'amount', null, null, ['user_id' => $userId]),
                'ticketsSold' => DB::table('tickets')
                    ->join('events', 'tickets.event_id', '=', 'events.id')
                    ->where('events.user_id', $userId)
                    ->where('tickets.status', 'sold')
                    ->count(),
                'ticketsTotal' => DB::table('tickets')
                    ->join('events', 'tickets.event_id', '=', 'events.id')
                    ->where('events.user_id', $userId)
                    ->count(),
                'paidTickets' => DB::table('tickets')
                    ->join('events', 'tickets.event_id', '=', 'events.id')
                    ->join('ticket_tiers', 'tickets.id', '=', 'ticket_tiers.ticket_id')
                    ->join('tiers', 'ticket_tiers.tier_id', '=', 'tiers.id')
                    ->where('events.user_id', $userId)
                    ->where('tickets.status', 'sold')
                    ->where('tiers.price', '>', 0)
                    ->count(),
                'freeTickets' => DB::table('tickets')
                    ->join('events', 'tickets.event_id', '=', 'events.id')
                    ->join('ticket_tiers', 'tickets.id', '=', 'ticket_tiers.ticket_id')
                    ->join('tiers', 'ticket_tiers.tier_id', '=', 'tiers.id')
                    ->where('events.user_id', $userId)
                    ->where('tickets.status', 'sold')
                    ->where('tiers.price', 0)
                    ->count(),
                'ticketsGrowth' => $this->calculateGrowth('tickets', 'status', 'sold', null, ['user_id' => $userId]),
                'pageViews' => DB::table('page_views')
                    ->join('events', 'page_views.event_id', '=', 'events.id')
                    ->where('events.user_id', $userId)
                    ->count() ?? 0,
                'socialViews' => DB::table('page_views')
                    ->join('events', 'page_views.event_id', '=', 'events.id')
                    ->where('events.user_id', $userId)
                    ->where('page_views.source', 'social')
                    ->count() ?? 0,
                'viewsGrowth' => $this->calculateGrowth('page_views', null, null, null, ['user_id' => $userId]) ?? 0,
                'activeUsers' => DB::table('event_attendees')
                    ->join('events', 'event_attendees.event_id', '=', 'events.id')
                    ->where('events.user_id', $userId)
                    ->where('event_attendees.last_active_at', '>=', now()->subDays(7))
                    ->count(),
                'newUsers' => DB::table('event_attendees')
                    ->join('events', 'event_attendees.event_id', '=', 'events.id')
                    ->where('events.user_id', $userId)
                    ->where('event_attendees.created_at', '>=', now()->subDays(7))
                    ->count(),
                'usersGrowth' => $this->calculateGrowth('event_attendees', null, null, null, ['user_id' => $userId])
            ];

            try {
                if (config('broadcasting.default') !== 'null') {
                    broadcast(new DashboardStatsUpdated('host', $stats, $userId))->toOthers();
                }
            } catch (\Exception $e) {
                \Log::error('Broadcasting error: ' . $e->getMessage());
            }

            return response()->json($stats);
        } catch (\Exception $e) {
            \Log::error('Error fetching host stats: ' . $e->getMessage());
            return response()->json([
                'totalRevenue' => 0,
                'grossSales' => 0,
                'revenueGrowth' => 0,
                'ticketsSold' => 0,
                'ticketsTotal' => 0,
                'paidTickets' => 0,
                'freeTickets' => 0,
                'ticketsGrowth' => 0,
                'pageViews' => 0,
                'socialViews' => 0,
                'viewsGrowth' => 0,
                'activeUsers' => 0,
                'newUsers' => 0,
                'usersGrowth' => 0,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get admin activities
     */
    public function getAdminActivities(): JsonResponse
    {
        $activities = DB::table('activities')
            ->select('users.name as user', 'activities.action', 'activities.resource', 'activities.created_at as date', 'activities.status')
            ->join('users', 'activities.user_id', '=', 'users.id')
            ->orderBy('activities.created_at', 'desc')
            ->limit(10)
            ->get();

        return response()->json($activities);
    }

    /**
     * Get host activities
     */
    public function getHostActivities(Request $request): JsonResponse
    {
        $userId = $request->user()->id;

        $activities = DB::table('tickets')
            ->select(
                'users.name as user',
                'ticket_types.name as action',
                'tickets.price',
                'tickets.created_at as date',
                'tickets.status'
            )
            ->join('users', 'tickets.user_id', '=', 'users.id')
            ->join('ticket_types', 'tickets.ticket_type_id', '=', 'ticket_types.id')
            ->join('events', 'ticket_types.event_id', '=', 'events.id')
            ->where('events.user_id', $userId)
            ->orderBy('tickets.created_at', 'desc')
            ->limit(10)
            ->get();

        return response()->json($activities);
    }

    /**
     * Get admin revenue chart data
     */
    public function getAdminRevenueChart(Request $request): JsonResponse
    {
        $period = $request->input('period', '30days');
        $data = $this->getChartData('payments', 'amount', $period, null, 'platform_fee');
        return response()->json($data);
    }

    /**
     * Get admin user growth chart data
     */
    public function getAdminUserGrowthChart(Request $request): JsonResponse
    {
        $period = $request->input('period', '30days');
        $data = $this->getChartData('users', null, $period);
        return response()->json($data);
    }

    /**
     * Get host revenue chart data
     */
    public function getHostRevenueChart(Request $request): JsonResponse
    {
        $period = $request->input('period', '30days');
        $userId = $request->user()->id;
        $data = $this->getChartData('payments', 'amount', $period, null, null, ['user_id' => $userId]);
        return response()->json($data);
    }

    /**
     * Get host ticket sales chart data
     */
    public function getHostTicketSalesChart(Request $request): JsonResponse
    {
        $period = $request->input('period', '30days');
        $userId = $request->user()->id;
        $data = $this->getChartData('tickets', null, $period, 'sold', null, ['user_id' => $userId]);
        return response()->json($data);
    }

    /**
     * Get analytics statistics
     */
    public function getAnalyticsStats(Request $request): JsonResponse
    {
        try {
            $period = $request->query('period', '30d');
            $days = match($period) {
                '7d' => 7,
                '30d' => 30,
                '90d' => 90,
                'year' => 365,
                default => 30
            };

            $stats = [
                'ticketsSold' => DB::table('tickets')->where('status', 'sold')->count(),
                'ticketsGrowth' => $this->calculateGrowth('tickets', null, 'sold'),
                'revenue' => Payment::where('status', 'completed')->sum('amount'),
                'revenueGrowth' => $this->calculateGrowth('payments', 'amount', 'completed'),
                'attendees' => DB::table('tickets')
                    ->join('ticket_tiers', 'tickets.id', '=', 'ticket_tiers.ticket_id')
                    ->where('tickets.status', 'sold')
                    ->count(),
                'attendeesGrowth' => $this->calculateGrowth('tickets', null, 'sold'),
                'pageViews' => DB::table('page_views')->count() ?? 0,
                'pageViewsGrowth' => $this->calculateGrowth('page_views') ?? 0,
            ];

            return response()->json($stats);
        } catch (\Exception $e) {
            \Log::error('Error fetching analytics stats: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to fetch analytics stats'], 500);
        }
    }

    /**
     * Get top performing events
     */
    public function getTopEvents(Request $request): JsonResponse
    {
        try {
            $period = $request->query('period', '30d');
            $days = match($period) {
                '7d' => 7,
                '30d' => 30,
                '90d' => 90,
                'year' => 365,
                default => 30
            };

            $events = Event::select([
                'events.*',
                DB::raw('COUNT(DISTINCT tickets.id) as tickets_sold'),
                DB::raw('COUNT(DISTINCT ticket_tiers.id) as total_tickets'),
                DB::raw('SUM(CASE WHEN payments.status = "completed" THEN payments.amount ELSE 0 END) as revenue'),
                DB::raw('(COUNT(DISTINCT tickets.id) * 100.0 / COUNT(DISTINCT ticket_tiers.id)) as conversion_rate')
            ])
            ->leftJoin('ticket_tiers', 'events.id', '=', 'ticket_tiers.event_id')
            ->leftJoin('tickets', function($join) {
                $join->on('ticket_tiers.id', '=', 'tickets.tier_id')
                     ->where('tickets.status', '=', 'sold');
            })
            ->leftJoin('payments', 'tickets.id', '=', 'payments.ticket_id')
            ->where('events.created_at', '>=', now()->subDays($days))
            ->groupBy('events.id')
            ->orderBy('tickets_sold', 'desc')
            ->limit(10)
            ->get();

            return response()->json($events);
        } catch (\Exception $e) {
            \Log::error('Error fetching top events: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to fetch top events'], 500);
        }
    }

    /**
     * Get revenue analytics
     */
    public function getRevenueAnalytics(Request $request): JsonResponse
    {
        try {
            $period = $request->query('period', '30d');
            $chartData = $this->getChartData('payments', 'amount', $period, 'completed');

            return response()->json([
                'chart_data' => $chartData,
                'total_revenue' => Payment::where('status', 'completed')->sum('amount'),
                'period' => $period
            ]);
        } catch (\Exception $e) {
            \Log::error('Error fetching revenue analytics: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to fetch revenue analytics'], 500);
        }
    }

    /**
     * Get attendees analytics
     */
    public function getAttendeesAnalytics(Request $request): JsonResponse
    {
        try {
            $period = $request->query('period', '30d');
            $chartData = $this->getChartData('tickets', null, $period, 'sold');

            return response()->json([
                'chart_data' => $chartData,
                'total_attendees' => DB::table('tickets')->where('status', 'sold')->count(),
                'period' => $period
            ]);
        } catch (\Exception $e) {
            \Log::error('Error fetching attendees analytics: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to fetch attendees analytics'], 500);
        }
    }

    /**
     * Get reports statistics
     */
    public function getReportsStats(Request $request): JsonResponse
    {
        try {
            $period = $request->query('period', '30d');
            $days = match($period) {
                '7d' => 7,
                '30d' => 30,
                '90d' => 90,
                'year' => 365,
                default => 30
            };

            $stats = [
                'totalRevenue' => Payment::where('status', 'completed')->sum('amount'),
                'revenueGrowth' => $this->calculateGrowth('payments', 'amount', 'completed'),
                'ticketsSold' => DB::table('tickets')->where('status', 'sold')->count(),
                'ticketsGrowth' => $this->calculateGrowth('tickets', null, 'sold'),
                'totalAttendees' => DB::table('tickets')->where('status', 'sold')->count(),
                'attendeesGrowth' => $this->calculateGrowth('tickets', null, 'sold'),
                'totalEvents' => Event::count(),
                'eventsGrowth' => $this->calculateGrowth('events'),
            ];

            return response()->json($stats);
        } catch (\Exception $e) {
            \Log::error('Error fetching reports stats: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to fetch reports stats'], 500);
        }
    }

    /**
     * Generate a report
     */
    public function generateReport(Request $request): JsonResponse
    {
        try {
            $type = $request->input('type', 'financial');
            $period = $request->input('period', '30d');
            $format = $request->input('format', 'json');

            $days = match($period) {
                '7d' => 7,
                '30d' => 30,
                '90d' => 90,
                'year' => 365,
                default => 30
            };

            $data = [];

            switch ($type) {
                case 'financial':
                    $data = [
                        'revenue' => Payment::where('status', 'completed')
                            ->where('created_at', '>=', now()->subDays($days))
                            ->sum('amount'),
                        'transactions' => Payment::where('created_at', '>=', now()->subDays($days))->count(),
                        'refunds' => Payment::where('status', 'refunded')
                            ->where('created_at', '>=', now()->subDays($days))
                            ->sum('amount'),
                    ];
                    break;
                case 'attendance':
                    $data = [
                        'tickets_sold' => DB::table('tickets')
                            ->where('status', 'sold')
                            ->where('created_at', '>=', now()->subDays($days))
                            ->count(),
                        'events_count' => Event::where('created_at', '>=', now()->subDays($days))->count(),
                    ];
                    break;
                case 'vendor':
                    $data = [
                        'total_vendors' => Vendor::count(),
                        'active_vendors' => Vendor::where('status', 'approved')->count(),
                        'pending_vendors' => Vendor::where('status', 'pending')->count(),
                    ];
                    break;
            }

            return response()->json([
                'type' => $type,
                'period' => $period,
                'data' => $data,
                'generated_at' => now()->toISOString()
            ]);
        } catch (\Exception $e) {
            \Log::error('Error generating report: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to generate report'], 500);
        }
    }

    /**
     * Export a report
     */
    public function exportReport(Request $request, $reportId): JsonResponse
    {
        try {
            // This would typically generate and return a file download
            // For now, return a success message
            return response()->json([
                'message' => 'Report export initiated',
                'report_id' => $reportId,
                'download_url' => route('reports.download', $reportId)
            ]);
        } catch (\Exception $e) {
            \Log::error('Error exporting report: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to export report'], 500);
        }
    }

    /**
     * Calculate growth percentage
     */
    private function calculateGrowth(
        string $table,
        ?string $sumColumn = null,
        ?string $whereValue = null,
        ?string $whereType = null,
        array $additionalWhere = []
    ): float {
        $query = DB::table($table);

        if (!empty($additionalWhere)) {
            foreach ($additionalWhere as $column => $value) {
                $query->where($column, $value);
            }
        }

        if ($whereValue) {
            $query->where('status', $whereValue);
        }

        if ($whereType) {
            $query->where('type', $whereType);
        }

        $currentPeriod = clone $query;
        $previousPeriod = clone $query;

        if ($sumColumn) {
            $current = $currentPeriod->whereBetween('created_at', [now()->subDays(30), now()])->sum($sumColumn);
            $previous = $previousPeriod->whereBetween('created_at', [now()->subDays(60), now()->subDays(30)])->sum($sumColumn);
        } else {
            $current = $currentPeriod->whereBetween('created_at', [now()->subDays(30), now()])->count();
            $previous = $previousPeriod->whereBetween('created_at', [now()->subDays(60), now()->subDays(30)])->count();
        }

        if ($previous == 0) {
            return $current > 0 ? 100 : 0;
        }

        return round((($current - $previous) / $previous) * 100, 1);
    }

    /**
     * Get chart data
     */
    private function getChartData(
        string $table,
        ?string $sumColumn = null,
        string $period = '30days',
        ?string $whereValue = null,
        ?string $whereType = null,
        array $additionalWhere = []
    ): array {
        $days = match($period) {
            '7days' => 7,
            '30days' => 30,
            '90days' => 90,
            'year' => 365,
            default => 30
        };

        $query = DB::table($table)
            ->select(
                DB::raw('DATE(created_at) as date'),
                DB::raw($sumColumn ? "SUM($sumColumn) as value" : 'COUNT(*) as value')
            )
            ->whereBetween('created_at', [now()->subDays($days), now()]);

        if (!empty($additionalWhere)) {
            foreach ($additionalWhere as $column => $value) {
                $query->where($column, $value);
            }
        }

        if ($whereValue) {
            $query->where('status', $whereValue);
        }

        if ($whereType) {
            $query->where('type', $whereType);
        }

        $data = $query->groupBy('date')
            ->orderBy('date')
            ->get();

        return [
            'labels' => $data->pluck('date'),
            'values' => $data->pluck('value')
        ];
    }
}
