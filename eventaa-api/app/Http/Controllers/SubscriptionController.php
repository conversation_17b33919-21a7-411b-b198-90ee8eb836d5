<?php

namespace App\Http\Controllers;

use App\Models\Plan;
use App\Models\Subscription;
use App\Models\PaymentTransaction;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class SubscriptionController extends Controller
{
    /**
     * Get all subscription plans
     */
    public function index(): JsonResponse
    {
        $plans = Plan::all();
        return response()->json($plans);
    }

    /**
     * Create a new subscription
     */
    public function create(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            "plan_id" => "required|exists:plans,id"
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $plan = Plan::findOrFail($request->plan_id);

        // Calculate end date based on plan duration
        $startDate = now();
        $endDate = null;

        $attributes = $plan->attributes;
        $duration = $attributes['duration'] ?? '1 month';

        if (strpos($duration, 'day') !== false) {
            $days = (int) $duration;
            $endDate = $startDate->copy()->addDays($days);
        } elseif (strpos($duration, 'month') !== false) {
            $months = (int) $duration;
            $endDate = $startDate->copy()->addMonths($months);
        } elseif (strpos($duration, 'year') !== false) {
            $years = (int) $duration;
            $endDate = $startDate->copy()->addYears($years);
        }

        // Create or update subscription
        $subscription = Subscription::updateOrCreate(
            ['user_id' => $request->user()->id],
            [
                'plan_id' => $request->plan_id,
                'start_date' => $startDate,
                'end_date' => $endDate
            ]
        );

        // Assign host role if not already assigned
        if (!$request->user()->hasRole('host')) {
            $request->user()->assignRole('host');
        }

        // Create a payment transaction record
        try {
            // Get the PayChangu gateway
            $paychangu = \App\Models\PaymentGateway::where('slug', 'paychangu')->first();

            if (!$paychangu) {
                throw new \Exception('PayChangu gateway not found');
            }

            // Get or create a payment method for this user
            $paymentMethod = null;

            if ($request->has('payment_method_id')) {
                // Use the provided payment method if it exists
                $paymentMethod = \App\Models\VendorPaymentMethod::findOrFail($request->payment_method_id);
            } else {
                // Find the default payment method for this user
                $vendor = $request->user()->vendor;

                if ($vendor) {
                    $paymentMethod = \App\Models\VendorPaymentMethod::where('vendor_id', $vendor->id)
                        ->where('payment_gateway_id', $paychangu->id)
                        ->where('is_default', true)
                        ->first();
                }

                // If no default method found, create one with Mpamba
                if (!$paymentMethod && $vendor) {
                    $paymentMethod = \App\Models\VendorPaymentMethod::create([
                        'vendor_id' => $vendor->id,
                        'payment_gateway_id' => $paychangu->id,
                        'is_default' => true,
                        'account_details' => [
                            'method' => 'mpamba',
                            'method_name' => 'TNM Mpamba',
                            'phone_number' => $request->user()->phone ?? '+265 999 123 456',
                            'account_name' => $request->user()->name
                        ],
                        'status' => 'active',
                        'last_used_at' => now()
                    ]);
                }
            }

            if ($paymentMethod) {
                PaymentTransaction::create([
                    'transaction_id' => 'sub_' . uniqid(),
                    'vendor_payment_method_id' => $paymentMethod->id,
                    'user_id' => $request->user()->id,
                    'amount' => $plan->price,
                    'currency' => $plan->currency ?? 'MWK',
                    'status' => 'completed',
                    'payment_type' => 'subscription',
                    'bookable_id' => $subscription->id,
                    'bookable_type' => Subscription::class,
                    'metadata' => [
                        'plan_name' => $plan->name,
                        'plan_id' => $plan->id,
                        'payment_method' => $paymentMethod->account_details['method'] ?? 'mpamba'
                    ],
                    'payment_date' => now(),
                    'gateway_response' => null
                ]);
            }
        } catch (\Exception $e) {
            // Log the error but don't fail the subscription creation
            \Illuminate\Support\Facades\Log::error('Failed to create payment transaction: ' . $e->getMessage());
        }

        return response()->json([
            'message' => 'Subscription was successful!',
            'data' => $subscription->load('plan')
        ], 200);
    }

    /**
     * Get user's current subscription
     */
    public function current(Request $request): JsonResponse
    {
        $subscription = $request->user()->subscription()->with('plan')->first();

        if (!$subscription) {
            return response()->json([
                'message' => 'No active subscription found',
                'data' => null
            ], 404);
        }

        return response()->json([
            'message' => 'Subscription retrieved successfully',
            'data' => $subscription
        ]);
    }

    /**
     * Change subscription plan
     */
    public function changePlan(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            "plan_id" => "required|exists:plans,id"
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $plan = Plan::findOrFail($request->plan_id);
        $user = $request->user();

        // Calculate end date based on plan duration
        $startDate = now();
        $endDate = null;

        $attributes = $plan->attributes;
        $duration = $attributes['duration'] ?? '1 month';

        if (strpos($duration, 'day') !== false) {
            $days = (int) $duration;
            $endDate = $startDate->copy()->addDays($days);
        } elseif (strpos($duration, 'month') !== false) {
            $months = (int) $duration;
            $endDate = $startDate->copy()->addMonths($months);
        } elseif (strpos($duration, 'year') !== false) {
            $years = (int) $duration;
            $endDate = $startDate->copy()->addYears($years);
        }

        // Create or update subscription
        $subscription = Subscription::updateOrCreate(
            ['user_id' => $user->id],
            [
                'plan_id' => $plan->id,
                'start_date' => $startDate,
                'end_date' => $endDate
            ]
        );

        // Create a payment transaction record
        try {
            // Get the PayChangu gateway
            $paychangu = \App\Models\PaymentGateway::where('slug', 'paychangu')->first();

            if (!$paychangu) {
                throw new \Exception('PayChangu gateway not found');
            }

            // Get or create a payment method for this user
            $paymentMethod = null;

            if ($request->has('payment_method_id')) {
                // Use the provided payment method if it exists
                $paymentMethod = \App\Models\VendorPaymentMethod::findOrFail($request->payment_method_id);
            } else {
                // Find the default payment method for this user
                $vendor = $user->vendor;

                if ($vendor) {
                    $paymentMethod = \App\Models\VendorPaymentMethod::where('vendor_id', $vendor->id)
                        ->where('payment_gateway_id', $paychangu->id)
                        ->where('is_default', true)
                        ->first();
                }

                // If no default method found, create one with Mpamba
                if (!$paymentMethod && $vendor) {
                    $paymentMethod = \App\Models\VendorPaymentMethod::create([
                        'vendor_id' => $vendor->id,
                        'payment_gateway_id' => $paychangu->id,
                        'is_default' => true,
                        'account_details' => [
                            'method' => 'mpamba',
                            'method_name' => 'TNM Mpamba',
                            'phone_number' => $user->phone ?? '+265 999 123 456',
                            'account_name' => $user->name
                        ],
                        'status' => 'active',
                        'last_used_at' => now()
                    ]);
                }
            }

            if ($paymentMethod) {
                PaymentTransaction::create([
                    'transaction_id' => 'sub_' . uniqid(),
                    'vendor_payment_method_id' => $paymentMethod->id,
                    'user_id' => $user->id,
                    'amount' => $plan->price,
                    'currency' => $plan->currency ?? 'MWK',
                    'status' => 'completed',
                    'payment_type' => 'subscription',
                    'bookable_id' => $subscription->id,
                    'bookable_type' => Subscription::class,
                    'metadata' => [
                        'plan_name' => $plan->name,
                        'plan_id' => $plan->id,
                        'payment_method' => $paymentMethod->account_details['method'] ?? 'mpamba'
                    ],
                    'payment_date' => now(),
                    'gateway_response' => null
                ]);
            }
        } catch (\Exception $e) {
            // Log the error but don't fail the subscription creation
            \Illuminate\Support\Facades\Log::error('Failed to create payment transaction: ' . $e->getMessage());
        }

        return response()->json([
            'message' => 'Subscription plan changed successfully',
            'data' => $subscription->load('plan')
        ]);
    }

    /**
     * Get user's subscription details
     */
    public function read(Request $request): JsonResponse
    {
        return response()->json([
            'subscription' => $request->user()->subscription()->with('plan')->first()
        ]);
    }
}
