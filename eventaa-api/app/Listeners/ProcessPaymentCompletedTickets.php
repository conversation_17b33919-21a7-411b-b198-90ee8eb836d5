<?php

namespace App\Listeners;

use App\Events\PaymentCompleted;
use App\Jobs\ProcessTicketPurchase;
use App\Models\TicketPurchase;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class ProcessPaymentCompletedTickets implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(PaymentCompleted $event): void
    {
        try {
            $payment = $event->payment;

            Log::info('Processing payment completed for tickets', [
                'payment_id' => $payment->id,
                'payment_type' => $payment->type,
                'payment_status' => $payment->status,
                'amount' => $payment->amount
            ]);

            // Only process if it's a ticket purchase payment
            if ($payment->type !== 'ticket_purchase') {
                Log::info('Payment is not for ticket purchase, skipping', [
                    'payment_id' => $payment->id,
                    'payment_type' => $payment->type
                ]);
                return;
            }

            // Find all ticket purchases associated with this payment
            $ticketPurchases = TicketPurchase::where('payment_id', $payment->id)
                ->where('status', 'pending')
                ->get();

            if ($ticketPurchases->isEmpty()) {
                Log::warning('No pending ticket purchases found for payment', [
                    'payment_id' => $payment->id
                ]);
                return;
            }

            foreach ($ticketPurchases as $purchase) {
                // Update ticket purchase status to completed
                $purchase->update([
                    'status' => 'completed',
                    'purchased_at' => now()
                ]);

                Log::info('Ticket purchase marked as completed', [
                    'purchase_id' => $purchase->id,
                    'payment_id' => $payment->id
                ]);

                // Dispatch job to process each ticket purchase
                ProcessTicketPurchase::dispatch($purchase, $payment->transaction_id ?? $payment->id);

                // Fire booking confirmed event
                event(new \App\Events\BookingConfirmed($purchase));
            }

            // Send payment confirmation email to user
            /** @var \App\Models\User $user */
            $user = $payment->user;
            if ($user) {
                $totalTickets = $ticketPurchases->sum('quantity');
                $eventTitles = $ticketPurchases->load('event')->pluck('event.title')->unique()->implode(', ');
                $formattedAmount = number_format((float) $payment->amount, 2);

                dispatch(new \App\Jobs\SendEmailNotification(
                    $user,
                    'Payment Confirmed - Your Tickets are Ready!',
                    "Your payment of {$payment->currency} {$formattedAmount} has been processed successfully. You have purchased {$totalTickets} ticket(s) for: {$eventTitles}",
                    null,
                    [
                        'payment' => $payment,
                        'ticket_purchases' => $ticketPurchases,
                        'total_tickets' => $totalTickets
                    ]
                ));
            }

            Log::info('Payment completed processing finished', [
                'payment_id' => $payment->id,
                'processed_purchases' => $ticketPurchases->count()
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to process payment completed tickets', [
                'payment_id' => $event->payment->id,
                'error' => $e->getMessage()
            ]);

            throw $e; // Rethrow to retry the job
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(PaymentCompleted $event, \Throwable $exception): void
    {
        Log::error('Payment completed processing failed', [
            'payment_id' => $event->payment->id,
            'error' => $exception->getMessage()
        ]);
    }
}
