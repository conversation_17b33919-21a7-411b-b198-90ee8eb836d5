<?php

namespace App\Services;

use App\Models\PaymentGateway;
use App\Models\PaymentTransaction;
use App\Models\VendorPaymentMethod;
use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class PayChanguService
{
    protected $baseUrl;
    protected $apiKey;
    protected $secretKey;
    protected $merchantId;
    protected $testMode;

    public function __construct(PaymentGateway $gateway = null)
    {
        if ($gateway) {
            $this->loadConfig($gateway);
        } else {
            $this->loadDefaultConfig();
        }
    }

    /**
     * Load configuration from a payment gateway model
     */
    public function loadConfig(PaymentGateway $gateway)
    {
        $config = $gateway->config;
        $this->testMode = $gateway->test_mode;

        $this->baseUrl = $this->testMode
            ? 'https://sandbox.paychangu.com/api/v1'
            : 'https://api.paychangu.com/api/v1';

        $this->apiKey = $config['api_key'] ?? '';
        $this->secretKey = $config['secret_key'] ?? '';
        $this->merchantId = $config['merchant_id'] ?? '';
    }

    /**
     * Load configuration from environment variables
     */
    public function loadDefaultConfig()
    {
        $this->testMode = env('PAYCHANGU_TEST_MODE', true);

        $this->baseUrl = $this->testMode
            ? 'https://sandbox.paychangu.com/api/v1'
            : 'https://api.paychangu.com/api/v1';

        $this->apiKey = env('PAYCHANGU_API_KEY', '');
        $this->secretKey = env('PAYCHANGU_SECRET_KEY', '');
        $this->merchantId = env('PAYCHANGU_MERCHANT_ID', '');
    }

    /**
     * Initialize a payment
     */
    public function initializePayment(
        float $amount,
        string $currency,
        string $reference,
        string $callbackUrl,
        array $metadata = []
    ) {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])->post($this->baseUrl . '/payments/initialize', [
                        'amount' => $amount,
                        'currency' => $currency,
                        'reference' => $reference,
                        'callback_url' => $callbackUrl,
                        'merchant_id' => $this->merchantId,
                        'metadata' => $metadata,
                    ]);

            if ($response->successful()) {
                return $response->json();
            }

            Log::error('PayChangu payment initialization failed', [
                'status' => $response->status(),
                'response' => $response->json(),
            ]);

            return [
                'status' => false,
                'message' => 'Payment initialization failed',
                'data' => $response->json(),
            ];
        } catch (Exception $e) {
            Log::error('PayChangu payment initialization exception', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'status' => false,
                'message' => 'Payment service error: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Verify a payment
     */
    public function verifyPayment(string $reference)
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])->get($this->baseUrl . '/payments/verify/' . $reference);

            if ($response->successful()) {
                return $response->json();
            }

            Log::error('PayChangu payment verification failed', [
                'status' => $response->status(),
                'response' => $response->json(),
            ]);

            return [
                'status' => false,
                'message' => 'Payment verification failed',
                'data' => $response->json(),
            ];
        } catch (Exception $e) {
            Log::error('PayChangu payment verification exception', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'status' => false,
                'message' => 'Payment verification error: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Process a payment for a booking
     */
    public function processBookingPayment(
        $booking,
        VendorPaymentMethod $paymentMethod,
        $userId,
        $callbackUrl
    ) {
        $reference = 'TXN-' . Str::uuid();

        $result = $this->initializePayment(
            $booking->total_price,
            'MWK',
            $reference,
            $callbackUrl,
            [
                'booking_id' => $booking->id,
                'booking_type' => get_class($booking),
                'vendor_id' => $booking->vendor_id,
                'user_id' => $userId,
            ]
        );

        if (!empty($result['status']) && $result['status']) {
            PaymentTransaction::create([
                'transaction_id' => $reference,
                'vendor_payment_method_id' => $paymentMethod->id,
                'user_id' => $userId,
                'amount' => $booking->total_price,
                'currency' => 'MWK',
                'status' => 'pending',
                'payment_type' => 'booking',
                'bookable_id' => $booking->id,
                'bookable_type' => get_class($booking),
                'metadata' => [
                    'payment_url' => $result['data']['authorization_url'] ?? null,
                ],
                'gateway_response' => $result,
            ]);

            return [
                'status' => true,
                'message' => 'Payment initialized successfully',
                'data' => [
                    'reference' => $reference,
                    'payment_url' => $result['data']['authorization_url'] ?? null,
                ],
            ];
        }

        return [
            'status' => false,
            'message' => $result['message'] ?? 'Payment initialization failed',
            'data' => $result,
        ];
    }
}
