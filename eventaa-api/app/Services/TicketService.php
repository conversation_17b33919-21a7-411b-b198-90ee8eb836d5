<?php

namespace App\Services;

use App\Models\Ticket;
use App\Models\Event;
use App\Models\Tier;
use chillerlan\QRCode\QRCode;
use Exception;
use Intervention\Image\Facades\Image;
use Illuminate\Support\Str;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Storage;

class TicketService
{
    public function generate($eventId): string
    {
        try {
            $randomNumber = mt_rand(100000, 999999);
            return 'eventaa-' . strval($eventId) . '-' . str_pad(strval($randomNumber), 6, '0', STR_PAD_LEFT);
        } catch (Exception $e) {
            return "";
        }
    }

    public function verifyAndUpdate($ticketUUID): bool
    {
        $ticket = Ticket::where('uuid', $ticketUUID)->first();

        if ($ticket) {
            $ticket->scanned = true;
            $ticket->scanned_at = now();
            $ticket->save();

            return true;
        }

        return false;
    }

    public function generateTicketImage($ticketId, $tier)
    {
        $ticket = Ticket::find($ticketId);
        if (!$ticket) {
            abort(404, 'Ticket not found');
        }

        if (!$tier) {
            dd($tier);
            abort(404, 'Tier not found');
        }


        $event = Event::find($tier->event_id);
        $tier = Tier::findOrFail($tier->id);

        $qrcode = new QRCode;
        $qrImageData = $qrcode->render($ticket->uuid);

        $ticketWidth = 400;
        $ticketHeight = 200;

        $coverArtPath = public_path('storage/banners/' . $tier->banner);
        $ticketImage = Image::make($coverArtPath)
            ->fit($ticketWidth, $ticketHeight);

        $qrCode = Image::make($qrImageData)->resize(60, 60);
        $ticketImage->insert($qrCode, 'bottom-right', 10, 10);
        $ticketImage->text($event->name, 20, 30, function ($font) {
            $font->file(public_path('fonts/MacanPanWeb-Regular.ttf'));
            $font->size(18);
            $font->color('#ffffff');
        });

        $imagePath = 'tickets/' . strtolower($ticket->uuid) . '-ticket.png';
        Storage::disk('public')->put($imagePath, $ticketImage->encode('png'));

        return $ticketId;
    }

    public function generateTicketsPDF($event_id, $ticketIds)
    {
        $ticketImages = [];
        $event = Event::findOrFail($event_id);

        foreach ($ticketIds as $ticketId) {
            $ticket = Ticket::find($ticketId);
            if ($ticket) {
                $imagePath = storage_path('app/public/tickets/' . strtolower($ticket->uuid) . '-ticket.png');
                if (file_exists($imagePath)) {
                    $imageData = base64_encode(file_get_contents($imagePath));
                    $ticketImages[] = 'data:image/png;base64,' . $imageData;
                }
            }
        }

        $pdf = Pdf::loadView('tickets', compact('ticketImages', 'event'));
        $pdfPath = 'tickets/event-' . $event_id . '-tickets.pdf';
        Storage::disk('public')->put($pdfPath, $pdf->output());
        return url(Storage::url($pdfPath));
    }

    /**
     * Generate a unique purchase reference
     */
    public function generatePurchaseReference(): string
    {
        return 'PUR-' . strtoupper(Str::random(12)) . '-' . time();
    }

    /**
     * Calculate total price with fees and taxes
     */
    public function calculateTotalPrice(array $ticketItems, float $feePercentage = 3.0, float $taxPercentage = 5.0): array
    {
        $subtotal = 0;

        foreach ($ticketItems as $item) {
            $subtotal += $item['price'] * $item['quantity'];
        }

        $fees = $subtotal * ($feePercentage / 100);
        $taxes = $subtotal * ($taxPercentage / 100);
        $total = $subtotal + $fees + $taxes;

        return [
            'subtotal' => round($subtotal, 2),
            'fees' => round($fees, 2),
            'taxes' => round($taxes, 2),
            'total' => round($total, 2),
            'fee_percentage' => $feePercentage,
            'tax_percentage' => $taxPercentage
        ];
    }

    /**
     * Validate ticket availability for purchase
     */
    public function validateTicketAvailability($ticketId, int $requestedQuantity): array
    {
        $ticket = Ticket::find($ticketId);

        if (!$ticket) {
            return [
                'valid' => false,
                'message' => 'Ticket not found'
            ];
        }

        if (!$ticket->isAvailable()) {
            return [
                'valid' => false,
                'message' => "Ticket '{$ticket->name}' is not available for purchase"
            ];
        }

        if ($ticket->remaining_quantity < $requestedQuantity) {
            return [
                'valid' => false,
                'message' => "Only {$ticket->remaining_quantity} tickets remaining for '{$ticket->name}'"
            ];
        }

        return [
            'valid' => true,
            'ticket' => $ticket
        ];
    }

    /**
     * Generate enhanced ticket with purchase details
     */
    public function generatePurchaseTicketImage($userTicketId, $purchaseDetails, $tier = null)
    {
        $userTicket = \App\Models\UserTicket::with(['ticket', 'user'])->find($userTicketId);
        if (!$userTicket) {
            abort(404, 'User ticket not found');
        }

        $ticket = $userTicket->ticket;
        $user = $userTicket->user;
        $event = $ticket->event;

        if (!$tier) {
            $tier = $ticket->tier;
        }

        $qrcode = new QRCode;
        $qrData = json_encode([
            'ticket_id' => $ticket->id,
            'user_ticket_id' => $userTicketId,
            'uuid' => $ticket->uuid,
            'user_id' => $user->id,
            'event_id' => $event->id,
            'purchase_ref' => $purchaseDetails['purchase_reference'] ?? null
        ]);
        $qrImageData = $qrcode->render($qrData);

        $ticketWidth = 600;
        $ticketHeight = 300;

        // Create ticket base image
        $ticketImage = Image::canvas($ticketWidth, $ticketHeight, '#ffffff');

        // Add event banner if available
        if ($tier && $tier->banner) {
            $coverArtPath = public_path('storage/banners/' . $tier->banner);
            if (file_exists($coverArtPath)) {
                $bannerImage = Image::make($coverArtPath)->fit(200, $ticketHeight);
                $ticketImage->insert($bannerImage, 'left');
            }
        }

        // Add QR code
        $qrCode = Image::make($qrImageData)->resize(80, 80);
        $ticketImage->insert($qrCode, 'bottom-right', 15, 15);

        // Add text information
        $textStartX = 220;
        $lineHeight = 25;
        $currentY = 30;

        // Event name
        $ticketImage->text($event->name, $textStartX, $currentY, function ($font) {
            $font->size(22);
            $font->color('#333333');
            $font->angle(0);
        });

        $currentY += $lineHeight + 10;

        // Ticket type
        $ticketImage->text('Ticket: ' . ($ticket->name ?? 'General'), $textStartX, $currentY, function ($font) {
            $font->size(16);
            $font->color('#666666');
        });

        $currentY += $lineHeight;

        // Attendee name
        $attendeeName = $purchaseDetails['attendee_name'] ?? $user->name;
        $ticketImage->text('Attendee: ' . $attendeeName, $textStartX, $currentY, function ($font) {
            $font->size(14);
            $font->color('#666666');
        });

        $currentY += $lineHeight;

        // Event date
        if ($event->start_date) {
            $ticketImage->text('Date: ' . $event->start_date->format('M d, Y H:i'), $textStartX, $currentY, function ($font) {
                $font->size(14);
                $font->color('#666666');
            });
            $currentY += $lineHeight;
        }

        // Ticket ID
        $ticketImage->text('Ticket ID: ' . $ticket->uuid, $textStartX, $currentY, function ($font) {
            $font->size(12);
            $font->color('#999999');
        });

        // Save the enhanced ticket
        $imagePath = 'tickets/purchase-' . $userTicketId . '-' . strtolower($ticket->uuid) . '.png';
        Storage::disk('public')->put($imagePath, $ticketImage->encode('png'));

        return $userTicketId;
    }

    /**
     * Generate PDF for purchased tickets
     */
    public function generatePurchaseTicketsPDF($purchaseReference, array $ticketData)
    {
        $ticketImages = [];
        $purchaseInfo = null;

        foreach ($ticketData as $data) {
            $imagePath = storage_path('app/public/tickets/purchase-' . $data['user_ticket_id'] . '-' . strtolower($data['ticket_uuid']) . '.png');
            if (file_exists($imagePath)) {
                $imageData = base64_encode(file_get_contents($imagePath));
                $ticketImages[] = [
                    'image' => 'data:image/png;base64,' . $imageData,
                    'attendee_name' => $data['attendee_name'],
                    'ticket_name' => $data['ticket_name']
                ];
            }

            if (!$purchaseInfo) {
                $purchaseInfo = $data;
            }
        }

        $pdf = Pdf::loadView('purchase-tickets', compact('ticketImages', 'purchaseReference', 'purchaseInfo'));
        $pdfPath = 'tickets/purchase-' . $purchaseReference . '-tickets.pdf';
        Storage::disk('public')->put($pdfPath, $pdf->output());

        return url(Storage::url($pdfPath));
    }
}
