<?php

namespace App\Providers;

use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        // Laravel default events
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],

        // Custom application events
        \App\Events\UserRegistered::class => [
            \App\Listeners\HandleUserRegistration::class,
        ],

        \App\Events\PaymentCompleted::class => [
            \App\Listeners\ProcessPaymentCompletedTickets::class,
        ],

        \App\Events\BookingConfirmed::class => [
            \App\Listeners\HandleBookingConfirmation::class,
        ],

        // Additional events you may want to handle
        \App\Events\MessageSent::class => [
            // Add listeners for message sent events
        ],

        \App\Events\NotificationCreated::class => [
            // Add listeners for notification events
        ],

        \App\Events\UserStatusUpdated::class => [
            // Add listeners for user status updates
        ],

        \App\Events\DashboardStatsUpdated::class => [
            // Add listeners for dashboard statistics
        ],

        \App\Events\VendorAnalyticsUpdated::class => [
            // Add listeners for vendor analytics
        ],
    ];

    /**
     * Register any events for your application.
     */
    public function boot(): void
    {
        //
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     */
    public function shouldDiscoverEvents(): bool
    {
        return false;
    }
}
