
# Eventaa API

## Installation Steps

1. **Clone the Eventaa API Repository**

    `git clone https://github.com/KellsWorks/eventaa-api.git`

2. **Navigate to the API Directory**

    `cd api`

3. **Install Dependencies using Composer**

    `composer install`

4. **Copy the Environment Configuration**

    `cp .env.example .env`

5. **Generate Application Key**

    `php artisan key:generate`

6. **Run Database Migrations**

    `php artisan migrate --seed`

7. **Start the Development Server**

    `php artisan serve`

8. **Access the API**

    The API should now be accessible at `http://localhost:8000`.

## Updating Database Credentials

1. **Open the `.env` File**

    Locate the `.env` file in the root directory of your Laravel project.

2. **Update Database Credentials**

    Find the following lines in the `.env` file:

    `DB_CONNECTION=mysql
    DB_HOST=127.0.0.1
    DB_PORT=3306
    DB_DATABASE=your_database_name
    DB_USERNAME=your_database_username
    DB_PASSWORD=your_database_password`

    Replace `your_database_name` with your actual database name, `your_database_username` with your database username, and `your_database_password` with your database password.

3. **Save Changes**

    Save the `.env` file after updating the database credentials.

4. **Run Migrations**

    If you've updated the `.env` file with a new database, run migrations again:

    `php artisan migrate --seed`

## With Docker

> Note: This is for development only and is running on mysql as the db

Make sure you have docker installed in your system
> Make is optional

### Step 1

Make sure to setup your .env value and add the db cred in the docker-compose.yml file db service environment as show below

```bash
 db:
        image: mysql:5.7.22
        container_name: db
        restart: unless-stopped
        tty: true
        ports:
            - "3306:3306"
        environment:
            MYSQL_DATABASE: eventaadb
            MYSQL_ROOT_PASSWORD: MAkc-2023
            SERVICE_TAGS: dev
            SERVICE_NAME: mysql
        volumes:
            - dbdata:/var/lib/mysql/
        networks:
            - app-network
```

### Step 2

if you have make installed in your system run

```bash
make Start
```

or

```bash
docker compose up -d
```

to start your docker containers

### Step 3

Install dependencies

```bash
make Install
```

### Step 4

Run migrations

```bash
make Migrate
```

> Note: you might run into an issue of permissions if so

Run

```bash
make Update-permissions
```

Your app then will be runniwng at port 9000.
